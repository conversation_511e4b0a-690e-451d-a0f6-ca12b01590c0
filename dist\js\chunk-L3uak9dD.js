import{r as t}from"./chunk-D3Ns84uO.js";import{r as e}from"./chunk-DJqUFuPP.js";var n,i,s={exports:{}},o={};var r=(i||(i=1,s.exports=function(){if(n)return o;n=1;var t=e(),i=Symbol.for("react.element"),s=Symbol.for("react.fragment"),r=Object.prototype.hasOwnProperty,a=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(t,e,n){var s,o={},u=null,h=null;for(s in void 0!==n&&(u=""+n),void 0!==e.key&&(u=""+e.key),void 0!==e.ref&&(h=e.ref),e)r.call(e,s)&&!l.hasOwnProperty(s)&&(o[s]=e[s]);if(t&&t.defaultProps)for(s in e=t.defaultProps)void 0===o[s]&&(o[s]=e[s]);return{$$typeof:i,type:t,key:u,ref:h,props:o,_owner:a.current}}return o.Fragment=s,o.jsx=u,o.jsxs=u,o}()),s.exports);const a=t.createContext({});function l(e){const n=t.useRef(null);return null===n.current&&(n.current=e()),n.current}const u="undefined"!=typeof window,h=u?t.useLayoutEffect:t.useEffect,c=t.createContext(null);function d(t,e){-1===t.indexOf(e)&&t.push(e)}function p(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const m=(t,e,n)=>n>e?e:n<t?t:n,f={},y=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function g(t){return"object"==typeof t&&null!==t}const v=t=>/^0[^.\s]+$/u.test(t);function x(t){let e;return()=>(void 0===e&&(e=t()),e)}const T=t=>t,w=(t,e)=>n=>e(t(n)),P=(...t)=>t.reduce(w),S=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class b{constructor(){this.subscriptions=[]}add(t){return d(this.subscriptions,t),()=>p(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const A=t=>1e3*t,E=t=>t/1e3;function V(t,e){return e?t*(1e3/e):0}const M=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function C(t,e,n,i){if(t===e&&n===i)return T;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=M(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:M(s(t),e,i)}const D=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,k=t=>e=>1-t(1-e),R=C(.33,1.53,.69,.99),L=k(R),j=D(L),B=t=>(t*=2)<1?.5*L(t):.5*(2-Math.pow(2,-10*(t-1))),F=t=>1-Math.sin(Math.acos(t)),O=k(F),I=D(F),U=C(.42,0,1,1),N=C(0,0,.58,1),W=C(.42,0,.58,1),$=t=>Array.isArray(t)&&"number"==typeof t[0],X={linear:T,easeIn:U,easeInOut:W,easeOut:N,circIn:F,circInOut:I,circOut:O,backIn:L,backInOut:j,backOut:R,anticipate:B},Y=t=>{if($(t)){const[e,n,i,s]=t;return C(e,n,i,s)}return"string"==typeof t?X[t]:t},z=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],K={value:null};function H(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=z.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(h.schedule(e),t()),l++,e(a)}const h={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&K.value&&K.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,h.process(t)))}};return h}(o,e?n:void 0),t)),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:c,preRender:d,render:p,postRender:m}=r,y=()=>{const o=f.useManualTiming?s.timestamp:performance.now();n=!1,f.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),h.process(s),c.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(y))};return{schedule:z.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(y)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<z.length;e++)r[z[e]].cancel(t)},state:s,steps:r}}const{schedule:_,cancel:q,state:G,steps:Z}=H("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:T,!0);let J;function Q(){J=void 0}const tt={now:()=>(void 0===J&&tt.set(G.isProcessing||f.useManualTiming?G.timestamp:performance.now()),J),set:t=>{J=t,queueMicrotask(Q)}},et=t=>e=>"string"==typeof e&&e.startsWith(t),nt=et("--"),it=et("var(--"),st=t=>!!it(t)&&ot.test(t.split("/*")[0].trim()),ot=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,rt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},at={...rt,transform:t=>m(0,1,t)},lt={...rt,default:1},ut=t=>Math.round(1e5*t)/1e5,ht=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const ct=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,dt=(t,e)=>n=>Boolean("string"==typeof n&&ct.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),pt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(ht);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},mt={...rt,transform:t=>Math.round((t=>m(0,255,t))(t))},ft={test:dt("rgb","red"),parse:pt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+mt.transform(t)+", "+mt.transform(e)+", "+mt.transform(n)+", "+ut(at.transform(i))+")"};const yt={test:dt("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:ft.transform},gt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),vt=gt("deg"),xt=gt("%"),Tt=gt("px"),wt=gt("vh"),Pt=gt("vw"),St=(()=>({...xt,parse:t=>xt.parse(t)/100,transform:t=>xt.transform(100*t)}))(),bt={test:dt("hsl","hue"),parse:pt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+xt.transform(ut(e))+", "+xt.transform(ut(n))+", "+ut(at.transform(i))+")"},At={test:t=>ft.test(t)||yt.test(t)||bt.test(t),parse:t=>ft.test(t)?ft.parse(t):bt.test(t)?bt.parse(t):yt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ft.transform(t):bt.transform(t)},Et=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Vt="number",Mt="color",Ct=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Dt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Ct,(t=>(At.test(t)?(i.color.push(o),s.push(Mt),n.push(At.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(Vt),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function kt(t){return Dt(t).values}function Rt(t){const{split:e,types:n}=Dt(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===Vt?ut(t[o]):e===Mt?At.transform(t[o]):t[o]}return s}}const Lt=t=>"number"==typeof t?0:t;const jt={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(ht))?void 0:e.length)||0)+((null==(n=t.match(Et))?void 0:n.length)||0)>0},parse:kt,createTransformer:Rt,getAnimatableNone:function(t){const e=kt(t);return Rt(t)(e.map(Lt))}};function Bt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Ft(t,e){return n=>n>0?e:t}const Ot=(t,e,n)=>t+(e-t)*n,It=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},Ut=[yt,ft,bt];function Nt(t){const e=(n=t,Ut.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===bt&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=Bt(a,i,t+1/3),o=Bt(a,i,t),r=Bt(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const Wt=(t,e)=>{const n=Nt(t),i=Nt(e);if(!n||!i)return Ft(t,e);const s={...n};return t=>(s.red=It(n.red,i.red,t),s.green=It(n.green,i.green,t),s.blue=It(n.blue,i.blue,t),s.alpha=Ot(n.alpha,i.alpha,t),ft.transform(s))},$t=new Set(["none","hidden"]);function Xt(t,e){return n=>Ot(t,e,n)}function Yt(t){return"number"==typeof t?Xt:"string"==typeof t?st(t)?Ft:At.test(t)?Wt:Ht:Array.isArray(t)?zt:"object"==typeof t?At.test(t)?Wt:Kt:Ft}function zt(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>Yt(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Kt(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=Yt(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const Ht=(t,e)=>{const n=jt.createTransformer(e),i=Dt(t),s=Dt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?$t.has(t)&&!s.values.length||$t.has(e)&&!i.values.length?function(t,e){return $t.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):P(zt(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):Ft(t,e)};function _t(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Ot(t,e,n);return Yt(t)(t,e)}const qt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>_.update(e,t),stop:()=>q(e),now:()=>G.isProcessing?G.timestamp:tt.now()}},Gt=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let o=0;o<s;o++)i+=t(o/(s-1))+", ";return`linear(${i.substring(0,i.length-2)})`},Zt=2e4;function Jt(t){let e=0;let n=t.next(e);for(;!n.done&&e<Zt;)e+=50,n=t.next(e);return e>=Zt?1/0:e}function Qt(t,e,n){const i=Math.max(e-5,0);return V(n-t(i),e-i)}const te=100,ee=10,ne=1,ie=0,se=800,oe=.3,re=.3,ae={granular:.01,default:2},le={granular:.005,default:.5},ue=.01,he=10,ce=.05,de=1;function pe({duration:t=se,bounce:e=oe,velocity:n=ie,mass:i=ne}){let s,o,r=1-e;r=m(ce,de,r),t=m(ue,he,E(t)),r<1?(s=e=>{const i=e*r,s=i*t;return.001-(i-n)/fe(e,r)*Math.exp(-s)},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=fe(Math.pow(e,2),r);return(.001-s(e)>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let s=1;s<me;s++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=A(t),isNaN(a))return{stiffness:te,damping:ee,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const me=12;function fe(t,e){return t*Math.sqrt(1-e*e)}const ye=["duration","bounce"],ge=["stiffness","damping","mass"];function ve(t,e){return e.some((e=>void 0!==t[e]))}function xe(t=re,e=oe){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:ie,stiffness:te,damping:ee,mass:ne,isResolvedFromDuration:!1,...t};if(!ve(t,ge)&&ve(t,ye))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*m(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:ne,stiffness:s,damping:o}}else{const n=pe(t);e={...e,...n,mass:ne},e.isResolvedFromDuration=!0}return e}({...n,velocity:-E(n.velocity||0)}),f=d||0,y=u/(2*Math.sqrt(l*h)),g=r-o,v=E(Math.sqrt(l/h)),x=Math.abs(g)<5;let T;if(i||(i=x?ae.granular:ae.default),s||(s=x?le.granular:le.default),y<1){const t=fe(v,y);T=e=>{const n=Math.exp(-y*v*e);return r-n*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===y)T=t=>r-Math.exp(-v*t)*(g+(f+v*g)*t);else{const t=v*Math.sqrt(y*y-1);T=e=>{const n=Math.exp(-y*v*e),i=Math.min(t*e,300);return r-n*((f+y*v*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&c||null,next:t=>{const e=T(t);if(p)a.done=t>=c;else{let n=0===t?f:0;y<1&&(n=0===t?A(f):Qt(T,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(Jt(w),Zt),e=Gt((e=>w.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Te({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:h}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=c+m,y=void 0===r?f:r(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/i),v=t=>y+g(t),x=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?y:n};let T,w;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(T=t,w=xe({keyframes:[d.value,p(d.value)],velocity:Qt(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==T||(e=!0,x(t),P(t)),void 0!==T&&t>=T?w.next(t-T):(!e&&x(t),d)}}}function we(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||f.mix||_t,o=t.length-1;for(let r=0;r<o;r++){let n=s(t[r],t[r+1]);if(e){const t=Array.isArray(e)?e[r]||T:e;n=P(t,n)}i.push(n)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=S(t[i],t[i+1],n);return a[i](s)};return n?e=>u(m(t[0],t[o-1],e)):u}function Pe(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=S(0,e,i);t.push(Ot(n,1,s))}}(e,t.length-1),e}function Se({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(Y):Y(i),o={done:!1,value:e[0]},r=function(t,e){return t.map((t=>t*e))}(n&&n.length===e.length?n:Pe(e),t),a=we(r,e,{ease:Array.isArray(s)?s:(l=e,u=s,l.map((()=>u||W)).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}xe.applyToOptions=t=>{const e=function(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(Jt(i),Zt);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:E(s)}}(t,100,xe);return t.ease=e.ease,t.duration=A(e.duration),t.type="keyframes",t};const be=t=>null!==t;function Ae(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(be),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const Ee={decay:Te,inertia:Te,tween:Se,keyframes:Se,spring:xe};function Ve(t){"string"==typeof t.type&&(t.type=Ee[t.type])}class Me{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ce=t=>t/100;class De extends Me{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{var e,n;if(t){const{motionValue:t}=this.options;t&&t.updatedAt!==tt.now()&&this.tick(tt.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null==(n=(e=this.options).onStop)||n.call(e))},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ve(t);const{type:e=Se,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Se;a!==Se&&"number"!=typeof r[0]&&(this.mixKeyframes=P(Ce,_t(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=Jt(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:p,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let x=this.currentTime,T=n;if(h){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,h+1);Boolean(e%2)&&("reverse"===c?(n=1-n,d&&(n-=d/r)):"mirror"===c&&(T=o)),x=m(0,1,n)*r}const w=v?{done:!1,value:u[0]}:T.next(x);s&&(w.value=s(w.value));let{done:P}=w;v||null===a||(P=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&p!==Te&&(w.value=Ae(u,this.options,y,this.speed)),f&&f(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return E(this.calculatedDuration)}get time(){return E(this.currentTime)}set time(t){var e;t=A(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),null==(e=this.driver)||e.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=E(this.currentTime))}play(){var t,e;if(this.isStopped)return;const{driver:n=qt,startTime:i}=this.options;this.driver||(this.driver=n((t=>this.tick(t)))),null==(e=(t=this.options).onPlay)||e.call(t);const s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=i??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,e;this.notifyFinished(),this.teardown(),this.state="finished",null==(e=(t=this.options).onComplete)||e.call(t)}cancel(){var t,e;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null==(e=(t=this.options).onCancel)||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var e;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null==(e=this.driver)||e.stop(),t.observe(this)}}const ke=t=>180*t/Math.PI,Re=t=>{const e=ke(Math.atan2(t[1],t[0]));return je(e)},Le={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Re,rotateZ:Re,skewX:t=>ke(Math.atan(t[1])),skewY:t=>ke(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},je=t=>((t%=360)<0&&(t+=360),t),Be=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Fe=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Oe={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Be,scaleY:Fe,scale:t=>(Be(t)+Fe(t))/2,rotateX:t=>je(ke(Math.atan2(t[6],t[5]))),rotateY:t=>je(ke(Math.atan2(-t[2],t[0]))),rotateZ:Re,rotate:Re,skewX:t=>ke(Math.atan(t[4])),skewY:t=>ke(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Ie(t){return t.includes("scale")?1:0}function Ue(t,e){if(!t||"none"===t)return Ie(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=Oe,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=Le,s=e}if(!s)return Ie(e);const o=i[e],r=s[1].split(",").map(Ne);return"function"==typeof o?o(r):r[o]}function Ne(t){return parseFloat(t.trim())}const We=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],$e=(()=>new Set(We))(),Xe=t=>t===rt||t===Tt,Ye=new Set(["x","y","z"]),ze=We.filter((t=>!Ye.has(t)));const Ke={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Ue(e,"x"),y:(t,{transform:e})=>Ue(e,"y")};Ke.translateX=Ke.x,Ke.translateY=Ke.y;const He=new Set;let _e=!1,qe=!1,Ge=!1;function Ze(){if(qe){const t=Array.from(He).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return ze.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{var i;null==(i=t.getValue(e))||i.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}qe=!1,_e=!1,He.forEach((t=>t.complete(Ge))),He.clear()}function Je(){He.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(qe=!0)}))}class Qe{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(He.add(this),_e||(_e=!0,_.read(Je),_.resolveKeyframes(Ze))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),He.delete(this)}cancel(){"scheduled"===this.state&&(He.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const tn=x((()=>void 0!==window.ScrollTimeline)),en={};function nn(t,e){const n=x(t);return()=>en[e]??n()}const sn=nn((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),on=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,rn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:on([0,.65,.55,1]),circOut:on([.55,0,1,.45]),backIn:on([.31,.01,.66,-.59]),backOut:on([.33,1.53,.69,.99])};function an(t,e){return t?"function"==typeof t?sn()?Gt(t,e):"ease-out":$(t)?on(t):Array.isArray(t)?t.map((t=>an(t,e)||rn.easeOut)):rn[t]:void 0}function ln(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const h={[e]:n};l&&(h.offset=l);const c=an(a,s);Array.isArray(c)&&(h.easing=c);const d={delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};u&&(d.pseudoElement=u);return t.animate(h,d)}function un(t){return"function"==typeof t&&"applyToOptions"in t}class hn extends Me{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t;const l=function({type:t,...e}){return un(t)&&sn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=ln(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=Ae(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}null==a||a(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,e;null==(e=(t=this.animation).finish)||e.call(t)}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var t,e;this.isPseudoElement||null==(e=(t=this.animation).commitStyles)||e.call(t)}get duration(){var t,e;const n=(null==(e=null==(t=this.animation.effect)?void 0:t.getComputedTiming)?void 0:e.call(t).duration)||0;return E(Number(n))}get time(){return E(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=A(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){var n;return this.allowFlatten&&(null==(n=this.animation.effect)||n.updateTiming({easing:"linear"})),this.animation.onfinish=null,t&&tn()?(this.animation.timeline=t,T):e(this)}}const cn={anticipate:B,backInOut:j,circInOut:I};function dn(t){"string"==typeof t.ease&&t.ease in cn&&(t.ease=cn[t.ease])}class pn extends hn{constructor(t){dn(t),Ve(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new De({...o,autoplay:!1}),a=A(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const mn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!jt.test(t)&&"0"!==t||t.startsWith("url(")));function fn(t){return g(t)&&"offsetHeight"in t}const yn=new Set(["opacity","clipPath","filter","transform"]),gn=x((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));class vn extends Me{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:u,...h}){var c;super(),this.stop=()=>{var t,e;this._animation&&(this._animation.stop(),null==(t=this.stopTimeline)||t.call(this)),null==(e=this.keyframeResolver)||e.cancel()},this.createdAt=tt.now();const d={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:u,...h},p=(null==u?void 0:u.KeyframeResolver)||Qe;this.keyframeResolver=new p(r,((t,e,n)=>this.onKeyframesResolved(t,e,d,!n)),a,l,u),null==(c=this.keyframeResolver)||c.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=tt.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=mn(s,e),a=mn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||un(n))&&i)}(t,s,o,r)||(!f.instantAnimations&&a||null==u||u(Ae(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const h={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},c=!l&&function(t){var e;const{motionValue:n,name:i,repeatDelay:s,repeatType:o,damping:r,type:a}=t;if(!fn(null==(e=null==n?void 0:n.owner)?void 0:e.current))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return gn()&&i&&yn.has(i)&&("transform"!==i||!u)&&!l&&!s&&"mirror"!==o&&0!==r&&"inertia"!==a}(h)?new pn({...h,element:h.motionValue.owner.current}):new De(h);c.finished.then((()=>this.notifyFinished())).catch(T),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){var t;return this._animation||(null==(t=this.keyframeResolver)||t.resume(),Ge=!0,Je(),Ze(),Ge=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),null==(t=this.keyframeResolver)||t.cancel()}}const xn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Tn(t,e,n=1){const[i,s]=function(t){const e=xn.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return y(t)?parseFloat(t):t}return st(s)?Tn(s,e,n+1):s}function wn(t,e){return(null==t?void 0:t[e])??(null==t?void 0:t.default)??t}const Pn=new Set(["width","height","top","left","right","bottom",...We]),Sn=t=>e=>e.test(t),bn=[rt,Tt,xt,vt,Pt,wt,{test:t=>"auto"===t,parse:t=>t}],An=t=>bn.find(Sn(t));const En=new Set(["brightness","contrast","saturate","opacity"]);function Vn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(ht)||[];if(!i)return t;const s=n.replace(i,"");let o=En.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Mn=/\b([a-z-]*)\(.*?\)/gu,Cn={...jt,getAnimatableNone:t=>{const e=t.match(Mn);return e?e.map(Vn).join(" "):t}},Dn={...rt,transform:Math.round},kn={borderWidth:Tt,borderTopWidth:Tt,borderRightWidth:Tt,borderBottomWidth:Tt,borderLeftWidth:Tt,borderRadius:Tt,radius:Tt,borderTopLeftRadius:Tt,borderTopRightRadius:Tt,borderBottomRightRadius:Tt,borderBottomLeftRadius:Tt,width:Tt,maxWidth:Tt,height:Tt,maxHeight:Tt,top:Tt,right:Tt,bottom:Tt,left:Tt,padding:Tt,paddingTop:Tt,paddingRight:Tt,paddingBottom:Tt,paddingLeft:Tt,margin:Tt,marginTop:Tt,marginRight:Tt,marginBottom:Tt,marginLeft:Tt,backgroundPositionX:Tt,backgroundPositionY:Tt,...{rotate:vt,rotateX:vt,rotateY:vt,rotateZ:vt,scale:lt,scaleX:lt,scaleY:lt,scaleZ:lt,skew:vt,skewX:vt,skewY:vt,distance:Tt,translateX:Tt,translateY:Tt,translateZ:Tt,x:Tt,y:Tt,z:Tt,perspective:Tt,transformPerspective:Tt,opacity:at,originX:St,originY:St,originZ:Tt},zIndex:Dn,fillOpacity:at,strokeOpacity:at,numOctaves:Dn},Rn={...kn,color:At,backgroundColor:At,outlineColor:At,fill:At,stroke:At,borderColor:At,borderTopColor:At,borderRightColor:At,borderBottomColor:At,borderLeftColor:At,filter:Cn,WebkitFilter:Cn},Ln=t=>Rn[t];function jn(t,e){let n=Ln(t);return n!==Cn&&(n=jt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Bn=new Set(["auto","none","0"]);class Fn extends Qe{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let n=t[a];if("string"==typeof n&&(n=n.trim(),st(n))){const i=Tn(n,e.current);void 0!==i&&(t[a]=i),a===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!Pn.has(n)||2!==t.length)return;const[i,s]=t,o=An(i),r=An(s);if(o!==r)if(Xe(o)&&Xe(r))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else Ke[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let s=0;s<t.length;s++)(null===t[s]||("number"==typeof(i=t[s])?0===i:null===i||"none"===i||"0"===i||v(i)))&&n.push(s);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Bn.has(e)&&Dt(e).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=jn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ke[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=Ke[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach((([t,n])=>{e.getValue(t).set(n)})),this.resolveNoneKeyframes()}}class On{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{var n,i;const s=tt.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(null==(n=this.events.change)||n.notify(this.current),this.dependents))for(const o of this.dependents)o.dirty();e&&(null==(i=this.events.renderRequest)||i.notify(this.current))},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=tt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new b);const n=this.events[t].add(e);return"change"===t?()=>{n(),_.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;null==(t=this.events.change)||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=tt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return V(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,e;null==(t=this.dependents)||t.clear(),null==(e=this.events.destroy)||e.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function In(t,e){return new On(t,e)}const Un=(t,e)=>e&&"number"==typeof t?e.transform(t):t,{schedule:Nn}=H(queueMicrotask,!1),Wn={x:!1,y:!1};function $n(){return Wn.x||Wn.y}function Xn(t,e){const n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document;const i=(null==n?void 0:n[t])??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Yn(t){return!("touch"===t.pointerType||$n())}const zn=(t,e)=>!!e&&(t===e||zn(t,e.parentElement)),Kn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Hn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const _n=new WeakSet;function qn(t){return e=>{"Enter"===e.key&&t(e)}}function Gn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Zn(t){return Kn(t)&&!$n()}function Jn(t,e,n={}){const[i,s,o]=Xn(t,n),r=t=>{const i=t.currentTarget;if(!Zn(t))return;_n.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),_n.has(i)&&_n.delete(i),Zn(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||zn(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),fn(t)&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=qn((()=>{if(_n.has(n))return;Gn(n,"down");const t=qn((()=>{Gn(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>Gn(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s))),e=t,Hn.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),o}function Qn(t){return g(t)&&"ownerSVGElement"in t}const ti=t=>Boolean(t&&t.getVelocity),ei=[...bn,At,jt],ni=t.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class ii extends t.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,n=fn(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function si({children:e,isPresent:n,anchorX:i}){const s=t.useId(),o=t.useRef(null),a=t.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=t.useContext(ni);return t.useInsertionEffect((()=>{const{width:t,height:e,top:r,left:u,right:h}=a.current;if(n||!o.current||!t||!e)return;const c="left"===i?`left: ${u}`:`right: ${h}`;o.current.dataset.motionPopId=s;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${s}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${c}px !important;\n            top: ${r}px !important;\n          }\n        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}}),[n]),r.jsx(ii,{isPresent:n,childRef:o,sizeRef:a,children:t.cloneElement(e,{ref:o})})}const oi=({children:e,initial:n,isPresent:i,onExitComplete:s,custom:o,presenceAffectsLayout:a,mode:u,anchorX:h})=>{const d=l(ri),p=t.useId();let m=!0,f=t.useMemo((()=>(m=!1,{id:p,initial:n,isPresent:i,custom:o,onExitComplete:t=>{d.set(t,!0);for(const e of d.values())if(!e)return;s&&s()},register:t=>(d.set(t,!1),()=>d.delete(t))})),[i,d,s]);return a&&m&&(f={...f}),t.useMemo((()=>{d.forEach(((t,e)=>d.set(e,!1)))}),[i]),t.useEffect((()=>{!i&&!d.size&&s&&s()}),[i]),"popLayout"===u&&(e=r.jsx(si,{isPresent:i,anchorX:h,children:e})),r.jsx(c.Provider,{value:f,children:e})};function ri(){return new Map}function ai(e=!0){const n=t.useContext(c);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=t.useId();t.useEffect((()=>{if(e)return o(r)}),[e]);const a=t.useCallback((()=>e&&s&&s(r)),[r,s,e]);return!i&&s?[!1,a]:[!0]}const li=t=>t.key||"";function ui(e){const n=[];return t.Children.forEach(e,(e=>{t.isValidElement(e)&&n.push(e)})),n}const hi=({children:e,custom:n,initial:i=!0,onExitComplete:s,presenceAffectsLayout:o=!0,mode:u="sync",propagate:c=!1,anchorX:d="left"})=>{const[p,m]=ai(c),f=t.useMemo((()=>ui(e)),[e]),y=c&&!p?[]:f.map(li),g=t.useRef(!0),v=t.useRef(f),x=l((()=>new Map)),[T,w]=t.useState(f),[P,S]=t.useState(f);h((()=>{g.current=!1,v.current=f;for(let t=0;t<P.length;t++){const e=li(P[t]);y.includes(e)?x.delete(e):!0!==x.get(e)&&x.set(e,!1)}}),[P,y.length,y.join("-")]);const b=[];if(f!==T){let t=[...f];for(let e=0;e<P.length;e++){const n=P[e],i=li(n);y.includes(i)||(t.splice(e,0,n),b.push(n))}return"wait"===u&&b.length&&(t=b),S(ui(t)),w(f),null}const{forceRender:A}=t.useContext(a);return r.jsx(r.Fragment,{children:P.map((t=>{const e=li(t),a=!(c&&!p)&&(f===P||y.includes(e));return r.jsx(oi,{isPresent:a,initial:!(g.current&&!i)&&void 0,custom:n,presenceAffectsLayout:o,mode:u,onExitComplete:a?void 0:()=>{if(!x.has(e))return;x.set(e,!0);let t=!0;x.forEach((e=>{e||(t=!1)})),t&&(null==A||A(),S(v.current),c&&(null==m||m()),s&&s())},anchorX:d,children:t},e)}))})},ci=t.createContext({strict:!1}),di={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},pi={};for(const ha in di)pi[ha]={isEnabled:t=>di[ha].some((e=>!!t[e]))};const mi=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function fi(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||mi.has(t)}let yi=t=>!fi(t);try{(gi=require("@emotion/is-prop-valid").default)&&(yi=t=>t.startsWith("on")?!fi(t):gi(t))}catch{}var gi;function vi(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const xi=t.createContext({});function Ti(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function wi(t){return"string"==typeof t||Array.isArray(t)}const Pi=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Si=["initial",...Pi];function bi(t){return Ti(t.animate)||Si.some((e=>wi(t[e])))}function Ai(t){return Boolean(bi(t)||t.variants)}function Ei(e){const{initial:n,animate:i}=function(t,e){if(bi(t)){const{initial:e,animate:n}=t;return{initial:!1===e||wi(e)?e:void 0,animate:wi(n)?n:void 0}}return!1!==t.inherit?e:{}}(e,t.useContext(xi));return t.useMemo((()=>({initial:n,animate:i})),[Vi(n),Vi(i)])}function Vi(t){return Array.isArray(t)?t.join(" "):t}const Mi=Symbol.for("motionComponentSymbol");function Ci(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function Di(e,n,i){return t.useCallback((t=>{t&&e.onMount&&e.onMount(t),n&&(t?n.mount(t):n.unmount()),i&&("function"==typeof i?i(t):Ci(i)&&(i.current=t))}),[n])}const ki=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Ri="data-"+ki("framerAppearId"),Li=t.createContext({});function ji(e,n,i,s,o){var r,a;const{visualElement:l}=t.useContext(xi),u=t.useContext(ci),d=t.useContext(c),p=t.useContext(ni).reducedMotion,m=t.useRef(null);s=s||u.renderer,!m.current&&s&&(m.current=s(e,{visualState:n,parent:l,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:p}));const f=m.current,y=t.useContext(Li);!f||f.projection||!o||"html"!==f.type&&"svg"!==f.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Bi(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&Ci(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:h,layoutScroll:l,layoutRoot:u})}(m.current,i,o,y);const g=t.useRef(!1);t.useInsertionEffect((()=>{f&&g.current&&f.update(i,d)}));const v=i[Ri],x=t.useRef(Boolean(v)&&!(null==(r=window.MotionHandoffIsComplete)?void 0:r.call(window,v))&&(null==(a=window.MotionHasOptimisedAnimation)?void 0:a.call(window,v)));return h((()=>{f&&(g.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Nn.render(f.render),x.current&&f.animationState&&f.animationState.animateChanges())})),t.useEffect((()=>{f&&(!x.current&&f.animationState&&f.animationState.animateChanges(),x.current&&(queueMicrotask((()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,v)})),x.current=!1))})),f}function Bi(t){if(t)return!1!==t.options.allowProjection?t.projection:Bi(t.parent)}function Fi({preloadedFeatures:e,createVisualElement:n,useRender:i,useVisualState:s,Component:o}){function a(e,a){let l;const h={...t.useContext(ni),...e,layoutId:Oi(e)},{isStatic:c}=h,d=Ei(e),p=s(e,c);if(!c&&u){t.useContext(ci).strict;const e=function(t){const{drag:e,layout:n}=pi;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(h);l=e.MeasureLayout,d.visualElement=ji(o,p,h,n,e.ProjectionNode)}return r.jsxs(xi.Provider,{value:d,children:[l&&d.visualElement?r.jsx(l,{visualElement:d.visualElement,...h}):null,i(o,e,Di(p,d.visualElement,a),p,c,d.visualElement)]})}e&&function(t){for(const e in t)pi[e]={...pi[e],...t[e]}}(e),a.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const l=t.forwardRef(a);return l[Mi]=o,l}function Oi({layoutId:e}){const n=t.useContext(a).id;return n&&void 0!==e?n+"-"+e:e}const Ii={};function Ui(t,{layout:e,layoutId:n}){return $e.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Ii[t]||"opacity"===t)}const Ni={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Wi=We.length;function $i(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const t=e[l];if($e.has(l))r=!0;else if(nt(l))s[l]=t;else{const e=Un(t,kn[l]);l.startsWith("origin")?(a=!0,o[l]=e):i[l]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<Wi;o++){const r=We[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Un(a,kn[r]);l||(s=!1,i+=`${Ni[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const Xi=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Yi(t,e,n){for(const i in e)ti(e[i])||Ui(i,n)||(t[i]=e[i])}function zi(e,n){const i={};return Yi(i,e.style||{},e),Object.assign(i,function({transformTemplate:e},n){return t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{}};return $i(t,n,e),Object.assign({},t.vars,t.style)}),[n])}(e,n)),i}function Ki(t,e){const n={},i=zi(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Hi={offset:"stroke-dashoffset",array:"stroke-dasharray"},_i={offset:"strokeDashoffset",array:"strokeDasharray"};function qi(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,u,h){if($i(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=(null==h?void 0:h.transformBox)??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==i&&(c.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Hi:_i;t[o.offset]=Tt.transform(-i);const r=Tt.transform(e),a=Tt.transform(n);t[o.array]=`${r} ${a}`}(c,s,o,r,!1)}const Gi=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Zi=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Ji(e,n,i,s){const o=t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return qi(t,n,Zi(s),e.transformTemplate,e.style),{...t.attrs,style:{...t.style}}}),[n]);if(e.style){const t={};Yi(t,e.style,e),o.style={...t,...o.style}}return o}const Qi=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ts(t){return"string"==typeof t&&!t.includes("-")&&!!(Qi.indexOf(t)>-1||/[A-Z]/u.test(t))}function es(e=!1){return(n,i,s,{latestValues:o},r)=>{const a=(ts(n)?Ji:Ki)(i,o,r,n),l=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(yi(s)||!0===n&&fi(s)||!e&&!fi(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(i,"string"==typeof n,e),u=n!==t.Fragment?{...l,...a,ref:s}:{},{children:h}=i,c=t.useMemo((()=>ti(h)?h.get():h),[h]);return t.createElement(n,{...u,children:c})}}function ns(t){const e=[{},{}];return null==t||t.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function is(t,e,n,i){if("function"==typeof e){const[s,o]=ns(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=ns(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function ss(t){return ti(t)?t.get():t}const os=e=>(n,i)=>{const s=t.useContext(xi),o=t.useContext(c),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:rs(n,i,s,t),renderState:e()}}(e,n,s,o);return i?r():l(r)};function rs(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=ss(o[d]);let{initial:r,animate:a}=t;const l=bi(t),u=Ai(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let h=!!n&&!1===n.initial;h=h||!1===r;const c=h?a:r;if(c&&"boolean"!=typeof c&&!Ti(c)){const e=Array.isArray(c)?c:[c];for(let n=0;n<e.length;n++){const i=is(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const i in n){let t=n[i];if(Array.isArray(t)){t=t[h?t.length-1:0]}null!==t&&(s[i]=t)}for(const i in t)s[i]=t[i]}}}return s}function as(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(ti(s[r])||e.style&&ti(e.style[r])||Ui(r,t)||void 0!==(null==(i=null==n?void 0:n.getValue(r))?void 0:i.liveStyle))&&(o[r]=s[r]);return o}const ls={useVisualState:os({scrapeMotionValuesFromProps:as,createRenderState:Xi})};function us(t,e,n){const i=as(t,e,n);for(const s in t)if(ti(t[s])||ti(e[s])){i[-1!==We.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=t[s]}return i}const hs={useVisualState:os({scrapeMotionValuesFromProps:us,createRenderState:Gi})};function cs(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return Fi({...ts(n)?hs:ls,preloadedFeatures:t,useRender:es(i),createVisualElement:e,Component:n})}}function ds(t,e,n){const i=t.getProps();return is(i,e,void 0!==n?n:i.custom,t)}const ps=t=>Array.isArray(t);function ms(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,In(n))}function fs(t,e){const n=t.getValue("willChange");if(i=n,Boolean(ti(i)&&i.add))return n.add(e);if(!n&&f.WillChange){const n=new f.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function ys(t){return t.props[Ri]}const gs=t=>null!==t;const vs={type:"spring",stiffness:500,damping:25,restSpeed:10},xs={type:"keyframes",duration:.8},Ts={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ws=(t,{keyframes:e})=>e.length>2?xs:$e.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:vs:Ts;const Ps=(t,e,n,i={},s,o)=>r=>{const a=wn(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=A(l);const h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length})(a)||Object.assign(h,ws(t,h)),h.duration&&(h.duration=A(h.duration)),h.repeatDelay&&(h.repeatDelay=A(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(c=!0)),(f.instantAnimations||f.skipAnimations)&&(c=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,c&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"}){const i=t.filter(gs);return i[e&&"loop"!==n&&e%2==1?0:i.length-1]}(h.keyframes,a);if(void 0!==t)return void _.update((()=>{h.onUpdate(t),h.onComplete()}))}return a.isSync?new De(h):new vn(h)};function Ss({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function bs(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(const h in a){const e=t.getValue(h,t.latestValues[h]??null),i=a[h];if(void 0===i||u&&Ss(u,h))continue;const s={delay:n,...wn(o||{},h)},r=e.get();if(void 0!==r&&!e.isAnimating&&!Array.isArray(i)&&i===r&&!s.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const e=ys(t);if(e){const t=window.MotionHandoffAnimation(e,h,_);null!==t&&(s.startTime=t,c=!0)}}fs(t,h),e.start(Ps(h,e,i,t.shouldReduceMotion&&Pn.has(h)?{type:!1}:s,t,c));const d=e.animation;d&&l.push(d)}return r&&Promise.all(l).then((()=>{_.update((()=>{r&&function(t,e){const n=ds(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const a in o)ms(t,a,(r=o[a],ps(r)?r[r.length-1]||0:r));var r}(t,r)}))})),l}function As(t,e,n={}){var i;const s=ds(t,e,"exit"===n.type?null==(i=t.presenceContext)?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(bs(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(Es).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(As(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then((()=>e()))}return Promise.all([r(),a(n.delay)])}function Es(t,e){return t.sortNodePosition(e)}function Vs(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const Ms=Si.length;function Cs(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Cs(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Ms;n++){const i=Si[n],s=t.props[i];(wi(s)||!1===s)&&(e[i]=s)}return e}const Ds=[...Pi].reverse(),ks=Pi.length;function Rs(t){return e=>Promise.all(e.map((({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>As(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=As(t,e,n);else{const s="function"==typeof e?ds(t,e,n.custom):e;i=Promise.all(bs(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}(t,e,n))))}function Ls(t){let e=Rs(t),n=Fs(),i=!0;const s=e=>(n,i)=>{var s;const o=ds(t,i,"exit"===e?null==(s=t.presenceContext)?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=Cs(t.parent)||{},l=[],u=new Set;let h={},c=1/0;for(let e=0;e<ks;e++){const d=Ds[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=wi(m),y=d===o?p.isActive:null;!1===y&&(c=e);let g=m===a[d]&&m!==r[d]&&f;if(g&&i&&t.manuallyAnimateOnMount&&(g=!1),p.protectedKeys={...h},!p.isActive&&null===y||!m&&!p.prevProp||Ti(m)||"boolean"==typeof m)continue;const v=js(p.prevProp,m);let x=v||d===o&&p.isActive&&!g&&f||e>c&&f,T=!1;const w=Array.isArray(m)?m:[m];let P=w.reduce(s(d),{});!1===y&&(P={});const{prevResolvedValues:S={}}=p,b={...S,...P},A=e=>{x=!0,u.has(e)&&(T=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=P[t],n=S[t];if(h.hasOwnProperty(t))continue;let i=!1;i=ps(e)&&ps(n)?!Vs(e,n):e!==n,i?null!=e?A(t):u.add(t):void 0!==e&&u.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(h={...h,...P}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(g&&v)||T)&&l.push(...w.map((t=>({animation:t,options:{type:d}}))))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=ds(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null==(s=t.variantChildren)||s.forEach((t=>{var n;return null==(n=t.animationState)?void 0:n.setActive(e,i)})),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Fs(),i=!0}}}function js(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Vs(e,t)}function Bs(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Fs(){return{animate:Bs(!0),whileInView:Bs(),whileHover:Bs(),whileTap:Bs(),whileDrag:Bs(),whileFocus:Bs(),exit:Bs()}}class Os{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Is=0;const Us={animation:{Feature:class extends Os{constructor(t){super(t),t.animationState||(t.animationState=Ls(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Ti(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}},exit:{Feature:class extends Os{constructor(){super(...arguments),this.id=Is++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Ns(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function Ws(t){return{point:{x:t.pageX,y:t.pageY}}}function $s(t,e,n,i){return Ns(t,e,(t=>e=>Kn(e)&&t(e,Ws(e)))(n),i)}function Xs({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Ys(t){return t.max-t.min}function zs(t,e,n,i=.5){t.origin=i,t.originPoint=Ot(e.min,e.max,t.origin),t.scale=Ys(n)/Ys(e),t.translate=Ot(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Ks(t,e,n,i){zs(t.x,e.x,n.x,i?i.originX:void 0),zs(t.y,e.y,n.y,i?i.originY:void 0)}function Hs(t,e,n){t.min=n.min+e.min,t.max=t.min+Ys(e)}function _s(t,e,n){t.min=e.min-n.min,t.max=t.min+Ys(e)}function qs(t,e,n){_s(t.x,e.x,n.x),_s(t.y,e.y,n.y)}const Gs=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Zs(t){return[t("x"),t("y")]}function Js(t){return void 0===t||1===t}function Qs({scale:t,scaleX:e,scaleY:n}){return!Js(t)||!Js(e)||!Js(n)}function to(t){return Qs(t)||eo(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function eo(t){return no(t.x)||no(t.y)}function no(t){return t&&"0%"!==t}function io(t,e,n){return n+e*(t-n)}function so(t,e,n,i,s){return void 0!==s&&(t=io(t,s,i)),io(t,n,i)+e}function oo(t,e=0,n=1,i,s){t.min=so(t.min,e,n,i,s),t.max=so(t.max,e,n,i,s)}function ro(t,{x:e,y:n}){oo(t.x,e.translate,e.scale,e.originPoint),oo(t.y,n.translate,n.scale,n.originPoint)}const ao=.999999999999,lo=1.0000000000001;function uo(t,e){t.min=t.min+e,t.max=t.max+e}function ho(t,e,n,i,s=.5){oo(t,e,n,Ot(t.min,t.max,s),i)}function co(t,e){ho(t.x,e.x,e.scaleX,e.scale,e.originX),ho(t.y,e.y,e.scaleY,e.scale,e.originY)}function po(t,e){return Xs(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const mo=({current:t})=>t?t.ownerDocument.defaultView:null,fo=(t,e)=>Math.abs(t-e);class yo{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=xo(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=fo(t.x,e.x),i=fo(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=G;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=go(e,this.transformPagePoint),_.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=xo("pointercancel"===t.type?this.lastMoveEventInfo:go(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!Kn(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=go(Ws(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=G;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,xo(o,this.history)),this.removeListeners=P($s(this.contextWindow,"pointermove",this.handlePointerMove),$s(this.contextWindow,"pointerup",this.handlePointerUp),$s(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),q(this.updatePoint)}}function go(t,e){return e?{point:e(t.point)}:t}function vo(t,e){return{x:t.x-e.x,y:t.y-e.y}}function xo({point:t},e){return{point:t,delta:vo(t,wo(e)),offset:vo(t,To(e)),velocity:Po(e,.1)}}function To(t){return t[0]}function wo(t){return t[t.length-1]}function Po(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=wo(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>A(e)));)n--;if(!i)return{x:0,y:0};const o=E(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function So(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function bo(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Ao=.35;function Eo(t,e,n){return{min:Vo(t,e),max:Vo(t,n)}}function Vo(t,e){return"number"==typeof t?t:t[e]||0}const Mo=new WeakMap;class Co{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new yo(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Ws(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?Wn[o]?null:(Wn[o]=!0,()=>{Wn[o]=!1}):Wn.x||Wn.y?null:(Wn.x=Wn.y=!0,()=>{Wn.x=Wn.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Zs((t=>{let e=this.getAxisMotionValue(t).get()||0;if(xt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Ys(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&_.postRender((()=>s(t,e))),fs(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Zs((t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())}))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:mo(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&_.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!Do(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Ot(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Ot(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,s=this.constraints;e&&Ci(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:So(t.x,n,s),y:So(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=Ao){return!1===t?t=0:!0===t&&(t=Ao),{x:Eo(t,"left","right"),y:Eo(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Zs((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Ci(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=po(t,n),{scroll:s}=e;return s&&(uo(i.x,s.offset.x),uo(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:bo(t.x,e.x),y:bo(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Xs(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Zs((r=>{if(!Do(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,h=i?40:1e7,c={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,c)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return fs(this.visualElement,t),n.start(Ps(t,n,0,e,this.visualElement,!1))}stopAnimation(){Zs((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){Zs((t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()}))}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Zs((e=>{const{drag:n}=this.getProps();if(!Do(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Ot(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!Ci(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Zs((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Ys(t),s=Ys(e);return s>i?n=S(e.min,e.max-i,t.min):i>s&&(n=S(t.min,t.max-s,e.min)),m(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Zs((e=>{if(!Do(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Ot(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;Mo.set(this.visualElement,this);const t=$s(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();Ci(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),_.read(e);const s=Ns(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Zs((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Ao,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function Do(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const ko=t=>(e,n)=>{t&&_.postRender((()=>t(e,n)))};const Ro={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Lo(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const jo={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Tt.test(t))return t;t=parseFloat(t)}return`${Lo(t,e.target.x)}% ${Lo(t,e.target.y)}%`}},Bo={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=jt.parse(t);if(s.length>5)return i;const o=jt.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Ot(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};class Fo extends t.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;!function(t){for(const e in t)Ii[e]=t[e],nt(e)&&(Ii[e].isCSSVariable=!0)}(Io),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Ro.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||_.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Nn.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Oo(e){const[n,i]=ai(),s=t.useContext(a);return r.jsx(Fo,{...e,layoutGroup:s,switchLayoutGroup:t.useContext(Li),isPresent:n,safeToRemove:i})}const Io={borderRadius:{...jo,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:jo,borderTopRightRadius:jo,borderBottomLeftRadius:jo,borderBottomRightRadius:jo,boxShadow:Bo};const Uo=(t,e)=>t.depth-e.depth;class No{constructor(){this.children=[],this.isDirty=!1}add(t){d(this.children,t),this.isDirty=!0}remove(t){p(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Uo),this.isDirty=!1,this.children.forEach(t)}}const Wo=["TopLeft","TopRight","BottomLeft","BottomRight"],$o=Wo.length,Xo=t=>"string"==typeof t?parseFloat(t):t,Yo=t=>"number"==typeof t||Tt.test(t);function zo(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Ko=_o(0,.5,O),Ho=_o(.5,.95,T);function _o(t,e,n){return i=>i<t?0:i>e?1:n(S(t,e,i))}function qo(t,e){t.min=e.min,t.max=e.max}function Go(t,e){qo(t.x,e.x),qo(t.y,e.y)}function Zo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Jo(t,e,n,i,s){return t=io(t-=e,1/n,i),void 0!==s&&(t=io(t,1/s,i)),t}function Qo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){xt.test(e)&&(e=parseFloat(e),e=Ot(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Ot(o.min,o.max,i);t===o&&(a-=e),t.min=Jo(t.min,e,n,a,s),t.max=Jo(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const tr=["x","scaleX","originX"],er=["y","scaleY","originY"];function nr(t,e,n,i){Qo(t.x,e,tr,n?n.x:void 0,i?i.x:void 0),Qo(t.y,e,er,n?n.y:void 0,i?i.y:void 0)}function ir(t){return 0===t.translate&&1===t.scale}function sr(t){return ir(t.x)&&ir(t.y)}function or(t,e){return t.min===e.min&&t.max===e.max}function rr(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ar(t,e){return rr(t.x,e.x)&&rr(t.y,e.y)}function lr(t){return Ys(t.x)/Ys(t.y)}function ur(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class hr{constructor(){this.members=[]}add(t){d(this.members,t),t.scheduleRender()}remove(t){if(p(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const cr=["","X","Y","Z"],dr={visibility:"hidden"};let pr=0;function mr(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function fr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=ys(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",_,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&fr(i)}function yr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=pr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(xr),this.nodes.forEach(Er),this.nodes.forEach(Vr),this.nodes.forEach(Tr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new No)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new b),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var n;this.isSVG=Qn(e)&&!(Qn(n=e)&&"svg"===n.tagName),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=tt.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(q(i),t(o-e))};return _.setup(i,!0),()=>q(i)}(i,250),Ro.hasAnimatedSinceResize&&(Ro.hasAnimatedSinceResize=!1,this.nodes.forEach(Ar))}))}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Lr,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!ar(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...wn(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||Ar(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Mr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&fr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let s=0;s<this.path.length;s++){const t=this.path[s];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Pr);this.isUpdating||this.nodes.forEach(Sr),this.isUpdating=!1,this.nodes.forEach(br),this.nodes.forEach(gr),this.nodes.forEach(vr),this.clearAllSnapshots();const t=tt.now();G.delta=m(0,1e3/60,t-G.timestamp),G.timestamp=t,G.isProcessing=!0,Z.update.process(G),Z.preRender.process(G),Z.render.process(G),G.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Nn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(wr),this.sharedNodes.forEach(Cr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,_.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){_.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Ys(this.snapshot.measuredBox.x)||Ys(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sr(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||to(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Fr((i=n).x),Fr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const n=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(Ir))){const{scroll:t}=this.root;t&&(uo(n.x,t.offset.x),uo(n.y,t.offset.y))}return n}removeElementScroll(t){var e;const n={x:{min:0,max:0},y:{min:0,max:0}};if(Go(n,t),null==(e=this.scroll)?void 0:e.wasRoot)return n;for(let i=0;i<this.path.length;i++){const e=this.path[i],{scroll:s,options:o}=e;e!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Go(n,t),uo(n.x,s.offset.x),uo(n.y,s.offset.y))}return n}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Go(n,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&co(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),to(t.latestValues)&&co(n,t.latestValues)}return to(this.latestValues)&&co(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Go(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!to(t.latestValues))continue;Qs(t.latestValues)&&t.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};Go(i,t.measurePageBox()),nr(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return to(this.latestValues)&&nr(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==G.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=G.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},qs(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Go(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var r,a,l;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,Hs(r.x,a.x,l.x),Hs(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Go(this.target,this.layout.layoutBox),ro(this.target,this.targetDelta)):Go(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},qs(this.relativeTargetOrigin,this.target,t.target),Go(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!Qs(this.parent.latestValues)&&!eo(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===G.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;Go(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&co(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,ro(t,r)),i&&to(o.latestValues)&&co(t,o.latestValues))}e.x<lo&&e.x>ao&&(e.x=1),e.y<lo&&e.y>ao&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;l?(this.projectionDelta&&this.prevProjectionDelta?(Zo(this.prevProjectionDelta.x,this.projectionDelta.x),Zo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Ks(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&ur(this.projectionDelta.x,this.prevProjectionDelta.x)&&ur(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,h=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Rr));let c;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,y;Dr(o.x,t.x,n),Dr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(qs(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,y=n,kr(p.x,m.x,f.x,y),kr(p.y,m.y,f.y,y),c&&(l=this.relativeTarget,d=c,or(l.x,d.x)&&or(l.y,d.y))&&(this.isProjectionDirty=!1),c||(c={x:{min:0,max:0},y:{min:0,max:0}}),Go(c,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Ot(0,n.opacity??1,Ko(i)),t.opacityExit=Ot(e.opacity??1,0,Ho(i))):o&&(t.opacity=Ot(e.opacity??1,n.opacity??1,i));for(let r=0;r<$o;r++){const s=`border${Wo[r]}Radius`;let o=zo(e,s),a=zo(n,s);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||Yo(o)===Yo(a)?(t[s]=Math.max(Ot(Xo(o),Xo(a),i),0),(xt.test(a)||xt.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=Ot(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,h,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){var e,n,i;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(!1),null==(i=null==(n=this.resumingFrom)?void 0:n.currentAnimation)||i.stop(!1),this.pendingAnimation&&(q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=_.update((()=>{Ro.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=In(0)),this.currentAnimation=function(t,e,n){const i=ti(t)?t:In(t);return i.start(Ps("",i,e,n)),i.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Or(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Ys(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Ys(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Go(e,n),co(e,s),Ks(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new hr);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&mr("z",t,i,this.animationValues);for(let s=0;s<cr.length;s++)mr(`rotate${cr[s]}`,t,i,this.animationValues),mr(`skew${cr[s]}`,t,i,this.animationValues);t.render();for(const s in i)t.setStaticValue(s,i[s]),this.animationValues&&(this.animationValues[s]=i[s]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return dr;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=ss(null==t?void 0:t.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=ss(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!to(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const s=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),n&&(e.transform=n(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,i.animationValues?e.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const a in Ii){if(void 0===s[a])continue;const{correct:t,applyTo:n,isCSSVariable:o}=Ii[a],r="none"===e.transform?s[a]:t(s[a],i);if(n){const t=n.length;for(let i=0;i<t;i++)e[n[i]]=r}else o?this.options.visualElement.renderState.vars[a]=r:e[a]=r}return this.options.layoutId&&(e.pointerEvents=i===this?ss(null==t?void 0:t.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop(!1)})),this.root.nodes.forEach(Pr),this.root.sharedNodes.clear()}}}function gr(t){t.updateLayout()}function vr(t){var e;const n=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?Zs((t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=Ys(i);i.min=e[t].min,i.max=i.min+s})):Or(s,n.layoutBox,e)&&Zs((i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=Ys(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Ks(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Ks(a,t.applyTransform(i,!0),n.measuredBox):Ks(a,e,n.layoutBox);const l=!sr(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};qs(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};qs(a,e,o.layoutBox),ar(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function xr(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Tr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function wr(t){t.clearSnapshot()}function Pr(t){t.clearMeasurements()}function Sr(t){t.isLayoutDirty=!1}function br(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Ar(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Er(t){t.resolveTargetDelta()}function Vr(t){t.calcProjection()}function Mr(t){t.resetSkewAndRotation()}function Cr(t){t.removeLeadSnapshot()}function Dr(t,e,n){t.translate=Ot(e.translate,0,n),t.scale=Ot(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function kr(t,e,n,i){t.min=Ot(e.min,n.min,i),t.max=Ot(e.max,n.max,i)}function Rr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Lr={duration:.45,ease:[.4,0,.1,1]},jr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Br=jr("applewebkit/")&&!jr("chrome/")?Math.round:T;function Fr(t){t.min=Br(t.min),t.max=Br(t.max)}function Or(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=lr(e),s=lr(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Ir(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}const Ur=yr({attachResizeListener:(t,e)=>Ns(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Nr={current:void 0},Wr=yr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Nr.current){const t=new Ur({});t.mount(window),t.setOptions({layoutScroll:!0}),Nr.current=t}return Nr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),$r={pan:{Feature:class extends Os{constructor(){super(...arguments),this.removePointerDownListener=T}onPointerDown(t){this.session=new yo(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:mo(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:ko(t),onStart:ko(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&_.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=$s(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Os{constructor(t){super(t),this.removeGroupControls=T,this.removeListeners=T,this.controls=new Co(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||T}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Wr,MeasureLayout:Oo}};function Xr(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&_.postRender((()=>s(e,Ws(e))))}function Yr(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&_.postRender((()=>s(e,Ws(e))))}const zr=new WeakMap,Kr=new WeakMap,Hr=t=>{const e=zr.get(t.target);e&&e(t)},_r=t=>{t.forEach(Hr)};function qr(t,e,n){const i=function({root:t,...e}){const n=t||document;Kr.has(n)||Kr.set(n,{});const i=Kr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(_r,{root:t,...e})),i[s]}(e);return zr.set(t,n),i.observe(t),()=>{zr.delete(t),i.unobserve(t)}}const Gr={some:0,all:1};const Zr={inView:{Feature:class extends Os{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:Gr[i]};return qr(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Os{mount(){const{current:t}=this.node;t&&(this.unmount=Jn(t,((t,e)=>(Yr(this.node,e,"Start"),(t,{success:e})=>Yr(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Os{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=P(Ns(this.node.current,"focus",(()=>this.onFocus())),Ns(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Os{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=Xn(t,n),r=t=>{if(!Yn(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{Yn(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),o}(t,((t,e)=>(Xr(this.node,e,"Start"),t=>Xr(this.node,t,"End")))))}unmount(){}}}},Jr={layout:{ProjectionNode:Wr,MeasureLayout:Oo}},Qr={current:null},ta={current:!1};const ea=new WeakMap;const na=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ia{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Qe,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=tt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,_.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=bi(e),this.isVariantNode=Ai(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const c in h){const t=h[c];void 0!==a[c]&&ti(t)&&t.set(a[c],!1)}}mount(t){this.current=t,ea.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),ta.current||function(){if(ta.current=!0,u)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Qr.current=t.matches;t.addListener(e),e()}else Qr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Qr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),q(this.notifyUpdate),q(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=$e.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&_.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in pi){const e=pi[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<na.length;n++){const e=na[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(ti(s))t.addValue(i,s);else if(ti(o))t.addValue(i,In(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,In(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=In(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=n&&("string"==typeof n&&(y(n)||v(n))?n=parseFloat(n):(i=n,!ei.find(Sn(i))&&jt.test(e)&&(n=jn(t,e))),this.setBaseTarget(t,ti(n)?n.get():n)),ti(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=is(this.props,n,null==(e=this.presenceContext)?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||ti(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new b),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sa extends ia{constructor(){super(...arguments),this.KeyframeResolver=Fn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ti(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}function oa(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}class ra extends sa{constructor(){super(...arguments),this.type="html",this.renderInstance=oa}readValueFromInstance(t,e){var n,i;if($e.has(e))return(null==(n=this.projection)?void 0:n.isProjecting)?Ie(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Ue(n,e)})(t,e);{const n=(i=t,window.getComputedStyle(i)),s=(nt(e)?n.getPropertyValue(e):n[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return po(t,e)}build(t,e,n){$i(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return as(t,e,n)}}const aa=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class la extends sa{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Gs}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if($e.has(e)){const t=Ln(e);return t&&t.default||0}return e=aa.has(e)?e:ki(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return us(t,e,n)}build(t,e,n){qi(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){oa(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(aa.has(s)?s:ki(s),e.attrs[s])}(t,e,0,i)}mount(t){this.isSVGTag=Zi(t.tagName),super.mount(t)}}const ua=vi(cs({...Us,...Zr,...$r,...Jr},((e,n)=>ts(e)?new la(n):new ra(n,{allowProjection:e!==t.Fragment}))));export{hi as A,r as j,ua as m};
//# sourceMappingURL=chunk-L3uak9dD.js.map
