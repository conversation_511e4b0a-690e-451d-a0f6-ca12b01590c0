{"version": 3, "mappings": ";g2CAMsB,oBAAXA,SACRA,OAAeC,MAAQA,EACvBD,OAAeE,MAAQD,GAIJ,oBAAXE,aACRA,WAAeF,MAAQA,EACvBE,WAAeD,MAAQD,iDCZ1B,IAAIG,EAAIC,WAENC,aAAqBF,EAAEG,WACvBD,cAAsBF,EAAEI,0oCCmB1B,IAAIC,EAAQ,EA+BZ,MAAMC,MAAoBC,IAEpBC,EAAoBC,IACpBH,KAAcI,IAAID,GACpB,OAGIE,QAAUC,YAAW,KACzBN,EAAcO,OAAOJ,GACZK,GAAA,CACPC,KAAM,eACNN,WACF,GA3DuB,KA8DXO,MAAIP,EAASE,IAGhBM,EAAU,CAACC,EAAcC,KACpC,OAAQA,EAAOJ,MACb,IAAK,YACI,UACFG,EACHE,OAAQ,CAACD,EAAOE,SAAUH,EAAME,QAAQE,MAAM,EAvElC,IA0EhB,IAAK,eACI,UACFJ,EACHE,OAAQF,EAAME,OAAOG,KAAKC,GACxBA,EAAEC,KAAON,EAAOE,MAAMI,GAAK,IAAKD,KAAML,EAAOE,OAAUG,KAI7D,IAAK,gBAAiB,CACd,MAAAf,QAAEA,GAAYU,EAYb,OARHV,EACFD,EAAiBC,GAEXW,SAAOM,SAASL,IACpBb,EAAiBa,EAAMI,GAAE,IAItB,IACFP,EACHE,OAAQF,EAAME,OAAOG,KAAKC,GACxBA,EAAEC,KAAOhB,QAAuBkB,IAAZlB,EAChB,IACKe,EACHI,MAAM,GAERJ,IAER,CAEF,IAAK,eACCL,YAAmBQ,IAAnBR,EAAOV,QACF,IACFS,EACHE,OAAQ,IAGL,IACFF,EACHE,OAAQF,EAAME,OAAOS,QAAQL,GAAMA,EAAEC,KAAON,EAAOV,WACrD,EAIAqB,GAA2C,GAEjD,IAAIC,GAAqB,CAAEX,OAAQ,IAEnC,SAASN,GAASK,GACFF,KAAQc,GAAaZ,GACzBO,YAASM,IACjBA,EAASD,MAEb,CAIA,SAASV,OAAWY,IAClB,MAAMR,GAnHGpB,KAAQ,GAAK6B,OAAOC,iBACtB9B,EAAM+B,YAyHPC,EAAU,IAAMvB,GAAS,CAAEC,KAAM,gBAAiBN,QAASgB,IAc1D,OAZEX,GAAA,CACPC,KAAM,YACNM,MAAO,IACFY,EACHR,KACAG,MAAM,EACNU,aAAeV,IACRA,GAAMS,QAKV,CACLZ,KACAY,UACAE,OAtBcN,GACdnB,GAAS,CACPC,KAAM,eACNM,MAAO,IAAKY,EAAOR,QAqBzB,CChKA,SAASe,GAAiBC,GACxB,MAAMC,EAAgBD,EAAO,sBACtBE,EAAyBC,GAAyBC,EAAmBH,IACrEI,EAAwBC,GAAwBJ,EACrDD,EACA,CAAEM,cAAe,CAAEC,QAAS,MAAQC,QAA6B,IAAA3C,MAE7D4C,EAAsBlB,IACpB,MAAAmB,MAAEA,EAAOC,YAAapB,EACtBqB,EAAMzD,EAAM0D,OAAO,MACnBL,EAAUrD,EAAM0D,OAA2B,IAAAhD,KAAO0C,QACjCO,SAAGA,IAACV,EAAwB,CAAEM,QAAOF,UAASF,cAAeM,EAAKD,YAAU,EAErGF,EAAmBM,YAAcf,EACjC,MAAMgB,EAAuBjB,EAAO,iBAC9BkB,EAAqBC,EAAWF,GAChCG,EAAiBhE,EAAMiE,YAC3B,CAAC7B,EAAO8B,KACA,MAAAX,MAAEA,EAAOC,YAAapB,EACtB+B,EAAUjB,EAAqBW,EAAsBN,GACrDa,EAAeC,EAAgBH,EAAcC,EAAQhB,eAC3D,SAAuBQ,IAAIG,EAAoB,CAAEL,IAAKW,EAAcZ,YAAU,IAGlFQ,EAAeJ,YAAcC,EAC7B,MAAMS,EAAiB1B,EAAO,qBACxB2B,EAAiB,6BACjBC,EAAyBT,EAAWO,GACpCG,EAAqBzE,EAAMiE,YAC/B,CAAC7B,EAAO8B,KACN,MAAMX,MAAEA,EAAAC,SAAOA,KAAakB,GAAatC,EACnCqB,EAAMzD,EAAM0D,OAAO,MACnBU,EAAeC,EAAgBH,EAAcT,GAC7CU,EAAUjB,EAAqBoB,EAAgBf,GAKrD,OAJAvD,EAAM2E,WAAU,KACdR,EAAQd,QAAQlC,IAAIsC,EAAK,CAAEA,SAAQiB,IAC5B,KAAWP,EAAQd,QAAQrC,OAAOyC,EAAG,WAEnBe,EAAwB,CAAOD,CAACA,GAAiB,GAAMd,IAAKW,EAAcZ,YAAU,IAkB5G,OAfPiB,EAAmBb,YAAcU,EAe1B,CACL,CAAEM,SAAUtB,EAAoBuB,KAAMb,EAAgBc,SAAUL,GAflE,SAAuBlB,GACrB,MAAMY,EAAUjB,EAAqBN,EAAO,qBAAsBW,GAW3D,OAVUvD,EAAM+E,aAAY,KAC3B,MAAAC,EAAiBb,EAAQhB,cAAcC,QACzC,IAAC4B,EAAgB,MAAO,GACtB,MAAAC,EAAeC,MAAMC,KAAKH,EAAeI,iBAAiB,IAAIb,OAK7D,OAJOW,MAAMC,KAAKhB,EAAQd,QAAQgC,UACdC,MACzB,CAACC,EAAGC,IAAMP,EAAaQ,QAAQF,EAAE9B,IAAIL,SAAW6B,EAAaQ,QAAQD,EAAE/B,IAAIL,UAEtE,GACN,CAACe,EAAQhB,cAAegB,EAAQd,SAEvC,EAIIN,EAEJ,CCnDA,IAAIF,GAAgB,iBACf6C,GAAYC,GAAe5C,IAAyBJ,GAAiB,UACrEiD,GAAoBC,IAAoB7C,EAAmB,QAAS,CAACD,MACrE+C,GAAuBC,IAA2BH,GAAmB/C,IACtEmD,GAAiB5D,IACb,MAAA6D,aACJA,EAAAC,MACAA,EAAQ,eAAAC,SACRA,EAAW,IAAAC,eACXA,EAAiB,QAAAC,eACjBA,EAAiB,GAAA7C,SACjBA,GACEpB,GACGkE,EAAUC,GAAeC,WAAe,OACxCC,EAAYC,GAAiBF,WAAe,GAC7CG,EAAiCC,EAAYlD,QAAC,GAC9CmD,EAAmBD,EAAYlD,QAAC,GAMfC,OALlBuC,EAAMY,QACDC,QAAAC,MACN,wCAAwCnE,wCAGrBc,MAAI+B,GAAWd,SAAU,CAAErB,MAAO0C,EAAczC,SAA6ByD,EAAAtD,IAClGmC,GACA,CACEvC,MAAO0C,EACPC,QACAC,WACAC,iBACAC,iBACAI,aACAH,WACAY,iBAAkBX,EAClBY,WAAYC,eAAkB,IAAMV,GAAeW,GAAcA,EAAY,KAAI,IACjFC,cAAeF,eAAkB,IAAMV,GAAeW,GAAcA,EAAY,KAAI,IACpFV,iCACAE,mBACArD,cAED,EAELwC,GAAcpC,YAAcf,GAC5B,IAAI0E,GAAgB,gBAChBC,GAA0B,CAAC,MAC3BC,GAAiB,sBACjBC,GAAkB,uBAClBC,GAAgBC,EAAgB3D,YAClC,CAAC7B,EAAO8B,KACA,MAAA+B,aACJA,EAAA4B,OACAA,EAASL,GAAAtB,MACTA,EAAQ,8BACL4B,GACD1F,EACE+B,EAAU4B,GAAwBwB,GAAetB,GACjD8B,EAAWpC,GAAcM,GACzB+B,EAAapB,EAAYlD,OAAC,MAC1BuE,EAAoBrB,EAAYlD,OAAC,MACjCwE,EAAoBtB,EAAYlD,OAAC,MACjCD,EAAMmD,EAAYlD,OAAC,MACnBU,EAAeC,EAAgBH,EAAcT,EAAKU,EAAQ+C,kBAC1DiB,EAAcN,EAAOO,KAAK,KAAKC,QAAQ,OAAQ,IAAIA,QAAQ,SAAU,IACrEC,EAAYnE,EAAQsC,WAAa,EACvC8B,aAAgB,KACR,MAAAC,EAAiBC,UACqB,IAAlBZ,EAAOa,QAAgBb,EAAOc,OAAOC,GAAQH,EAAMG,IAAQH,EAAMI,OAASD,MAC7E,OAAAE,EAAArF,EAAIL,UAAJ0F,EAAaC,QAAA,EAGpC,OADSC,SAAAC,iBAAiB,UAAWT,GAC9B,IAAMQ,SAASE,oBAAoB,UAAWV,EAAa,GACjE,CAACX,IACJU,aAAgB,KACd,MAAMY,EAAUnB,EAAW5E,QACrBkD,EAAW7C,EAAIL,QACjB,GAAAkF,GAAaa,GAAW7C,EAAU,CACpC,MAAM8C,EAAc,KACd,IAACjF,EAAQ0C,iBAAiBzD,QAAS,CAC/B,MAAAiG,EAAa,IAAIC,YAAY7B,IACnCnB,EAASiD,cAAcF,GACvBlF,EAAQ0C,iBAAiBzD,SAAU,CAC/C,GAEcoG,EAAe,KACf,GAAArF,EAAQ0C,iBAAiBzD,QAAS,CAC9B,MAAAqG,EAAc,IAAIH,YAAY5B,IACpCpB,EAASiD,cAAcE,GACvBtF,EAAQ0C,iBAAiBzD,SAAU,CAC/C,GAEcsG,EAAwBjB,KACEU,EAAQQ,SAASlB,EAAMmB,gBACbJ,GAAA,EAEpCK,EAA2B,KACTV,EAAQQ,SAASX,SAASc,gBACdN,GAAA,EAQpC,OANQL,EAAAF,iBAAiB,UAAWG,GAC5BD,EAAAF,iBAAiB,WAAYS,GAC7BP,EAAAF,iBAAiB,cAAeG,GAChCD,EAAAF,iBAAiB,eAAgBY,GAClC9J,OAAAkJ,iBAAiB,OAAQG,GACzBrJ,OAAAkJ,iBAAiB,QAASO,GAC1B,KACGL,EAAAD,oBAAoB,UAAWE,GAC/BD,EAAAD,oBAAoB,WAAYQ,GAChCP,EAAAD,oBAAoB,cAAeE,GACnCD,EAAAD,oBAAoB,eAAgBW,GACrC9J,OAAAmJ,oBAAoB,OAAQE,GAC5BrJ,OAAAmJ,oBAAoB,QAASM,EAAY,CAE1D,IACO,CAAClB,EAAWnE,EAAQ0C,mBACvB,MAAMkD,EAA8B3C,EAAiBrC,aACnD,EAAGiF,uBACD,MACMC,EADalC,IACmBrG,KAAKwI,IACnC,MAAAC,EAAYD,EAAUzG,IAAIL,QAC1BgH,EAA0B,CAACD,KAAcE,GAAsBF,IACrE,MAA4B,aAArBH,EAAkCI,EAA0BA,EAAwBE,SAAS,IAEtG,OAA6B,aAArBN,EAAkCC,EAAmBK,UAAYL,GAAoBM,MAAM,GAErG,CAACxC,IA8BH,OA5BAQ,aAAgB,KACd,MAAMjC,EAAW7C,EAAIL,QACrB,GAAIkD,EAAU,CACN,MAAAkC,EAAiBC,cACrB,MAAM+B,EAAY/B,EAAMgC,QAAUhC,EAAMiC,SAAWjC,EAAMkC,QAEzD,GAD+B,QAAdlC,EAAMG,MAAkB4B,EAC3B,CACZ,MAAMI,EAAiB5B,SAASc,cAC1Be,EAAqBpC,EAAMqC,SAEjC,GADyBrC,EAAMsC,SAAWzE,GAClBuE,EAEtB,YADA,OAAA/B,EAAAb,EAAkB7E,UAAlB0F,EAA2BC,SAGvB,MACAiC,EAAmBjB,EAA4B,CAAEC,iBAD9Ba,EAAqB,YAAc,aAEtDI,EAAQD,EAAiBE,WAAWC,GAAcA,IAAcP,IAClEQ,GAAWJ,EAAiBvJ,MAAMwJ,EAAQ,IAC5CxC,EAAM4C,iBAEeC,EAAA,OAAAA,EAAArD,EAAkB7E,UAAlBkI,EAA2BvC,QAAU,OAAAwC,EAAArD,EAAkB9E,UAASmI,EAAAxC,OAEnG,GAGQ,OADSzC,EAAA2C,iBAAiB,UAAWT,GAC9B,IAAMlC,EAAS4C,oBAAoB,UAAWV,EAC7D,IACO,CAACT,EAAUgC,IACa9C,EAAAuE,KACzBC,EACA,CACEhI,IAAKuE,EACL0D,KAAM,SACN,aAAcxF,EAAMmC,QAAQ,WAAYF,GACxCwD,UAAU,EACVC,MAAO,CAAEC,cAAevD,OAAY,EAAS,QAC7C9E,SAAU,CACR8E,GAAgCrB,EAAAtD,IAC9BmI,GACA,CACErI,IAAKwE,EACL8D,2BAA4B,KAI1BX,GAH2BrB,EAA4B,CACrDC,iBAAkB,aAES,IAInBrG,EAAGA,IAAC+B,GAAWb,KAAM,CAAEtB,MAAO0C,EAAczC,eAA8BwI,EAAUC,GAAI,CAAEN,UAAc,KAAG7D,EAAerE,IAAKW,MAC/IkE,GAAgCrB,EAAAtD,IAC9BmI,GACA,CACErI,IAAKyE,EACL6D,2BAA4B,KAI1BX,GAH2BrB,EAA4B,CACrDC,iBAAkB,cAES,MAMxC,IAGLrC,GAAc/D,YAAc2D,GAC5B,IAAI2E,GAAmB,kBACnBJ,GAAalE,EAAgB3D,YAC/B,CAAC7B,EAAO8B,KACN,MAAM+B,aAAEA,EAAA8F,2BAAcA,KAA+BI,GAAe/J,EAC9D+B,EAAU4B,GAAwBmG,GAAkBjG,GAC1D,OAA0BgB,EAAAtD,IACxByI,EACA,CACE,eAAe,EACfT,SAAU,KACPQ,EACH1I,IAAKS,EACL0H,MAAO,CAAES,SAAU,SACnBC,QAAU7D,UACR,MAAM8D,EAAqB9D,EAAMmB,gBACG,OAAAd,EAAA3E,EAAQmC,eAAR,EAAAwC,EAAkBa,SAAS4C,KACHR,GAAA,GAGjE,IAGLD,GAAWlI,YAAcsI,GACzB,IAAIM,GAAa,QAKbC,GAAQ7E,EAAgB3D,YAC1B,CAAC7B,EAAO8B,KACA,MAAAwI,WAAEA,EAAY3K,KAAM4K,EAAAC,YAAUA,eAAanK,KAAiBoK,GAAezK,GAC1EL,EAAM+K,GAAWC,EAAqB,CAC3CC,KAAML,EACNM,YAAaL,IAAe,EAC5BM,SAAUzK,EACV0K,OAAQX,KAEa7I,aAAIyJ,EAAU,CAAEC,QAASX,GAAc3K,EAAMyB,SAA6ByD,EAAAtD,IAC/F2J,GACA,CACEvL,UACG8K,EACHpJ,IAAKS,EACLqJ,QAAS,IAAMT,GAAQ,GACvBU,QAASC,EAAerL,EAAMoL,SAC9BE,SAAUD,EAAerL,EAAMsL,UAC/BC,aAAcC,EAAqBxL,EAAMuL,cAAelF,IAChDA,EAAAoF,cAAcC,aAAa,aAAc,QAAO,IAExDC,YAAaH,EAAqBxL,EAAM2L,aAActF,IACpD,MAAMuF,EAAEA,EAAAC,EAAGA,GAAMxF,EAAMyF,OAAOC,MACxB1F,EAAAoF,cAAcC,aAAa,aAAc,QAC/CrF,EAAMoF,cAAcjC,MAAMwC,YAAY,6BAA8B,GAAGJ,OACvEvF,EAAMoF,cAAcjC,MAAMwC,YAAY,6BAA8B,GAAGH,MAAK,IAE9EI,cAAeT,EAAqBxL,EAAMiM,eAAgB5F,IAClDA,EAAAoF,cAAcC,aAAa,aAAc,UACzCrF,EAAAoF,cAAcjC,MAAM0C,eAAe,8BACnC7F,EAAAoF,cAAcjC,MAAM0C,eAAe,8BACnC7F,EAAAoF,cAAcjC,MAAM0C,eAAe,6BACnC7F,EAAAoF,cAAcjC,MAAM0C,eAAe,4BAA2B,IAEtEC,WAAYX,EAAqBxL,EAAMmM,YAAa9F,IAClD,MAAMuF,EAAEA,EAAAC,EAAGA,GAAMxF,EAAMyF,OAAOC,MACxB1F,EAAAoF,cAAcC,aAAa,aAAc,OACzCrF,EAAAoF,cAAcjC,MAAM0C,eAAe,8BACnC7F,EAAAoF,cAAcjC,MAAM0C,eAAe,8BACzC7F,EAAMoF,cAAcjC,MAAMwC,YAAY,4BAA6B,GAAGJ,OACtEvF,EAAMoF,cAAcjC,MAAMwC,YAAY,4BAA6B,GAAGH,OACtEnB,GAAQ,EAAK,OAGhB,IAGPL,GAAM7I,YAAc4I,GACpB,IAAKgC,GAA0BC,IAA8B7I,GAAmB4G,GAAY,CAC1F,OAAAe,GACF,IAEID,GAAY1F,EAAgB3D,YAC9B,CAAC7B,EAAO8B,KACA,MAAA+B,aACJA,EAAA/E,KACAA,EAAO,aACPiF,SAAUuI,EAAA3M,KACVA,EAAAwL,QACAA,EAAAoB,gBACAA,EAAAnB,QACAA,EAAAE,SACAA,EAAAC,aACAA,EAAAI,YACAA,EAAAM,cACAA,EAAAE,WACAA,KACG1B,GACDzK,EACE+B,EAAU4B,GAAwByG,GAAYvG,IAC7C2I,EAAMC,GAAWrI,WAAe,MACjCpC,EAAeC,EAAgBH,GAAe4K,GAAUD,EAAQC,KAChEC,EAAkBnI,EAAYlD,OAAC,MAC/BsL,EAAgBpI,EAAYlD,OAAC,MAC7ByC,EAAWuI,GAAgBvK,EAAQgC,SACnC8I,EAAyBrI,EAAYlD,OAAC,GACtCwL,EAA6BtI,EAAYlD,OAACyC,GAC1CgJ,EAAgBvI,EAAYlD,OAAC,IAC7ByD,WAAEA,EAAYG,iBAAkBnD,EAChCiL,EAAc3B,GAAe,YACJ,MAANmB,OAAM,EAAAA,EAAAjF,SAASX,SAASc,kBAC3B,OAAAhB,EAAA3E,EAAQmC,WAARwC,EAAkBC,SAC7BwE,GAAA,IAEL8B,EAAajI,EAAiBrC,aACjCuK,IACMA,GAAaA,IAAcC,MACzBxP,OAAAyP,aAAaL,EAAc/L,SAClC6L,EAAuB7L,SAA2B,IAAIqM,MAAQC,UAC9DP,EAAc/L,QAAUrD,OAAOgB,WAAWqO,EAAaE,GAAS,GAElE,CAACF,IAEH7G,aAAgB,KACd,MAAMjC,EAAWnC,EAAQmC,SACzB,GAAIA,EAAU,CACZ,MAAMkD,EAAe,KACnB6F,EAAWH,EAA2B9L,SACtC,MAAAsK,MAAA,EAEItE,EAAc,KAClB,MAAMuG,OAAmCF,MAAQC,UAAYT,EAAuB7L,QACzD8L,EAAA9L,QAAU8L,EAA2B9L,QAAUuM,EACnE5P,OAAAyP,aAAaL,EAAc/L,SAClC,MAAAoK,MAAA,EAIF,OAFSlH,EAAA2C,iBAAiBxB,GAAgB2B,GACjC9C,EAAA2C,iBAAiBvB,GAAiB8B,GACpC,KACIlD,EAAA4C,oBAAoBzB,GAAgB2B,GACpC9C,EAAA4C,oBAAoBxB,GAAiB8B,EAAY,CAEpE,IACO,CAACrF,EAAQmC,SAAUH,EAAUqH,EAASE,EAAU2B,IACnD9G,aAAgB,KACVxG,IAASoC,EAAQ0C,iBAAiBzD,WAAoB+C,EAAQ,GACjE,CAACpE,EAAMoE,EAAUhC,EAAQ0C,iBAAkBwI,IAC9C9G,aAAgB,KACFpB,IACL,IAAMG,MACZ,CAACH,EAAYG,IACV,MAAAsI,EAAsBC,WAAc,IACjCjB,EAAOkB,GAAuBlB,GAAQ,MAC5C,CAACA,IACA,OAACzK,EAAQmC,SACcW,EAAAuE,KAACuE,WAAU,CAAEvM,SAAU,CAChDoM,GAA0C3I,EAAAtD,IACxCqM,GACA,CACE/J,eACAyF,KAAM,SACN,YAAsB,eAATxK,EAAwB,YAAc,SACnD,eAAe,EACfsC,SAAUoM,IAGEjM,EAAGA,IAAC6K,GAA0B,CAAEjL,MAAO0C,EAAcsH,QAAS6B,EAAa5L,SAAUyM,EAAqBC,aACxGvM,EAAGA,IAAC+B,GAAWZ,SAAU,CAAEvB,MAAO0C,EAAczC,SAA6ByD,EAAAtD,IAC3FwM,EACA,CACEC,SAAS,EACTzB,gBAAiBf,EAAqBe,GAAiB,KAChDxK,EAAQwC,+BAA+BvD,SAAsBgM,IAClEjL,EAAQwC,+BAA+BvD,SAAU,KAEnDI,SAA6ByD,EAAAtD,IAC3BqI,EAAUqE,GACV,CACE3E,KAAM,SACN,YAAa,MACb,eAAe,EACfC,SAAU,EACV,aAAc5J,EAAO,OAAS,SAC9B,uBAAwBoC,EAAQiC,kBAC7ByG,EACHpJ,IAAKW,EACLwH,MAAO,CAAE0E,WAAY,OAAQC,YAAa,UAAWnO,EAAMwJ,OAC3D4E,UAAW5C,EAAqBxL,EAAMoO,WAAY/H,IAC9B,WAAdA,EAAMG,MACV,MAAA+F,KAAkBlG,EAAMgI,aACnBhI,EAAMgI,YAAYC,mBACrBvM,EAAQwC,+BAA+BvD,SAAU,EACpCgM,KACjC,IAEgBuB,cAAe/C,EAAqBxL,EAAMuO,eAAgBlI,IACnC,IAAjBA,EAAMmI,SACV7B,EAAgB3L,QAAU,CAAE4K,EAAGvF,EAAMoI,QAAS5C,EAAGxF,EAAMqI,SAAS,IAElEC,cAAenD,EAAqBxL,EAAM2O,eAAgBtI,IACpD,IAACsG,EAAgB3L,QAAS,OAC9B,MAAM4K,EAAIvF,EAAMoI,QAAU9B,EAAgB3L,QAAQ4K,EAC5CC,EAAIxF,EAAMqI,QAAU/B,EAAgB3L,QAAQ6K,EAC5C+C,EAAsBC,QAAQjC,EAAc5L,SAC5C8N,EAAoB,CAAC,OAAQ,SAASC,SAAShN,EAAQiC,gBACvDgL,EAAQ,CAAC,OAAQ,MAAMD,SAAShN,EAAQiC,gBAAkBiL,KAAKC,IAAMD,KAAKE,IAC1EC,EAAWN,EAAoBE,EAAM,EAAGpD,GAAK,EAC7CyD,EAAYP,EAAkC,EAAdE,EAAM,EAAGnD,GACzCyD,EAAwC,UAAtBjJ,EAAMkJ,YAA0B,GAAK,EACvDxD,EAAQ,CAAEH,EAAGwD,EAAUvD,EAAGwD,GAC1BG,EAAc,CAAEC,cAAepJ,EAAO0F,SACxC6C,GACFhC,EAAc5L,QAAU+K,EACK2D,GA1L1B,kBA0L4C/D,EAAa6D,EAAa,CACvEG,UAAU,KAEHC,GAAmB7D,EAAOhK,EAAQiC,eAAgBsL,IAC3D1C,EAAc5L,QAAU+K,EACK2D,GAhMzB,mBAgM4CnE,EAAciE,EAAa,CACzEG,UAAU,IAENtJ,EAAAsC,OAAOkH,kBAAkBxJ,EAAMyJ,aAC5Bb,KAAKc,IAAInE,GAAK0D,GAAmBL,KAAKc,IAAIlE,GAAKyD,KACxD3C,EAAgB3L,QAAU,KAC9C,IAEgBgP,YAAaxE,EAAqBxL,EAAMgQ,aAAc3J,IACpD,MAAM0F,EAAQa,EAAc5L,QACtB2H,EAAStC,EAAMsC,OAMrB,GALIA,EAAOsH,kBAAkB5J,EAAMyJ,YAC1BnH,EAAAuH,sBAAsB7J,EAAMyJ,WAErClD,EAAc5L,QAAU,KACxB2L,EAAgB3L,QAAU,KACtB+K,EAAO,CACT,MAAM3M,EAAQiH,EAAMoF,cACd+D,EAAc,CAAEC,cAAepJ,EAAO0F,SACxC6D,GAAmB7D,EAAOhK,EAAQiC,eAAgBjC,EAAQkC,gBAC/ByL,GAjN7B,iBAiN8CvD,EAAYqD,EAAa,CACrEG,UAAU,IAGZD,GAtNG,oBAwNDzD,EACAuD,EACA,CACEG,UAAU,IAIhBvQ,EAAMyH,iBAAiB,SAAUsJ,GAAWA,EAAOlH,kBAAkB,CACnEmH,MAAM,GAE5B,WAMQrO,EAAQmC,eA1GkB,IA4G3B,IAGH0J,GAAiB5N,IACnB,MAAM6D,aAAEA,EAAAzC,SAAcA,KAAaiP,GAAkBrQ,EAC/C+B,EAAU4B,GAAwByG,GAAYvG,IAC7CyM,EAAoBC,GAAyBnM,YAAe,IAC5DoM,EAAaC,GAAkBrM,YAAe,GAM9C,OAyGT,SAAsBsM,EAAW,QAEzB,MAAAC,EAAKtF,EAAeqF,GAC1BE,GAAgB,KACd,IAAIC,EAAO,EACPC,EAAO,EAEX,OADAD,EAAOlT,OAAOoT,uBAAsB,IAAMD,EAAOnT,OAAOoT,sBAAsBJ,KACvE,KACLhT,OAAOqT,qBAAqBH,GAC5BlT,OAAOqT,qBAAqBF,EAAI,CACjC,GACA,CAACH,GACN,CA1HeM,EAAA,IAAMV,GAAsB,KACzCpK,aAAgB,KACd,MAAM+K,EAAQvT,OAAOgB,YAAW,IAAM8R,GAAe,IAAO,KACrD,UAAM9S,OAAOyP,aAAa8D,EAAK,GACrC,IACIV,EAAc,KAA0B3L,EAAAtD,IAAC4P,EAAQ,CAAEnD,SAAS,EAAM5M,SAA0BG,MAAIyI,EAAgB,IAAKqG,EAAejP,SAAUkP,UAA2C3C,WAAU,CAAEvM,SAAU,CACpNW,EAAQ+B,MACR,IACA1C,QACO,EAGPgQ,GAAa5L,EAAgB3D,YAC/B,CAAC7B,EAAO8B,KACN,MAAM+B,aAAEA,KAAiBwN,GAAerR,EACjBuB,aAAIqI,EAAU0H,IAAK,IAAKD,EAAYhQ,IAAKS,GAAc,IAGlFsP,GAAW5P,YAPM,aAQjB,IACI+P,GAAmB/L,EAAgB3D,YACrC,CAAC7B,EAAO8B,KACN,MAAM+B,aAAEA,KAAiB2N,GAAqBxR,EACvBuB,aAAIqI,EAAU0H,IAAK,IAAKE,EAAkBnQ,IAAKS,GAAc,IAGxFyP,GAAiB/P,YAPM,mBAQvB,IAAIiQ,GAAc,cACdC,GAAclM,EAAgB3D,YAChC,CAAC7B,EAAO8B,KACN,MAAM6P,QAAEA,KAAYC,GAAgB5R,EAChC,OAAC2R,EAAQjN,SAMUnD,IAAIsQ,GAAsB,CAAEF,UAAS3D,SAAS,EAAM5M,SAA0BG,EAAGA,IAACuQ,GAAY,IAAKF,EAAavQ,IAAKS,OALlI6C,QAAAC,MACN,0CAA0C6M,wCAErC,KAEoJ,IAGjKC,GAAYlQ,YAAciQ,GAC1B,IAAIM,GAAa,aACbD,GAAatM,EAAgB3D,YAC/B,CAAC7B,EAAO8B,KACN,MAAM+B,aAAEA,KAAiBmO,GAAehS,EAClCiS,EAAqB5F,GAA2B0F,GAAYlO,GAClE,SAAuBtC,IAAIsQ,GAAsB,CAAE7D,SAAS,EAAM5M,SAA6ByD,EAAAtD,IAC7FqI,EAAU4E,OACV,CACE1P,KAAM,YACHkT,EACH3Q,IAAKS,EACLoQ,QAAS1G,EAAqBxL,EAAMkS,QAASD,EAAmB9G,YAEjE,IAGP2G,GAAWtQ,YAAcuQ,GACzB,IAAIF,GAAuBrM,EAAgB3D,YAAC,CAAC7B,EAAO8B,KAClD,MAAM+B,aAAEA,EAAA8N,QAAcA,KAAYQ,GAAyBnS,EAC3D,OAA0B6E,EAAAtD,IACxBqI,EAAU0H,IACV,CACE,oCAAqC,GACrC,gCAAiCK,QAAW,KACzCQ,EACH9Q,IAAKS,GAER,IAEH,SAAS4L,GAAuB0E,GAC9B,MAAMC,EAAc,GAiBb,OAhBYvP,MAAMC,KAAKqP,EAAUE,YAC7B7S,SAAS+M,IAEd,GADAA,EAAK+F,WAAa/F,EAAKgG,WAAahG,EAAK6F,aAAaA,EAAYI,KAAKjG,EAAK6F,aAiDpF,SAAuB7F,GACd,OAAAA,EAAK+F,WAAa/F,EAAKkG,YAChC,CAlDQC,CAAcnG,GAAO,CACvB,MAAMoG,EAAWpG,EAAKqG,YAAcrG,EAAKsG,QAAiC,SAAvBtG,EAAKhD,MAAMuJ,QACxDC,EAAwD,KAA3CxG,EAAKyG,QAAQC,0BAChC,IAAKN,EACH,GAAII,EAAY,CACR,MAAArB,EAAUnF,EAAKyG,QAAQE,sBACzBxB,GAAqBU,EAAAI,KAAKd,EACxC,MACUU,EAAYI,QAAQ/E,GAAuBlB,GAGrD,KAES6F,CACT,CACA,SAAS3C,GAA6BlP,EAAM4S,EAAStH,GAAQ6D,SAAEA,IACvD,MAAAlE,EAAgBK,EAAO2D,cAAchE,cACrCpF,EAAQ,IAAIa,YAAY1G,EAAM,CAAE6S,SAAS,EAAMC,YAAY,EAAMxH,WACnEsH,KAAuBvM,iBAAiBrG,EAAM4S,EAAS,CAAEhD,MAAM,IAC/DT,EACF4D,EAA4B9H,EAAepF,GAE3CoF,EAActE,cAAcd,EAEhC,CACA,IAAIuJ,GAAqB,CAAC7D,EAAOyH,EAAWC,EAAY,KACtD,MAAMC,EAASzE,KAAKc,IAAIhE,EAAMH,GACxB+H,EAAS1E,KAAKc,IAAIhE,EAAMF,GACxB+H,EAAWF,EAASC,EACtB,MAAc,SAAdH,GAAsC,UAAdA,EACnBI,GAAYF,EAASD,GAEpBG,GAAYD,EAASF,CACjC,EAkBA,SAASxL,GAAsBmK,GAC7B,MAAMyB,EAAQ,GACRC,EAASlN,SAASmN,iBAAiB3B,EAAW4B,WAAWC,aAAc,CAC3EC,WAAa1H,IACX,MAAM2H,EAAiC,UAAjB3H,EAAK4H,SAAqC,WAAd5H,EAAK1N,KACvD,OAAI0N,EAAK6H,UAAY7H,EAAKsG,QAAUqB,EAAsBH,WAAWM,YAC9D9H,EAAKjD,UAAY,EAAIyK,WAAWO,cAAgBP,WAAWM,WAAA,IAGtE,KAAOR,EAAOU,YAAkBX,EAAApB,KAAKqB,EAAOW,aACrC,OAAAZ,CACT,CACA,SAAS7K,GAAW0L,GAClB,MAAMC,EAA2B/N,SAASc,cACnC,OAAAgN,EAAWE,MAAM7L,GAClBA,IAAc4L,IAClB5L,EAAUpC,QACHC,SAASc,gBAAkBiN,IAEtC,CACA,IAAInS,GAAWoB,GACXiR,GAAWtP,GACXuP,GAAQzK,GACR0K,GAAQ3D,GACR4D,GAAczD,GACd0D,GAASvD,GACTwD,GAAQpD,GCxmBZ,MAAMqD,GAAiBC,GAAyB,kBAAVA,EAAsB,GAAGA,IAAoB,IAAVA,EAAc,IAAMA,EAChFC,GAAKC,EACLC,GAAM,CAACC,EAAMC,IAAUzV,IACxB,IAAA0V,EACC,GAAoE,OAApED,aAAuC,EAASA,EAAOE,UAA0B,OAAAN,GAAGG,EAAMxV,aAAqC,EAASA,EAAM4V,MAAO5V,aAAqC,EAASA,EAAM6V,WACxM,MAAAF,SAAEA,EAAUG,mBAAoBL,EAChCM,EAAuBC,OAAOC,KAAKN,GAAUrW,KAAK4W,IACpD,MAAMC,EAAcnW,aAAqC,EAASA,EAAMkW,GAClEE,EAAqBN,aAAyD,EAASA,EAAgBI,GACzG,GAAgB,OAAhBC,EAA6B,YACjC,MAAME,EAAalB,GAAcgB,IAAgBhB,GAAciB,GACxD,OAAAT,EAASO,GAASG,EAAU,IAEjCC,EAAwBtW,GAASgW,OAAOO,QAAQvW,GAAOwW,QAAO,CAACC,EAAKC,KAClE,IAAClQ,EAAK4O,GAASsB,EACnB,YAAc,IAAVtB,IAGJqB,EAAIjQ,GAAO4O,GAFAqB,CAGJ,GACR,IACGE,EAA+BlB,SAAyG,QAAxDC,EAA2BD,EAAOmB,wBAA2D,IAA7BlB,OAA1E,EAAyHA,EAAyBc,QAAO,CAACC,EAAKC,KACvO,IAAMd,MAAOiB,EAAShB,UAAWiB,KAAgBC,GAA2BL,EAC5E,OAAOV,OAAOO,QAAQQ,GAAwBxQ,OAAOmQ,IAC7C,IAAClQ,EAAK4O,GAASsB,EACnB,OAAO5T,MAAMkU,QAAQ5B,GAASA,EAAMrG,SAAS,IACtC+G,KACAQ,GACL9P,IAAS,IACJsP,KACAQ,GACJ9P,KAAS4O,CAAA,IACX,IACEqB,EACHI,EACAC,GACAL,CAAA,GACL,IACH,OAAOpB,GAAGG,EAAMO,EAAsBY,EAA8B3W,aAAqC,EAASA,EAAM4V,MAAO5V,aAAqC,EAASA,EAAM6V,UAAS,ECpD9LoB,GAAkCxB,IAChC,MAAAyB,EAAWC,GAAe1B,IAC1B2B,uBACJA,EAAAC,+BACAA,GACE5B,EAgBG,OACL6B,gBAhBmCzB,IAC7B,MAAA0B,EAAa1B,EAAU2B,MARJ,KAazB,MAHsB,KAAlBD,EAAW,IAAmC,IAAtBA,EAAWjR,QACrCiR,EAAWE,QAENC,GAAkBH,EAAYL,IAAaS,GAA+B9B,EAAS,EAW1F+B,4BATkC,CAACC,EAAcC,KACjD,MAAMC,EAAYX,EAAuBS,IAAiB,GACtD,OAAAC,GAAsBT,EAA+BQ,GAChD,IAAIE,KAAcV,EAA+BQ,IAEnDE,CAAA,EAKR,EAEGL,GAAoB,CAACH,EAAYS,WACjC,GAAsB,IAAtBT,EAAWjR,OACb,OAAO0R,EAAgBH,aAEnB,MAAAI,EAAmBV,EAAW,GAC9BW,EAAsBF,EAAgBG,SAASC,IAAIH,GACnDI,EAA8BH,EAAsBR,GAAkBH,EAAWlY,MAAM,GAAI6Y,QAAuB,EACxH,GAAIG,EACK,OAAAA,EAEL,GAAsC,IAAtCL,EAAgBM,WAAWhS,OACtB,OAEH,MAAAiS,EAAYhB,EAAWvR,KAxCF,KAyC3B,OAAO,OAAAU,EAAAsR,EAAgBM,WAAWE,MAAK,EACrCC,eACIA,EAAUF,aAFT7R,EAEsBmR,YAAA,EAEzBa,GAAyB,aACzBf,GAA8C9B,IAC9C,GAAA6C,GAAuBC,KAAK9C,GAAY,CAC1C,MAAM+C,EAA6BF,GAAuBG,KAAKhD,GAAW,GACpEiD,EAAuC,MAA5BF,OAA4B,EAAAA,EAAAG,UAAU,EAAGH,EAA2BvV,QAAQ,MAC7F,GAAIyV,EAEF,MAAO,cAAgBA,CAE7B,GAKM3B,GAA2B1B,IACzB,MACJuD,eACAC,GACExD,EACEyB,EAAW,CACfiB,aAAc7Z,IACdga,WAAY,IAMP,OAJ2BY,GAA6BlD,OAAOO,QAAQd,EAAO0D,aAAcF,GACzExZ,SAAQ,EAAEoY,EAAcuB,MACtBC,GAAAD,EAAYlC,EAAUW,EAAcmB,EAAK,IAE9D9B,CAAA,EAEHmC,GAA4B,CAACD,EAAYpB,EAAiBH,EAAcmB,KAC5EI,EAAW3Z,SAA2B6Z,IAChC,GAA2B,iBAApBA,EAAP,CAKA,GAA2B,mBAApBA,EACL,OAAAC,GAAcD,QAChBD,GAA0BC,EAAgBN,GAAQhB,EAAiBH,EAAcmB,QAGnFhB,EAAgBM,WAAW7F,KAAK,CAC9BgG,UAAWa,EACXzB,iBAIG7B,OAAAO,QAAQ+C,GAAiB7Z,SAAQ,EAAE+G,EAAK4S,MAC7CC,GAA0BD,EAAYI,GAAQxB,EAAiBxR,GAAMqR,EAAcmB,EAAK,GAb9F,KAJQ,EACgD,KAApBM,EAAyBtB,EAAkBwB,GAAQxB,EAAiBsB,IAC5EzB,aAAeA,CAE3C,CAcK,GACF,EAEG2B,GAAU,CAACxB,EAAiByB,KAChC,IAAIC,EAAyB1B,EAUtB,OATPyB,EAAKjC,MAnGsB,KAmGM/X,SAAoBka,IAC9CD,EAAuBvB,SAAS1Z,IAAIkb,IAChBD,EAAAvB,SAASpZ,IAAI4a,EAAU,CAC5CxB,aAAc7Z,IACdga,WAAY,KAGSoB,IAAuBvB,SAASC,IAAIuB,EAAQ,IAEhED,CAAA,EAEHH,MAAwBK,EAAKL,cAC7BL,GAA+B,CAACW,EAAmBZ,IAClDA,EAGEY,EAAkBva,KAAI,EAAEuY,EAAcuB,KAUpC,CAACvB,EATmBuB,EAAW9Z,KAAuBga,GAC5B,iBAApBA,EACFL,EAASK,EAEa,iBAApBA,EACFtD,OAAO8D,YAAY9D,OAAOO,QAAQ+C,GAAiBha,KAAI,EAAEkH,EAAK4O,KAAW,CAAC6D,EAASzS,EAAK4O,MAE1FkE,OAVFO,EAiBLE,GAAiCC,IACrC,GAAIA,EAAe,EACV,OACL5B,IAAK,KAAM,EACXrZ,IAAK,QAGT,IAAIkb,EAAY,EACZC,MAAY5b,IACZ6b,MAAoB7b,IAClB,MAAAgC,EAAS,CAACkG,EAAK4O,KACb8E,EAAAnb,IAAIyH,EAAK4O,GACf6E,IACIA,EAAYD,IACFC,EAAA,EACIE,EAAAD,EAChBA,MAAY5b,IAClB,EAES,OACL,GAAA8Z,CAAI5R,GACE,IAAA4O,EAAQ8E,EAAM9B,IAAI5R,GACtB,YAAc,IAAV4O,EACKA,OAEgC,KAApCA,EAAQ+E,EAAc/B,IAAI5R,KAC7BlG,EAAOkG,EAAK4O,GACLA,QAFT,CAID,EACD,GAAArW,CAAIyH,EAAK4O,GACH8E,EAAMzb,IAAI+H,GACN0T,EAAAnb,IAAIyH,EAAK4O,GAEf9U,EAAOkG,EAAK4O,EAEpB,EACG,EAGGgF,GAAiC3E,IAC/B,MAAA4E,UACJA,EAAAC,2BACAA,GACE7E,EACE8E,EAAkD,IAArBF,EAAU/T,OACvCkU,EAA0BH,EAAU,GACpCI,EAAkBJ,EAAU/T,OAE5BoU,EAA8B7E,IAClC,MAAM8E,EAAY,GAClB,IAEIC,EAFAC,EAAe,EACfC,EAAgB,EAEpB,QAASjS,EAAQ,EAAGA,EAAQgN,EAAUvP,OAAQuC,IAAS,CACjD,IAAAkS,EAAmBlF,EAAUhN,GACjC,GAAqB,IAAjBgS,EAAoB,CAClB,GAAAE,IAAqBP,IAA4BD,GAA8B1E,EAAUxW,MAAMwJ,EAAOA,EAAQ4R,KAAqBJ,GAAY,CACjJM,EAAUlI,KAAKoD,EAAUxW,MAAMyb,EAAejS,IAC9CiS,EAAgBjS,EAAQ4R,EACxB,QACV,CACQ,GAAyB,MAArBM,EAA0B,CACFH,EAAA/R,EAC1B,QACV,CACA,CAC+B,MAArBkS,EACFF,IAC8B,MAArBE,GACTF,GAER,CACI,MAAMG,EAA0D,IAArBL,EAAUrU,OAAeuP,EAAYA,EAAUkD,UAAU+B,GAC9FG,EAAuBD,EAAmCE,WAnCzC,KAsChB,OACLP,YACAM,uBACAE,cALoBF,EAAuBD,EAAmCjC,UAAU,GAAKiC,EAM7FI,6BALmCR,GAA2BA,EAA0BE,EAAgBF,EAA0BE,OAAgB,EAMnJ,EAEH,OAAIR,KACkBA,EAA2B,CAC7CzE,YACA6E,mBAGGA,CAAA,EAOHW,GAA6BV,IAC7B,GAAAA,EAAUrU,QAAU,EACf,OAAAqU,EAET,MAAMW,EAAkB,GACxB,IAAIC,EAAoB,GAWjB,OAVPZ,EAAUlb,SAAoB+b,IACe,MAAhBA,EAAS,IAElCF,EAAgB7I,QAAQ8I,EAAkBrY,OAAQsY,GAClDD,EAAoB,IAEpBA,EAAkB9I,KAAK+I,EAC7B,IAEEF,EAAgB7I,QAAQ8I,EAAkBrY,QACnCoY,CAAA,EAOHG,GAAsB,MAqE5B,SAASC,KACP,IACIC,EACAC,EAFA/S,EAAQ,EAGRgT,EAAS,GACN,KAAAhT,EAAQiT,UAAUxV,SACnBqV,EAAWG,UAAUjT,QACnB+S,EAAgBG,GAAQJ,MAC1BE,IAAWA,GAAU,KACXA,GAAAD,GAIT,OAAAC,CACT,CACA,MAAME,GAAiBC,IACjB,GAAe,iBAARA,EACF,OAAAA,EAEL,IAAAJ,EACAC,EAAS,GACb,QAASI,EAAI,EAAGA,EAAID,EAAI1V,OAAQ2V,IAC1BD,EAAIC,KACFL,EAAgBG,GAAQC,EAAIC,OAC9BJ,IAAWA,GAAU,KACXA,GAAAD,GAIT,OAAAC,CAAA,EAET,SAASK,GAAoBC,KAAsBC,GAC7C,IAAAC,EACAC,EACAC,EACAC,EACJ,SAA2BC,GACnB,MAAAhH,EAAS2G,EAAiB5F,QAAO,CAACkG,EAAgBC,IAAwBA,EAAoBD,IAAiBP,KAKrH,OAJAE,EAhHsB,CAAW5G,IAAA,CACnCyE,MAAOH,GAAetE,EAAOwE,WAC7BS,eAAgBN,GAAqB3E,MAClCwB,GAAsBxB,KA6GTmH,CAAkBnH,GAChC6G,EAAWD,EAAYnC,MAAM9B,IAC7BmE,EAAWF,EAAYnC,MAAMnb,IACZyd,EAAAK,EACVA,EAAcJ,EACzB,EACE,SAASI,EAAcJ,GACf,MAAAK,EAAeR,EAASG,GAC9B,GAAIK,EACK,OAAAA,EAEH,MAAAC,EArHa,EAACN,EAAWJ,KAC3B,MAAA3B,eACJA,EAAApD,gBACAA,EAAAM,4BACAA,GACEyE,EAQEW,EAAwB,GACxBC,EAAaR,EAAU/X,OAAO8S,MAAMiE,IAC1C,IAAIsB,EAAS,GACb,QAASlU,EAAQoU,EAAW3W,OAAS,EAAGuC,GAAS,EAAGA,GAAS,EAAG,CACxD,MAAAqU,EAAoBD,EAAWpU,IAC/B8R,UACJA,EAAAM,qBACAA,EAAAE,cACAA,EAAAC,6BACAA,GACEV,EAAewC,GACf,IAAApF,EAAqBjJ,QAAQuM,GAC7BvD,EAAeP,EAAgBQ,EAAqBqD,EAAcpC,UAAU,EAAGqC,GAAgCD,GACnH,IAAKtD,EAAc,CACjB,IAAKC,EAAoB,CAEvBiF,EAASG,GAAqBH,EAAOzW,OAAS,EAAI,IAAMyW,EAASA,GACjE,QACR,CAEM,GADAlF,EAAeP,EAAgB6D,IAC1BtD,EAAc,CAEjBkF,EAASG,GAAqBH,EAAOzW,OAAS,EAAI,IAAMyW,EAASA,GACjE,QACR,CAC2BjF,GAAA,CAC3B,CACI,MAAMqF,EAAkB9B,GAAcV,GAAW3U,KAAK,KAChDoX,EAAanC,EAAuBkC,EA3HnB,IA2H0DA,EAC3EE,EAAUD,EAAavF,EACzB,GAAAmF,EAAsBjO,SAASsO,GAEjC,SAEFL,EAAsBvK,KAAK4K,GACrB,MAAAC,EAAiB1F,EAA4BC,EAAcC,GACjE,QAASyF,EAAI,EAAGA,EAAID,EAAehX,SAAUiX,EAAG,CACxC,MAAAC,EAAQF,EAAeC,GACPP,EAAAvK,KAAK2K,EAAaI,EAC9C,CAEIT,EAASG,GAAqBH,EAAOzW,OAAS,EAAI,IAAMyW,EAASA,EACrE,CACS,OAAAA,CAAA,EA6DUU,CAAehB,EAAWJ,GAElC,OADPE,EAASE,EAAWM,GACbA,CACX,CACE,OAAO,WACL,OAAOP,EAAed,GAAOgC,MAAM,KAAM5B,WAC1C,CACH,CACA,MAAM6B,GAAmBnX,IACvB,MAAMoX,EAAc5E,GAASA,EAAMxS,IAAQ,GAEpC,OADPoX,EAAYrE,eAAgB,EACrBqE,CAAA,EAEHC,GAAsB,6BACtBC,GAAgB,aAChBC,GAAiC,IAAAC,IAAI,CAAC,KAAM,OAAQ,WACpDC,GAAkB,mCAClBC,GAAkB,4HAClBC,GAAqB,2CAErBC,GAAc,kEACdC,GAAa,+FACbC,GAAoBlJ,GAAAmJ,GAASnJ,IAAU2I,GAActf,IAAI2W,IAAU0I,GAAcnF,KAAKvD,GACtFoJ,GAAoBpJ,GAASqJ,GAAoBrJ,EAAO,SAAUsJ,IAClEH,GAAoBnJ,GAAAvG,QAAQuG,KAAWnV,OAAO0e,MAAM1e,OAAOmV,IAC3DwJ,GAAoBxJ,GAASqJ,GAAoBrJ,EAAO,SAAUmJ,IAClEM,MAAqBhQ,QAAQuG,IAAUnV,OAAO4e,UAAU5e,OAAOmV,IAC/D0J,GAAqB1J,KAAM2J,SAAS,MAAQR,GAASnJ,EAAM/V,MAAM,GAAG,IACpE2f,GAAmB5J,GAASyI,GAAoBlF,KAAKvD,GACrD6J,GAAe7J,GAAS6I,GAAgBtF,KAAKvD,GAC7C8J,GAA8B,IAAAlB,IAAI,CAAC,SAAU,OAAQ,eACrDmB,GAAkB/J,GAASqJ,GAAoBrJ,EAAO8J,GAAYE,IAClEC,GAAsBjK,GAASqJ,GAAoBrJ,EAAO,WAAYgK,IACtEE,GAA+B,IAAAtB,IAAI,CAAC,QAAS,QAC7CuB,GAAmBnK,GAASqJ,GAAoBrJ,EAAOkK,GAAaE,IACpEC,GAAoBrK,GAASqJ,GAAoBrJ,EAAO,GAAIsK,IAC5DC,GAAQ,KAAM,EACdlB,GAAsB,CAACrJ,EAAOtR,EAAO8b,KACnC,MAAA7C,EAASc,GAAoBhF,KAAKzD,GACxC,QAAI2H,IACEA,EAAO,GACe,iBAAVjZ,EAAqBiZ,EAAO,KAAOjZ,EAAQA,EAAMrF,IAAIse,EAAO,IAErE6C,EAAU7C,EAAO,IAEnB,EAEH2B,GAAetJ,GAIrB8I,GAAgBvF,KAAKvD,KAAW+I,GAAmBxF,KAAKvD,GAClDgK,GAAU,KAAM,EAChBM,GAAWtK,GAASgJ,GAAYzF,KAAKvD,GACrCoK,GAAUpK,GAASiJ,GAAW1F,KAAKvD,GAslEnCyK,OAnkEmB,KACjB,MAAAC,EAASnC,GAAU,UACnBoC,EAAUpC,GAAU,WACpBqC,EAAOrC,GAAU,QACjBsC,EAAatC,GAAU,cACvBuC,EAAcvC,GAAU,eACxBwC,EAAexC,GAAU,gBACzByC,EAAgBzC,GAAU,iBAC1B0C,EAAc1C,GAAU,eACxB2C,EAAW3C,GAAU,YACrB4C,EAAY5C,GAAU,aACtB6C,EAAY7C,GAAU,aACtB8C,EAAS9C,GAAU,UACnB+C,EAAM/C,GAAU,OAChBgD,EAAqBhD,GAAU,sBAC/BiD,EAA6BjD,GAAU,8BACvCkD,EAAQlD,GAAU,SAClBmD,EAASnD,GAAU,UACnBoD,EAAUpD,GAAU,WACpBqD,EAAUrD,GAAU,WACpBsD,EAAWtD,GAAU,YACrBuD,EAAQvD,GAAU,SAClBwD,EAAQxD,GAAU,SAClByD,EAAOzD,GAAU,QACjB0D,EAAQ1D,GAAU,SAClB2D,EAAY3D,GAAU,aAGtB4D,EAAiC,IAAM,CAAC,OAAQvC,GAAkBe,GAClEyB,EAA0B,IAAM,CAACxC,GAAkBe,GACnD0B,EAAiC,IAAM,CAAC,GAAInD,GAAUE,IACtDkD,EAAgC,IAAM,CAAC,OAAQnD,GAAUS,IAKzD2C,EAAkB,IAAM,CAAC,GAAI,IAAK3C,IAElC4C,EAAwB,IAAM,CAACrD,GAAUS,IACxC,OACL/E,UAAW,IACXI,UAAW,IACXrB,MAAO,CACL8G,OAAQ,CAACH,IACTI,QAAS,CAACzB,GAAUE,IACpBwB,KAAM,CAAC,OAAQ,GAAIf,GAAcD,IACjCiB,WAAY2B,IACZ1B,YAAa,CAACJ,GACdK,aAAc,CAAC,OAAQ,GAAI,OAAQlB,GAAcD,IACjDoB,cAAeoB,IACfnB,YAAaoB,IACbnB,SAAUsB,IACVrB,UAAWoB,IACXnB,UAAWoB,IACXnB,OAAQkB,IACRjB,IAAKc,IACLb,mBAAoB,CAACb,GACrBc,2BAA4B,CAAC9B,GAAWN,IACxCqC,MAAOU,IACPT,OAAQS,IACRR,QAASa,IACTZ,QAASQ,IACTP,SAAUW,IACVV,MAAOU,IACPT,MAAOQ,IACPP,KAAMQ,IACNP,MAAOG,IACPF,UAAWE,KAEbrI,YAAa,CAMX0I,OAAQ,CAAC,CACPA,OAAQ,CAAC,OAAQ,SAAU,QAAS7C,MAMtC5M,UAAW,CAAC,aAKZ0P,QAAS,CAAC,CACRA,QAAS,CAAC7C,MAMZ,cAAe,CAAC,CACd,cA1DkB,CAAC,OAAQ,QAAS,MAAO,aAAc,OAAQ,OAAQ,QAAS,YAgEpF,eAAgB,CAAC,CACf,eAjEkB,CAAC,OAAQ,QAAS,MAAO,aAAc,OAAQ,OAAQ,QAAS,YAuEpF,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQ,QAAS,aAAc,kBAMlD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,QAAS,WAM9B8C,IAAK,CAAC,CACJA,IAAK,CAAC,SAAU,aAMlBhP,QAAS,CAAC,QAAS,eAAgB,SAAU,OAAQ,cAAe,QAAS,eAAgB,gBAAiB,aAAc,eAAgB,qBAAsB,qBAAsB,qBAAsB,kBAAmB,YAAa,YAAa,OAAQ,cAAe,WAAY,YAAa,UAK3SiP,MAAO,CAAC,CACNA,MAAO,CAAC,QAAS,OAAQ,OAAQ,QAAS,SAM5CC,MAAO,CAAC,CACNA,MAAO,CAAC,OAAQ,QAAS,OAAQ,OAAQ,QAAS,SAMpDC,UAAW,CAAC,UAAW,kBAKvB,aAAc,CAAC,CACbC,OAAQ,CAAC,UAAW,QAAS,OAAQ,OAAQ,gBAM/C,kBAAmB,CAAC,CAClBA,OAAQ,CAjIc,SAAU,SAAU,OAAQ,cAAe,WAAY,QAAS,eAAgB,YAAa,MAiIvFnD,MAM9BoD,SAAU,CAAC,CACTA,SA7IoB,CAAC,OAAQ,SAAU,OAAQ,UAAW,YAmJ5D,aAAc,CAAC,CACb,aApJoB,CAAC,OAAQ,SAAU,OAAQ,UAAW,YA0J5D,aAAc,CAAC,CACb,aA3JoB,CAAC,OAAQ,SAAU,OAAQ,UAAW,YAiK5DC,WAAY,CAAC,CACXA,WAnKsB,CAAC,OAAQ,UAAW,UAyK5C,eAAgB,CAAC,CACf,eA1KsB,CAAC,OAAQ,UAAW,UAgL5C,eAAgB,CAAC,CACf,eAjLsB,CAAC,OAAQ,UAAW,UAuL5CpY,SAAU,CAAC,SAAU,QAAS,WAAY,WAAY,UAKtD4W,MAAO,CAAC,CACNA,MAAO,CAACA,KAMV,UAAW,CAAC,CACV,UAAW,CAACA,KAMd,UAAW,CAAC,CACV,UAAW,CAACA,KAMdyB,MAAO,CAAC,CACNA,MAAO,CAACzB,KAMV0B,IAAK,CAAC,CACJA,IAAK,CAAC1B,KAMR2B,IAAK,CAAC,CACJA,IAAK,CAAC3B,KAMR4B,MAAO,CAAC,CACNA,MAAO,CAAC5B,KAMV6B,OAAQ,CAAC,CACPA,OAAQ,CAAC7B,KAMX8B,KAAM,CAAC,CACLA,KAAM,CAAC9B,KAMT+B,WAAY,CAAC,UAAW,YAAa,YAKrCC,EAAG,CAAC,CACFA,EAAG,CAAC,OAAQhE,GAAWG,MAOzB8D,MAAO,CAAC,CACNA,MAAOvB,MAMT,iBAAkB,CAAC,CACjBwB,KAAM,CAAC,MAAO,cAAe,MAAO,iBAMtC,YAAa,CAAC,CACZA,KAAM,CAAC,OAAQ,eAAgB,YAMjCA,KAAM,CAAC,CACLA,KAAM,CAAC,IAAK,OAAQ,UAAW,OAAQ/D,MAMzCgE,KAAM,CAAC,CACLA,KAAMrB,MAMRsB,OAAQ,CAAC,CACPA,OAAQtB,MAMVuB,MAAO,CAAC,CACNA,MAAO,CAAC,QAAS,OAAQ,OAAQrE,GAAWG,MAM9C,YAAa,CAAC,CACZ,YAAa,CAACW,MAMhB,gBAAiB,CAAC,CAChBwD,IAAK,CAAC,OAAQ,CACZC,KAAM,CAAC,OAAQvE,GAAWG,KACzBA,MAML,YAAa,CAAC,CACZ,YAAa0C,MAMf,UAAW,CAAC,CACV,UAAWA,MAMb,YAAa,CAAC,CACZ,YAAa,CAAC/B,MAMhB,gBAAiB,CAAC,CAChB0D,IAAK,CAAC,OAAQ,CACZD,KAAM,CAACvE,GAAWG,KACjBA,MAML,YAAa,CAAC,CACZ,YAAa0C,MAMf,UAAW,CAAC,CACV,UAAWA,MAMb,YAAa,CAAC,CACZ,YAAa,CAAC,MAAO,MAAO,QAAS,YAAa,eAMpD,YAAa,CAAC,CACZ,YAAa,CAAC,OAAQ,MAAO,MAAO,KAAM1C,MAM5C,YAAa,CAAC,CACZ,YAAa,CAAC,OAAQ,MAAO,MAAO,KAAMA,MAM5C0B,IAAK,CAAC,CACJA,IAAK,CAACA,KAMR,QAAS,CAAC,CACR,QAAS,CAACA,KAMZ,QAAS,CAAC,CACR,QAAS,CAACA,KAMZ,kBAAmB,CAAC,CAClB4C,QAAS,CAAC,SAvZQ,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,aA6Z7E,gBAAiB,CAAC,CAChB,gBAAiB,CAAC,QAAS,MAAO,SAAU,aAM9C,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQ,QAAS,MAAO,SAAU,aAMrD,gBAAiB,CAAC,CAChBC,QAAS,CAAC,SA5aQ,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,UA4axC,cAMrC,cAAe,CAAC,CACdC,MAAO,CAAC,QAAS,MAAO,SAAU,WAAY,aAMhD,aAAc,CAAC,CACbC,KAAM,CAAC,OAAQ,QAAS,MAAO,SAAU,UAAW,cAMtD,gBAAiB,CAAC,CAChB,gBAAiB,CAjcC,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,UAic1C,cAMnC,cAAe,CAAC,CACd,cAAe,CAAC,QAAS,MAAO,SAAU,WAAY,aAMxD,aAAc,CAAC,CACb,aAAc,CAAC,OAAQ,QAAS,MAAO,SAAU,aAOnDC,EAAG,CAAC,CACFA,EAAG,CAAC1C,KAMN2C,GAAI,CAAC,CACHA,GAAI,CAAC3C,KAMP4C,GAAI,CAAC,CACHA,GAAI,CAAC5C,KAMP6C,GAAI,CAAC,CACHA,GAAI,CAAC7C,KAMP8C,GAAI,CAAC,CACHA,GAAI,CAAC9C,KAMP+C,GAAI,CAAC,CACHA,GAAI,CAAC/C,KAMPgD,GAAI,CAAC,CACHA,GAAI,CAAChD,KAMPiD,GAAI,CAAC,CACHA,GAAI,CAACjD,KAMPkD,GAAI,CAAC,CACHA,GAAI,CAAClD,KAMPjjB,EAAG,CAAC,CACFA,EAAG,CAAC+iB,KAMNqD,GAAI,CAAC,CACHA,GAAI,CAACrD,KAMPsD,GAAI,CAAC,CACHA,GAAI,CAACtD,KAMPuD,GAAI,CAAC,CACHA,GAAI,CAACvD,KAMPwD,GAAI,CAAC,CACHA,GAAI,CAACxD,KAMPyD,GAAI,CAAC,CACHA,GAAI,CAACzD,KAMP0D,GAAI,CAAC,CACHA,GAAI,CAAC1D,KAMP2D,GAAI,CAAC,CACHA,GAAI,CAAC3D,KAMP4D,GAAI,CAAC,CACHA,GAAI,CAAC5D,KAMP,UAAW,CAAC,CACV,UAAW,CAACO,KAMd,kBAAmB,CAAC,mBAKpB,UAAW,CAAC,CACV,UAAW,CAACA,KAMd,kBAAmB,CAAC,mBAMpBsD,EAAG,CAAC,CACFA,EAAG,CAAC,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO3F,GAAkBe,KAM1E,QAAS,CAAC,CACR,QAAS,CAACf,GAAkBe,EAAS,MAAO,MAAO,SAMrD,QAAS,CAAC,CACR,QAAS,CAACf,GAAkBe,EAAS,OAAQ,OAAQ,MAAO,MAAO,MAAO,QAAS,CACjF6E,OAAQ,CAAC3F,KACRA,MAML4F,EAAG,CAAC,CACFA,EAAG,CAAC7F,GAAkBe,EAAS,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,SAM5E,QAAS,CAAC,CACR,QAAS,CAACf,GAAkBe,EAAS,MAAO,MAAO,MAAO,MAAO,MAAO,SAM1E,QAAS,CAAC,CACR,QAAS,CAACf,GAAkBe,EAAS,MAAO,MAAO,MAAO,MAAO,MAAO,SAM1E+E,KAAM,CAAC,CACLA,KAAM,CAAC9F,GAAkBe,EAAS,OAAQ,MAAO,MAAO,SAO1D,YAAa,CAAC,CACZgF,KAAM,CAAC,OAAQ9F,GAAcT,MAM/B,iBAAkB,CAAC,cAAe,wBAKlC,aAAc,CAAC,SAAU,cAKzB,cAAe,CAAC,CACdwG,KAAM,CAAC,OAAQ,aAAc,QAAS,SAAU,SAAU,WAAY,OAAQ,YAAa,QAASpG,MAMtG,cAAe,CAAC,CACdoG,KAAM,CAACrF,MAMT,aAAc,CAAC,eAKf,cAAe,CAAC,WAKhB,mBAAoB,CAAC,gBAKrB,aAAc,CAAC,cAAe,iBAK9B,cAAe,CAAC,oBAAqB,gBAKrC,eAAgB,CAAC,qBAAsB,qBAKvCsF,SAAU,CAAC,CACTA,SAAU,CAAC,UAAW,QAAS,SAAU,OAAQ,QAAS,SAAUjG,MAMtE,aAAc,CAAC,CACb,aAAc,CAAC,OAAQT,GAAUK,MAMnCsG,QAAS,CAAC,CACRA,QAAS,CAAC,OAAQ,QAAS,OAAQ,SAAU,UAAW,QAAS5G,GAAUU,MAM7E,aAAc,CAAC,CACb,aAAc,CAAC,OAAQA,MAMzB,kBAAmB,CAAC,CAClBmG,KAAM,CAAC,OAAQ,OAAQ,UAAWnG,MAMpC,sBAAuB,CAAC,CACtBmG,KAAM,CAAC,SAAU,aAOnB,oBAAqB,CAAC,CACpBC,YAAa,CAACtF,KAMhB,sBAAuB,CAAC,CACtB,sBAAuB,CAACiB,KAM1B,iBAAkB,CAAC,CACjBgE,KAAM,CAAC,OAAQ,SAAU,QAAS,UAAW,QAAS,SAMxD,aAAc,CAAC,CACbA,KAAM,CAACjF,KAMT,eAAgB,CAAC,CACf,eAAgB,CAACiB,KAMnB,kBAAmB,CAAC,YAAa,WAAY,eAAgB,gBAK7D,wBAAyB,CAAC,CACxBsE,WAAY,CApzBW,QAAS,SAAU,SAAU,SAAU,OAozB7B,UAMnC,4BAA6B,CAAC,CAC5BA,WAAY,CAAC,OAAQ,YAAa/G,GAAUE,MAM9C,mBAAoB,CAAC,CACnB,mBAAoB,CAAC,OAAQF,GAAUU,MAMzC,wBAAyB,CAAC,CACxBqG,WAAY,CAACvF,KAMf,iBAAkB,CAAC,YAAa,YAAa,aAAc,eAK3D,gBAAiB,CAAC,WAAY,gBAAiB,aAK/C,YAAa,CAAC,CACZiF,KAAM,CAAC,OAAQ,SAAU,UAAW,YAMtCO,OAAQ,CAAC,CACPA,OAAQ9D,MAMV,iBAAkB,CAAC,CACjB+D,MAAO,CAAC,WAAY,MAAO,SAAU,SAAU,WAAY,cAAe,MAAO,QAASvG,MAM5FwG,WAAY,CAAC,CACXA,WAAY,CAAC,SAAU,SAAU,MAAO,WAAY,WAAY,kBAMlEC,MAAO,CAAC,CACNA,MAAO,CAAC,SAAU,QAAS,MAAO,UAMpCC,QAAS,CAAC,CACRA,QAAS,CAAC,OAAQ,SAAU,UAM9BnC,QAAS,CAAC,CACRA,QAAS,CAAC,OAAQvE,MAOpB,gBAAiB,CAAC,CAChB2G,GAAI,CAAC,QAAS,QAAS,YAMzB,UAAW,CAAC,CACV,UAAW,CAAC,SAAU,UAAW,UAAW,UAO9C,aAAc,CAAC,CACb,aAAc,CAAC5E,KAMjB,YAAa,CAAC,CACZ,YAAa,CAAC,SAAU,UAAW,aAMrC,cAAe,CAAC,CACd4E,GAAI,CA16BkB,SAAU,SAAU,OAAQ,cAAe,WAAY,QAAS,eAAgB,YAAa,MA06B3FtG,MAM1B,YAAa,CAAC,CACZsG,GAAI,CAAC,YAAa,CAChBC,OAAQ,CAAC,GAAI,IAAK,IAAK,QAAS,aAOpC,UAAW,CAAC,CACVD,GAAI,CAAC,OAAQ,QAAS,UAAWxG,MAMnC,WAAY,CAAC,CACXwG,GAAI,CAAC,OAAQ,CACX,cAAe,CAAC,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,OACrDpG,MAML,WAAY,CAAC,CACXoG,GAAI,CAAC7F,KAMP,oBAAqB,CAAC,CACpB/c,KAAM,CAAC6d,KAMT,mBAAoB,CAAC,CACnBiF,IAAK,CAACjF,KAMR,kBAAmB,CAAC,CAClBkF,GAAI,CAAClF,KAMP,gBAAiB,CAAC,CAChB7d,KAAM,CAAC4d,KAMT,eAAgB,CAAC,CACfkF,IAAK,CAAClF,KAMR,cAAe,CAAC,CACdmF,GAAI,CAACnF,KAOPoF,QAAS,CAAC,CACRA,QAAS,CAAC5F,KAMZ,YAAa,CAAC,CACZ,YAAa,CAACA,KAMhB,YAAa,CAAC,CACZ,YAAa,CAACA,KAMhB,YAAa,CAAC,CACZ,YAAa,CAACA,KAMhB,YAAa,CAAC,CACZ,YAAa,CAACA,KAMhB,YAAa,CAAC,CACZ,YAAa,CAACA,KAMhB,YAAa,CAAC,CACZ,YAAa,CAACA,KAMhB,aAAc,CAAC,CACb,aAAc,CAACA,KAMjB,aAAc,CAAC,CACb,aAAc,CAACA,KAMjB,aAAc,CAAC,CACb,aAAc,CAACA,KAMjB,aAAc,CAAC,CACb,aAAc,CAACA,KAMjB,aAAc,CAAC,CACb,aAAc,CAACA,KAMjB,aAAc,CAAC,CACb,aAAc,CAACA,KAMjB,aAAc,CAAC,CACb,aAAc,CAACA,KAMjB,aAAc,CAAC,CACb,aAAc,CAACA,KAMjB,WAAY,CAAC,CACX6F,OAAQ,CAAC3F,KAMX,aAAc,CAAC,CACb,WAAY,CAACA,KAMf,aAAc,CAAC,CACb,WAAY,CAACA,KAMf,aAAc,CAAC,CACb,WAAY,CAACA,KAMf,aAAc,CAAC,CACb,WAAY,CAACA,KAMf,aAAc,CAAC,CACb,WAAY,CAACA,KAMf,aAAc,CAAC,CACb,WAAY,CAACA,KAMf,aAAc,CAAC,CACb,WAAY,CAACA,KAMf,aAAc,CAAC,CACb,WAAY,CAACA,KAMf,iBAAkB,CAAC,CACjB,iBAAkB,CAACU,KAMrB,eAAgB,CAAC,CACfiF,OAAQ,CA1qCe,QAAS,SAAU,SAAU,SAAU,OA0qCjC,YAM/B,WAAY,CAAC,CACX,WAAY,CAAC3F,KAMf,mBAAoB,CAAC,oBAKrB,WAAY,CAAC,CACX,WAAY,CAACA,KAMf,mBAAoB,CAAC,oBAKrB,iBAAkB,CAAC,CACjB,iBAAkB,CAACU,KAMrB,eAAgB,CAAC,CACfkF,OAhtCsB,CAAC,QAAS,SAAU,SAAU,SAAU,UAstChE,eAAgB,CAAC,CACfD,OAAQ,CAAC9F,KAMX,iBAAkB,CAAC,CACjB,WAAY,CAACA,KAMf,iBAAkB,CAAC,CACjB,WAAY,CAACA,KAMf,iBAAkB,CAAC,CACjB,WAAY,CAACA,KAMf,iBAAkB,CAAC,CACjB,WAAY,CAACA,KAMf,iBAAkB,CAAC,CACjB,WAAY,CAACA,KAMf,iBAAkB,CAAC,CACjB,WAAY,CAACA,KAMf,iBAAkB,CAAC,CACjB,WAAY,CAACA,KAMf,iBAAkB,CAAC,CACjB,WAAY,CAACA,KAMf,eAAgB,CAAC,CACf+F,OAAQ,CAAC/F,KAMX,gBAAiB,CAAC,CAChBgG,QAAS,CAAC,GA7xCa,QAAS,SAAU,SAAU,SAAU,UAmyChE,iBAAkB,CAAC,CACjB,iBAAkB,CAAC5H,GAAUU,MAM/B,YAAa,CAAC,CACZkH,QAAS,CAAC5H,GAAUE,MAMtB,gBAAiB,CAAC,CAChB0H,QAAS,CAACpG,KAMZ,SAAU,CAAC,CACTqG,KAAM1E,MAMR,eAAgB,CAAC,cAKjB,aAAc,CAAC,CACb0E,KAAM,CAACrG,KAMT,eAAgB,CAAC,CACf,eAAgB,CAACiB,KAMnB,gBAAiB,CAAC,CAChB,cAAe,CAACzC,GAAUE,MAM5B,oBAAqB,CAAC,CACpB,cAAe,CAACsB,KAOlBsG,OAAQ,CAAC,CACPA,OAAQ,CAAC,GAAI,QAAS,OAAQnH,GAAcQ,MAM9C,eAAgB,CAAC,CACf2G,OAAQ,CAACzG,MAMXoB,QAAS,CAAC,CACRA,QAAS,CAACA,KAMZ,YAAa,CAAC,CACZ,YAAa,CAt3CU,SAAU,WAAY,SAAU,UAAW,SAAU,UAAW,cAAe,aAAc,aAAc,aAAc,aAAc,YAAa,MAAO,aAAc,QAAS,aAs3CvK,eAAgB,iBAMpD,WAAY,CAAC,CACX,WA73CsB,CAAC,SAAU,WAAY,SAAU,UAAW,SAAU,UAAW,cAAe,aAAc,aAAc,aAAc,aAAc,YAAa,MAAO,aAAc,QAAS,gBAq4C3MnhB,OAAQ,CAAC,CACPA,OAAQ,CAAC,GAAI,UAMfogB,KAAM,CAAC,CACLA,KAAM,CAACA,KAMTC,WAAY,CAAC,CACXA,WAAY,CAACA,KAMfK,SAAU,CAAC,CACTA,SAAU,CAACA,KAMb,cAAe,CAAC,CACd,cAAe,CAAC,GAAI,OAAQrB,GAAcD,MAM5CuB,UAAW,CAAC,CACVA,UAAW,CAACA,KAMd,aAAc,CAAC,CACb,aAAc,CAACC,KAMjBC,OAAQ,CAAC,CACPA,OAAQ,CAACA,KAMXQ,SAAU,CAAC,CACTA,SAAU,CAACA,KAMbE,MAAO,CAAC,CACNA,MAAO,CAACA,KAOV,kBAAmB,CAAC,CAClB,kBAAmB,CAAC,GAAI,UAM1B,gBAAiB,CAAC,CAChB,gBAAiB,CAACnB,KAMpB,sBAAuB,CAAC,CACtB,sBAAuB,CAACC,KAM1B,oBAAqB,CAAC,CACpB,oBAAqB,CAACK,KAMxB,qBAAsB,CAAC,CACrB,qBAAsB,CAACC,KAMzB,sBAAuB,CAAC,CACtB,sBAAuB,CAACC,KAM1B,kBAAmB,CAAC,CAClB,kBAAmB,CAACC,KAMtB,mBAAoB,CAAC,CACnB,mBAAoB,CAACM,KAMvB,oBAAqB,CAAC,CACpB,oBAAqB,CAACE,KAMxB,iBAAkB,CAAC,CACjB,iBAAkB,CAACE,KAOrB,kBAAmB,CAAC,CAClB6E,OAAQ,CAAC,WAAY,cAMvB,iBAAkB,CAAC,CACjB,iBAAkB,CAAC5F,KAMrB,mBAAoB,CAAC,CACnB,mBAAoB,CAACA,KAMvB,mBAAoB,CAAC,CACnB,mBAAoB,CAACA,KAMvB,eAAgB,CAAC,CACfiG,MAAO,CAAC,OAAQ,WAMlBC,QAAS,CAAC,CACRA,QAAS,CAAC,MAAO,YAOnBC,WAAY,CAAC,CACXA,WAAY,CAAC,OAAQ,MAAO,GAAI,SAAU,UAAW,SAAU,YAAavH,MAM9Ejb,SAAU,CAAC,CACTA,SAAU6d,MAMZ4E,KAAM,CAAC,CACLA,KAAM,CAAC,SAAU,KAAM,MAAO,SAAUxH,MAM1CyH,MAAO,CAAC,CACNA,MAAO7E,MAMT8E,QAAS,CAAC,CACRA,QAAS,CAAC,OAAQ,OAAQ,OAAQ,QAAS,SAAU1H,MAOvD2H,UAAW,CAAC,CACVA,UAAW,CAAC,GAAI,MAAO,UAMzBzF,MAAO,CAAC,CACNA,MAAO,CAACA,KAMV,UAAW,CAAC,CACV,UAAW,CAACA,KAMd,UAAW,CAAC,CACV,UAAW,CAACA,KAMd0F,OAAQ,CAAC,CACPA,OAAQ,CAAC/H,GAAWG,MAMtB,cAAe,CAAC,CACd,cAAe,CAACsC,KAMlB,cAAe,CAAC,CACd,cAAe,CAACA,KAMlB,SAAU,CAAC,CACT,SAAU,CAACF,KAMb,SAAU,CAAC,CACT,SAAU,CAACA,KAMb,mBAAoB,CAAC,CACnByF,OAAQ,CAAC,SAAU,MAAO,YAAa,QAAS,eAAgB,SAAU,cAAe,OAAQ,WAAY7H,MAO/G8H,OAAQ,CAAC,CACPA,OAAQ,CAAC,OAAQhH,KAMnBiH,WAAY,CAAC,CACXA,WAAY,CAAC,OAAQ,UAMvBC,OAAQ,CAAC,CACPA,OAAQ,CAAC,OAAQ,UAAW,UAAW,OAAQ,OAAQ,OAAQ,OAAQ,cAAe,OAAQ,eAAgB,WAAY,OAAQ,YAAa,gBAAiB,QAAS,OAAQ,UAAW,OAAQ,WAAY,aAAc,aAAc,aAAc,WAAY,WAAY,WAAY,WAAY,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,cAAe,cAAe,UAAW,WAAYhI,MAM/a,cAAe,CAAC,CACdiI,MAAO,CAACnH,KAMV,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,OAAQ,UAM7BoH,OAAQ,CAAC,CACPA,OAAQ,CAAC,OAAQ,IAAK,IAAK,MAM7B,kBAAmB,CAAC,CAClBC,OAAQ,CAAC,OAAQ,YAMnB,WAAY,CAAC,CACX,WAAY3F,MAMd,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,WAAY,CAAC,CACX,WAAYA,MAMd,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,YAAa,CAAC,CACZ,YAAaA,MAMf,aAAc,CAAC,CACb4F,KAAM,CAAC,QAAS,MAAO,SAAU,gBAMnC,YAAa,CAAC,CACZA,KAAM,CAAC,SAAU,YAMnB,YAAa,CAAC,CACZA,KAAM,CAAC,OAAQ,IAAK,IAAK,UAM3B,kBAAmB,CAAC,CAClBA,KAAM,CAAC,YAAa,eAMtBC,MAAO,CAAC,CACNA,MAAO,CAAC,OAAQ,OAAQ,kBAM1B,UAAW,CAAC,CACV,YAAa,CAAC,IAAK,OAAQ,WAM7B,UAAW,CAAC,CACV,YAAa,CAAC,IAAK,KAAM,UAM3B,WAAY,CAAC,oBAKbC,OAAQ,CAAC,CACPA,OAAQ,CAAC,OAAQ,OAAQ,MAAO,UAMlC,cAAe,CAAC,CACd,cAAe,CAAC,OAAQ,SAAU,WAAY,YAAatI,MAO7DuI,KAAM,CAAC,CACLA,KAAM,CAACzH,EAAQ,UAMjB,WAAY,CAAC,CACX0H,OAAQ,CAAClJ,GAAUE,GAAmBI,MAMxC4I,OAAQ,CAAC,CACPA,OAAQ,CAAC1H,EAAQ,UAOnB2H,GAAI,CAAC,UAAW,eAKhB,sBAAuB,CAAC,CACtB,sBAAuB,CAAC,OAAQ,WAGpCrQ,uBAAwB,CACtBgL,SAAU,CAAC,aAAc,cACzBC,WAAY,CAAC,eAAgB,gBAC7BxB,MAAO,CAAC,UAAW,UAAW,QAAS,MAAO,MAAO,QAAS,SAAU,QACxE,UAAW,CAAC,QAAS,QACrB,UAAW,CAAC,MAAO,UACnBkC,KAAM,CAAC,QAAS,OAAQ,UACxBrC,IAAK,CAAC,QAAS,SACfgD,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC9CC,GAAI,CAAC,KAAM,MACXC,GAAI,CAAC,KAAM,MACX7lB,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC9ComB,GAAI,CAAC,KAAM,MACXC,GAAI,CAAC,KAAM,MACXU,KAAM,CAAC,IAAK,KACZ,YAAa,CAAC,WACd,aAAc,CAAC,cAAe,mBAAoB,aAAc,cAAe,gBAC/E,cAAe,CAAC,cAChB,mBAAoB,CAAC,cACrB,aAAc,CAAC,cACf,cAAe,CAAC,cAChB,eAAgB,CAAC,cACjB,aAAc,CAAC,UAAW,YAC1BiB,QAAS,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,cAC1L,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,iBAAkB,CAAC,mBAAoB,oBACvC,WAAY,CAAC,aAAc,aAAc,aAAc,aAAc,aAAc,cACnF,aAAc,CAAC,aAAc,cAC7B,aAAc,CAAC,aAAc,cAC7B,eAAgB,CAAC,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,kBAC3G,iBAAkB,CAAC,iBAAkB,kBACrC,iBAAkB,CAAC,iBAAkB,kBACrC,WAAY,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,aACxG,YAAa,CAAC,YAAa,aAC3B,YAAa,CAAC,YAAa,aAC3B,WAAY,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,aACxG,YAAa,CAAC,YAAa,aAC3B,YAAa,CAAC,YAAa,aAC3BsB,MAAO,CAAC,UAAW,UAAW,YAC9B,UAAW,CAAC,SACZ,UAAW,CAAC,SACZ,WAAY,CAAC,UAEfhQ,+BAAgC,CAC9B,YAAa,CAAC,YAEjB,ICv8EI,SAASqQ,MAAMC,GACb9H,UAAQvK,EAAKqS,GACtB,CCEA,MAAM/jB,GAAgBgkB,GAEhBriB,GAAgB3H,cAGpB,EAAGiY,eAAc7V,GAASqB,IACzBumB,SAAwB,CACvBvmB,MACAwU,UAAW6R,GACT,oIACA7R,MAEE7V,MAGRuF,GAAc/D,YAAcomB,GAAyBpmB,YAErD,MAAMqmB,GAAgBtS,GACpB,4lBACA,CACEI,SAAU,CACRO,QAAS,CACP4R,QAAS,uCACTC,YACE,oFAGNjS,gBAAiB,CACfI,QAAS,aAKT7L,GAAcxI,cAIlB,EAAGgU,YAAWK,aAAYlW,GAASqB,IAEjC2mB,MAACJ,GAAoB,CACnBvmB,MACAwU,UAAW6R,GAAGG,GAAc,CAAE3R,YAAYL,MACtC7V,MAIVqK,GAAM7I,YAAcomB,GAAqBpmB,YAErB5D,cAGlB,EAAGiY,eAAc7V,GAASqB,IACzBumB,SAAsB,CACrBvmB,MACAwU,UAAW6R,GACT,qgBACA7R,MAEE7V,MAGIwB,YAAcomB,GAAuBpmB,YAEjD,MAAMsQ,GAAalU,cAGjB,EAAGiY,eAAc7V,GAASqB,IACzBumB,SAAqB,CACpBvmB,MACAwU,UAAW6R,GACT,wVACA7R,GAEFoS,cAAY,MACRjoB,EAEJoB,eAAC8mB,GAAErS,UAAU,gBAGjB/D,GAAWtQ,YAAcomB,GAAsBpmB,YAE/C,MAAM4P,GAAaxT,cAGjB,EAAGiY,eAAc7V,GAASqB,IACzBumB,SAAqB,CACpBvmB,MACAwU,UAAW6R,GAAG,wBAAyB7R,MACnC7V,MAGRoR,GAAW5P,YAAcomB,GAAsBpmB,YAE/C,MAAM+P,GAAmB3T,cAGvB,EAAGiY,eAAc7V,GAASqB,IACzBumB,SAA2B,CAC1BvmB,MACAwU,UAAW6R,GAAG,qBAAsB7R,MAChC7V,MCjGD,SAASmoB,KACR,MAAEhpB,UP+JV,WACE,MAAOF,EAAOmpB,GAAYxqB,WAAsBkC,IAYzC,OAVPlC,aAAgB,KACdiC,GAAU4S,KAAK2V,GACR,KACCvf,QAAQhJ,GAAUwD,QAAQ+kB,GAC5Bvf,GAAY,GACJwf,UAAOxf,EAAO,MAG3B,CAAC5J,IAEG,IACFA,EACHG,SACAgB,QAAU5B,GAAqBK,GAAS,CAAEC,KAAM,gBAAiBN,YAErE,COjLqB8pB,iBAGhB1kB,cACEzE,EAAOG,KAAI,UAAUE,GAAEA,QAAI+oB,cAAOC,EAAatpB,YAAWc,kBAEtDqK,OAAmBrK,mBACjBsR,OAAIuE,UAAU,uBACZ0S,SAAUnX,IAAYmX,aACtBC,SACEjX,IAAkBiX,gBAGtBtpB,EACD2F,EAAAtD,IAACuQ,SARStS,MAYhBqF,EAAAtD,IAACgE,SAGP,CD8EAgM,GAAiB/P,YAAcomB,GAA4BpmB,YEnG3D,MAAMinB,wBAA2D/oB,GAgBpDgpB,GAA8C,EACzDtnB,WACAunB,eAAe,SACfC,aAAa,sBAEb,MAAO5P,EAAO6P,GAAiBC,WAAgBH,IACxCI,EAAeC,GAAoBF,WAA2B,UAC9DG,EAAWC,GAAgBJ,YAAS,GAGrCK,EAAiB,IACC,oBAAXxrB,QACFA,OAAOyrB,WAAW,gCAAgCC,QAAU,OAE9D,QAIHC,EAAgBC,GACC,WAAjBA,EACKJ,IAEFI,EAIHC,EAAcT,IAClB,MAAMU,EAAO7iB,SAAS8iB,gBAGjBjN,YAAUkN,OAAO,QAAS,QAG1BlN,YAAUmN,IAAIb,GAGdrd,eAAa,aAAcqd,IAmBlCxmB,aAAU,KACJ,GAAkB,oBAAX5E,OAAwB,CAE3BksB,QAAaC,aAAaC,QAAQnB,GAClCoB,EAAeH,GAAc,CAAC,QAAS,OAAQ,UAAU9a,SAAS8a,GACpEA,EACAlB,EAEJE,EAAcmB,GAGRC,QAAWX,EAAaU,GAC9BhB,EAAiBiB,GACjBT,EAAWS,GAEXf,GAAa,MAEd,CAACP,EAAcC,IAGlBrmB,aAAU,KACJ,GAAkB,oBAAX5E,OAAwB,OAE7BusB,QAAavsB,OAAOyrB,WAAW,gCAE/Be,EAAe,KACnB,GAAc,WAAVnR,EAAoB,CACtB,MAAMiR,EAAWd,IACjBH,EAAiBiB,GACjBT,EAAWS,KAKf,OADWpjB,mBAAiB,SAAUsjB,GAC/B,IAAMD,EAAWpjB,oBAAoB,SAAUqjB,KACrD,CAACnR,IAEJ,MAAM5D,EAA0B,CAC9B4D,QACAoR,SAtDgBC,IAChBxB,EAAcwB,GAGQ,oBAAX1sB,QACI2sB,qBAAQ1B,EAAYyB,GAI7BJ,QAAWX,EAAae,GAC9BrB,EAAiBiB,GACjBT,EAAWS,IA4CXlB,gBACAE,aAIAjB,SAAAzmB,IAACknB,GAAajmB,SAAQ,CAAC4S,QACpBhU,cChIiH,IAAsImpB,GAAGznB,MAAM,IAAIykB,KAAK,GAAGiD,GAAG,EAAEC,QAAQC,EAAE7U,UAAU8U,KAAKC,EAAEC,cAAc,MAAM,CAAChV,UAAU,CAAC,yBAAyB8U,GAAG/qB,OAAOiP,SAAS7I,KAAK,KAAK,eAAe0kB,GAAGE,EAAEC,cAAc,MAAM,CAAChV,UAAU,kBAAkB0U,GAAGjrB,KAAI,CAACC,EAAE4D,IAAIynB,EAAEC,cAAc,MAAM,CAAChV,UAAU,qBAAqBrP,IAAI,eAAerD,UAAS2nB,GAAGF,EAAEC,cAAc,MAAM,CAACE,MAAM,6BAA6BC,QAAQ,YAAYzD,KAAK,eAAe0D,OAAO,KAAKC,MAAM,MAAMN,EAAEC,cAAc,OAAO,CAACM,SAAS,UAAUC,EAAE,yJAAyJC,SAAS,aAAaC,GAAGV,EAAEC,cAAc,MAAM,CAACE,MAAM,6BAA6BC,QAAQ,YAAYzD,KAAK,eAAe0D,OAAO,KAAKC,MAAM,MAAMN,EAAEC,cAAc,OAAO,CAACM,SAAS,UAAUC,EAAE,4OAA4OC,SAAS,aAAaE,GAAGX,EAAEC,cAAc,MAAM,CAACE,MAAM,6BAA6BC,QAAQ,YAAYzD,KAAK,eAAe0D,OAAO,KAAKC,MAAM,MAAMN,EAAEC,cAAc,OAAO,CAACM,SAAS,UAAUC,EAAE,0OAA0OC,SAAS,aAAaG,GAAGZ,EAAEC,cAAc,MAAM,CAACE,MAAM,6BAA6BC,QAAQ,YAAYzD,KAAK,eAAe0D,OAAO,KAAKC,MAAM,MAAMN,EAAEC,cAAc,OAAO,CAACM,SAAS,UAAUC,EAAE,sIAAsIC,SAAS,aAAaI,GAAGb,EAAEC,cAAc,MAAM,CAACE,MAAM,6BAA6BG,MAAM,KAAKD,OAAO,KAAKD,QAAQ,YAAYzD,KAAK,OAAOC,OAAO,eAAekE,YAAY,MAAMC,cAAc,QAAQC,eAAe,SAAShB,EAAEC,cAAc,OAAO,CAACgB,GAAG,KAAKC,GAAG,IAAIC,GAAG,IAAIC,GAAG,OAAOpB,EAAEC,cAAc,OAAO,CAACgB,GAAG,IAAIC,GAAG,IAAIC,GAAG,KAAKC,GAAG,QAAsRC,GAAG,EAA+xFC,GAAE,IAA5xF,MAAM,WAAAC,GAAcC,KAAKC,UAAc1B,IAAAyB,KAAKE,YAAY7Z,KAAKkY,GAAG,KAAK,IAAIprB,EAAE6sB,KAAKE,YAAYjpB,QAAQsnB,GAAQyB,KAAAE,YAAYjE,OAAO9oB,EAAE,EAAC,GAAI6sB,KAAKG,QAAW5B,IAACyB,KAAKE,YAAY7sB,SAAWF,KAAEorB,IAAE,EAAGyB,KAAKI,SAAY7B,IAAMyB,KAAAG,QAAQ5B,GAAGyB,KAAKjtB,OAAO,IAAIitB,KAAKjtB,OAAOwrB,EAAC,EAAGyB,KAAKK,OAAU9B,IAAK,IAAA+B,EAAE,IAAIC,QAAQptB,KAAK4D,GAAGwnB,EAAEiC,EAA+B,iBAAnB,MAAHjC,OAAQ,EAAOA,EAAEnrB,MAA0B,OAATktB,EAAE/B,EAAEnrB,SAAU,EAAOktB,EAAEpmB,QAAQ,EAAEqkB,EAAEnrB,GAAGysB,KAAKY,EAAET,KAAKjtB,OAAOqZ,MAAKsU,GAAGA,EAAEttB,KAAKotB,IAAGjI,OAAkB,IAAhBgG,EAAEoC,aAAwBpC,EAAEoC,YAAmB,OAAAX,KAAKY,gBAAgBvuB,IAAImuB,IAAIR,KAAKY,gBAAgBpuB,OAAOguB,GAAGC,EAAET,KAAKjtB,OAAOitB,KAAKjtB,OAAOG,KAAIwtB,GAAGA,EAAEttB,KAAKotB,GAAGR,KAAKG,QAAQ,IAAIO,KAAKnC,EAAEnrB,GAAGotB,EAAErE,MAAMhpB,IAAI,IAAIutB,KAAKnC,EAAEnrB,GAAGotB,EAAEG,YAAYpI,EAAE4D,MAAMhpB,IAAIutB,IAAGV,KAAKI,SAAS,CAACjE,MAAMhpB,KAAK4D,EAAE4pB,YAAYpI,EAAEnlB,GAAGotB,IAAIA,CAAA,EAAQR,KAAAhsB,QAAYuqB,IAAAyB,KAAKY,gBAAgBpD,IAAIe,GAAGA,GAAGyB,KAAKjtB,OAAOM,SAAWF,IAAM6sB,KAAAE,YAAY7sB,SAAQ0D,GAAGA,EAAE,CAAC3D,GAAGD,EAAEC,GAAGY,SAAQ,KAAI,IAAIgsB,KAAKE,YAAY7sB,SAAWF,KAAE,CAACC,GAAGmrB,EAAEvqB,SAAQ,MAAMuqB,GAAQyB,KAAAO,QAAQ,CAAChC,EAAEprB,IAAI6sB,KAAKK,OAAO,IAAIltB,EAAEotB,QAAQhC,IAAIyB,KAAKxnB,MAAM,CAAC+lB,EAAEprB,IAAI6sB,KAAKK,OAAO,IAAIltB,EAAEotB,QAAQhC,EAAE7rB,KAAK,UAAUstB,KAAKa,QAAQ,CAACtC,EAAEprB,IAAI6sB,KAAKK,OAAO,IAAIltB,EAAET,KAAK,UAAU6tB,QAAQhC,IAAIyB,KAAKc,KAAK,CAACvC,EAAEprB,IAAI6sB,KAAKK,OAAO,IAAIltB,EAAET,KAAK,OAAO6tB,QAAQhC,IAAIyB,KAAKe,QAAQ,CAACxC,EAAEprB,IAAI6sB,KAAKK,OAAO,IAAIltB,EAAET,KAAK,UAAU6tB,QAAQhC,IAAIyB,KAAKgB,QAAQ,CAACzC,EAAEprB,IAAI6sB,KAAKK,OAAO,IAAIltB,EAAET,KAAK,UAAU6tB,QAAQhC,IAASyB,KAAAiB,QAAQ,CAAC1C,EAAEprB,KAAK,IAAIA,EAAE,OAAW,IAAA4D,OAAc,IAAV5D,EAAA6tB,UAAmBjqB,EAAEipB,KAAKK,OAAO,IAAIltB,EAAE8tB,QAAQ1C,EAAE7rB,KAAK,UAAU6tB,QAAQptB,EAAE6tB,QAAQ5E,YAAkC,mBAAfjpB,EAAEipB,YAAwBjpB,EAAEipB,iBAAY,KAAU,IAA8C7D,EAA1CiI,EAAEjC,aAAa2C,QAAQ3C,EAAEA,IAAIkC,OAAM,IAAJ1pB,EAAaupB,EAAEE,EAAEW,MAAKC,MAAMjQ,IAAO,GAAAoH,EAAE,CAAC,UAAUpH,GAAGkQ,EAAGC,eAAenQ,GAAGsP,GAAE,EAAGT,KAAKK,OAAO,CAACjtB,GAAG2D,EAAErE,KAAK,UAAU6tB,QAAQpP,SAAE,GAAUoQ,GAAGpQ,KAAKA,EAAEqQ,GAAG,CAAGf,GAAA,EAAG,IAAIgB,EAAkB,mBAATtuB,EAAEqF,YAAwBrF,EAAEqF,MAAM,uBAAuB2Y,EAAEuQ,UAAUvuB,EAAEqF,MAAMmpB,EAAwB,mBAAfxuB,EAAEipB,kBAA8BjpB,EAAEipB,YAAY,uBAAuBjL,EAAEuQ,UAAUvuB,EAAEipB,YAAiB4D,KAAAK,OAAO,CAACjtB,GAAG2D,EAAErE,KAAK,QAAQ6tB,QAAQkB,EAAErF,YAAYuF,GAAG,cAAqB,IAAZxuB,EAAE0tB,QAAiB,CAAGJ,GAAA,EAAO,IAAAgB,EAAoB,mBAAXtuB,EAAE0tB,cAA0B1tB,EAAE0tB,QAAQ1P,GAAGhe,EAAE0tB,QAAQc,EAAwB,mBAAfxuB,EAAEipB,kBAA8BjpB,EAAEipB,YAAYjL,GAAGhe,EAAEipB,YAAiB4D,KAAAK,OAAO,CAACjtB,GAAG2D,EAAErE,KAAK,UAAU6tB,QAAQkB,EAAErF,YAAYuF,GAAG,KAAIC,OAAMR,MAAMjQ,IAAI,GAAGoH,EAAE,CAAC,SAASpH,QAAa,IAAVhe,EAAEqF,MAAe,CAAGioB,GAAA,EAAO,IAAAoB,EAAkB,mBAAT1uB,EAAEqF,YAAwBrF,EAAEqF,MAAM2Y,GAAGhe,EAAEqF,MAAMipB,EAAwB,mBAAftuB,EAAEipB,kBAA8BjpB,EAAEipB,YAAYjL,GAAGhe,EAAEipB,YAAiB4D,KAAAK,OAAO,CAACjtB,GAAG2D,EAAErE,KAAK,QAAQ6tB,QAAQsB,EAAEzF,YAAYqF,GAAG,KAAIK,SAAQ,KAAS,IAAA3Q,EAAEsP,IAAIT,KAAKhsB,QAAQ+C,GAAGA,OAAE,GAAuB,OAAdoa,EAAEhe,EAAE2uB,UAAgB3Q,EAAE4Q,KAAK5uB,EAAC,IAAIutB,EAAE,IAAI,IAAIQ,SAAQ,CAAC/P,EAAE0Q,IAAIvB,EAAEa,MAAK,IAAW,WAAP5I,EAAE,GAAcsJ,EAAEtJ,EAAE,IAAIpH,EAAEoH,EAAE,MAAKqJ,MAAMC,KAAI,MAAiB,iBAAH9qB,GAAuB,iBAAHA,EAAY,CAACirB,OAAOtB,GAAG9W,OAAOqY,OAAOlrB,EAAE,CAACirB,OAAOtB,GAAE,EAAQV,KAAAkC,OAAO,CAAC3D,EAAEprB,KAAK,IAAI4D,GAAM,MAAH5D,OAAQ,EAAOA,EAAEC,KAAKysB,KAAK,OAAOG,KAAKK,OAAO,CAAClrB,IAAIopB,EAAExnB,GAAG3D,GAAG2D,KAAK5D,IAAI4D,CAAA,EAAGipB,KAAKmC,gBAAgB,IAAInC,KAAKjtB,OAAOS,QAAO+qB,IAAIyB,KAAKY,gBAAgBvuB,IAAIksB,EAAEnrB,MAAU4sB,KAAAE,YAAY,GAAGF,KAAKjtB,OAAO,GAAGitB,KAAKY,gBAAoB,IAAAhP,GAAG,GAAmG2P,GAAMjD,MAAa,iBAAHA,GAAa,OAAOA,GAAgB,kBAANA,EAAEkD,IAAe,WAAWlD,GAAoB,iBAAVA,EAAEoD,OAAiBU,GAA3L,CAAC9D,EAAEC,KAAK,IAAIprB,GAAM,MAAHorB,OAAQ,EAAOA,EAAEnrB,KAAKysB,KAAY,OAAAC,GAAEM,SAAS,CAACjE,MAAMmC,KAAKC,EAAEnrB,GAAGD,IAAIA,CAAA,EACx+K,SAASkvB,GAAG/D,GAAG,YAAiB,IAAVA,EAAE5mB,KAAc,CADgmLkS,OAAOqY,OAAOG,GAAG,CAACvB,QAAQf,GAAEe,QAAQC,KAAKhB,GAAEgB,KAAKC,QAAQjB,GAAEiB,QAAQvoB,MAAMsnB,GAAEtnB,MAAM0pB,OAAOpC,GAAEoC,OAAO3B,QAAQT,GAAES,QAAQU,QAAQnB,GAAEmB,QAAQjtB,QAAQ8rB,GAAE9rB,QAAQgtB,QAAQlB,GAAEkB,SAAS,CAACsB,WAArN,IAAIxC,GAAE/sB,OAA6NwvB,UAAnN,IAAIzC,GAAEqC,oBAA4N,SAAY7D,GAAGkE,SAASjE,GAAG,IAAW,GAAiB,oBAAV/jB,SAAsB,OAAO,IAAIrH,EAAEqH,SAASioB,MAAMjoB,SAASkoB,qBAAqB,QAAQ,GAAG3rB,EAAEyD,SAASikB,cAAc,SAAW1nB,EAAArE,KAAK,WAAe,QAAJ6rB,GAAWprB,EAAEwvB,WAAWxvB,EAAEyvB,aAAa7rB,EAAE5D,EAAEwvB,YAAYxvB,EAAE0vB,YAAY9rB,GAAGA,EAAE+rB,WAAW/rB,EAAE+rB,WAAWC,QAAQzE,EAAEvnB,EAAE8rB,YAAYroB,SAASwoB,eAAe1E,GAAG,CAAC2E,CAAG,sycAC3nM,IAAIvL,GAAG,EAAoCwL,GAAG,GAAgB,SAASC,MAAK7E,GAAG,OAAOA,EAAE9qB,OAAOiP,SAAS7I,KAAK,IAAI,CAA8E,IAAIwpB,GAAM9E,IAAK,IAAA+E,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAM,IAAC1P,OAAOkK,EAAEvrB,MAAMG,EAAE6wB,SAASjtB,EAAEktB,YAAYzD,EAAE0D,WAAWzD,EAAE0D,cAAc5L,EAAE6L,QAAQ9D,EAAE7jB,MAAMikB,EAAE3tB,OAAOoe,EAAEkT,SAASxC,EAAEyC,YAAY7C,EAAE8C,kBAAkB5C,EAAE6C,YAAYC,EAAGrnB,MAAMsnB,EAAGC,kBAAkBC,EAAGC,kBAAkBC,EAAErb,UAAUsb,EAAG,GAAGC,qBAAqBC,EAAG,GAAGttB,SAASmkB,EAAEje,SAASqnB,EAAG5Q,IAAIqD,EAAGwN,YAAYC,EAAGC,gBAAgBC,EAAEzU,WAAW0U,EAAEC,MAAMC,EAAEC,qBAAqBC,EAAG,cAAcC,sBAAsBC,GAAIvH,GAAGwH,EAAEC,GAAGC,EAAEtJ,SAAS,OAAOuJ,EAAGC,GAAGF,EAAEtJ,SAAS,OAAOyJ,EAAEC,GAAGJ,EAAEtJ,UAAS,IAAK2J,EAAElO,GAAI6N,EAAEtJ,UAAS,IAAK4J,EAAE7P,GAAGuP,EAAEtJ,UAAS,IAAK6J,EAAGvH,GAAGgH,EAAEtJ,UAAS,IAAKjE,EAAEhZ,IAAGumB,EAAEtJ,UAAS,IAAK8J,GAAEC,IAAGT,EAAEtJ,SAAS,IAAIpF,GAAEoP,IAAGV,EAAEtJ,SAAS,GAAGiK,GAAEX,EAAE9wB,OAAO/B,EAAEwE,UAAUmkB,GAA7zB,KAAo0B8K,GAAEZ,EAAE9wB,OAAO,MAAM2a,GAAEmW,EAAE9wB,OAAO,MAAM2xB,GAAO,IAAJnG,EAAMoG,GAAGpG,EAAE,GAAGnI,EAAEwO,GAAE5zB,EAAET,KAAKs0B,IAAkB,IAAhB7zB,EAAEwtB,YAAiBsG,GAAG9zB,EAAEsW,WAAW,GAAGyd,GAAG/zB,EAAE6xB,sBAAsB,GAAGmC,GAAGnB,EAAEoB,SAAQ,IAAI9G,EAAE5jB,WAAa2qB,KAAEj1B,UAAUe,EAAEC,MAAK,GAAE,CAACktB,EAAEntB,EAAEC,KAAKk0B,GAAGtB,EAAEoB,SAAQ,KAAS,IAAAC,EAAE,OAAyB,OAAlBA,EAAEl0B,EAAEqxB,aAAmB6C,EAAE5C,CAAA,GAAI,CAACtxB,EAAEqxB,YAAYC,IAAK8C,GAAGvB,EAAEoB,SAAQ,IAAIj0B,EAAEwE,UAAUmkB,GAApnC,KAA0nC,CAAC3oB,EAAEwE,SAASmkB,IAAI0L,GAAGxB,EAAE9wB,OAAO,GAAGuyB,GAAEzB,EAAE9wB,OAAO,GAAGwyB,GAAG1B,EAAE9wB,OAAO,GAAGyyB,GAAE3B,EAAE9wB,OAAO,OAAO0yB,GAAGC,IAAI3C,EAAG9Z,MAAM,KAAK0c,GAAG9B,EAAEoB,SAAQ,IAAI9G,EAAElW,QAAO,CAACid,EAAE11B,EAAEo2B,IAAIA,GAAGZ,GAAGE,EAAEA,EAAE11B,EAAEktB,QAAO,IAAG,CAACyB,EAAE6G,KAAKa,GADmhC,MAAK,IAAI1J,EAAEC,GAAG0J,EAAGvL,SAASliB,SAASkM,QAAeuhB,SAAG9xB,WAAU,KAAK,IAAIhD,EAAE,KAAKorB,EAAE/jB,SAASkM,OAAM,EAAU,OAAAlM,SAASC,iBAAiB,mBAAmBtH,GAAG,IAAI5B,OAAOmJ,oBAAoB,mBAAmBvH,EAAC,GAAG,IAAImrB,CAAA,EACpuC4J,GAAKC,GAAGh1B,EAAEkhB,QAAQkK,EAAE6J,GAAO,YAAJrB,GAAcU,GAAE7yB,QAAQoxB,EAAEoB,SAAQ,IAAID,GAAGxP,EAAGmQ,IAAG,CAACX,GAAGW,KAAK9B,EAAE7vB,WAAU,KAAKwwB,GAAE/xB,QAAQ2yB,EAAA,GAAI,CAACA,KAAKvB,EAAE7vB,WAAU,KAAKiwB,GAAE,EAAE,GAAG,IAAIJ,EAAE7vB,WAAU,KAAK,IAAIkxB,EAAExX,GAAEjb,QAAQ,GAAGyyB,EAAE,CAAK,IAAA11B,EAAE01B,EAAEgB,wBAAwBxJ,OAAO,OAAO6H,GAAE/0B,GAAG8uB,GAAKsH,GAAA,CAAC,CAAC31B,QAAQe,EAAEC,GAAGyrB,OAAOltB,EAAEkM,SAAS1K,EAAE0K,aAAakqB,KAAI,IAAItH,GAAEsH,GAAGA,EAAEv0B,QAAUwD,KAAE5E,UAAUe,EAAEC,MAAI,IAAG,CAACqtB,EAAEttB,EAAEC,KAAK4yB,EAAExhB,iBAAgB,KAAK,IAAI2hB,EAAE,OAAO,IAAIkB,EAAExX,GAAEjb,QAAQjD,EAAE01B,EAAEjqB,MAAMyhB,OAAOwI,EAAEjqB,MAAMyhB,OAAO,OAAW,IAAAkJ,EAAEV,EAAEgB,wBAAwBxJ,OAASwI,EAAAjqB,MAAMyhB,OAAOltB,EAAE+0B,GAAEqB,GAAGtH,MAAKzpB,EAAEoV,SAAQ5M,EAAEpN,UAAUe,EAAEC,KAAI4D,EAAE9D,KAAIsM,GAAGA,EAAEpN,UAAUe,EAAEC,GAAG,IAAIoM,EAAEqf,OAAOkJ,GAAGvoB,IAAG,CAAC,CAACpN,QAAQe,EAAEC,GAAGyrB,OAAOkJ,EAAElqB,SAAS1K,EAAE0K,aAAa7G,IAAE,GAAG,CAACmvB,EAAEhzB,EAAEgpB,MAAMhpB,EAAEipB,YAAYqE,EAAEttB,EAAEC,KAAS,IAAAk1B,GAAEtC,EAAEzvB,aAAY,KAAK4hB,GAAG,GAAIsO,GAAEgB,GAAE7yB,SAAS6rB,GAAK4G,KAAE7zB,QAAO7B,GAAGA,EAAES,UAAUe,EAAEC,OAAKb,YAAW,KAAKkvB,EAAEtuB,EAAC,GAA//D,IAAogE,GAAG,CAACA,EAAEsuB,EAAEhB,EAAEgH,KAAm+B,OAA/9BzB,EAAE7vB,WAAU,KAAQ,GAAAhD,EAAE8tB,SAAa,YAAJ8F,IAAe5zB,EAAEwE,WAAW,KAAc,YAATxE,EAAET,KAAiB,OAAW,IAAA20B,EAAE,OAAOxF,GAAGrB,GAAGqF,GAAImC,GAAA,MAAY,GAAAN,GAAG9yB,QAAQ4yB,GAAG5yB,QAAQ,CAAC,IAAIoC,OAAMiK,MAAOC,UAAUsmB,GAAG5yB,QAAU+xB,GAAA/xB,QAAQ+xB,GAAE/xB,QAAQoC,CAAC,CAAC0wB,GAAG9yB,SAAQ,IAAIqM,MAAOC,SAAS,EAA9H,GAAyIylB,GAAE/xB,UAAU,MAAM4yB,GAAG5yB,SAAQ,IAAIqM,MAAOC,UAAUmmB,EAAE90B,YAAW,KAAS,IAAAyE,EAAqB,OAAlBA,EAAE7D,EAAEo1B,cAAoBvxB,EAAE+qB,KAAK5uB,EAAEA,GAAGm1B,IAAG,GAAE3B,GAAE/xB,UAAc,IAAIoM,aAAaqmB,EAAC,GAAG,CAACxF,EAAErB,EAAErtB,EAAE4zB,GAAElB,EAAGmC,GAAGM,KAAItC,EAAE7vB,WAAU,KAAKhD,EAAEX,QAAQ81B,IAAG,GAAE,CAACA,GAAEn1B,EAAEX,SAA2hBwzB,EAAEvH,cAAc,KAAK,CAACthB,SAAS,EAAElI,IAAI4a,GAAEpG,UAAU0Z,GAAE4B,EAAGkC,GAAM,MAAH1B,OAAQ,EAAOA,EAAEvyB,MAAwC,OAAjCqwB,EAAM,MAAHlwB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAOwS,EAAGrwB,MAAS,MAAHuyB,OAAQ,EAAOA,EAAE7J,QAAW,MAAH6J,OAAQ,EAAOA,EAAEwB,IAAqC,OAAjCzD,EAAM,MAAHnwB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAOyS,EAAGyD,KAAI,oBAAoB,GAAG,mBAAsC,OAAlBxD,EAAGpwB,EAAEq1B,YAAkBjF,EAAG5B,EAAE,gBAAgBxuB,EAAEgC,KAAKhC,EAAE6wB,UAAUjtB,GAAG,eAAeovB,EAAE,iBAAiBhzB,EAAE8tB,QAAQ,cAAcxI,EAAE,eAAe4N,EAAE,eAAeS,GAAG,kBAAkBc,GAAG,kBAAkBC,GAAG,aAAanH,EAAE,aAAamG,GAAG,eAAeP,EAAE,mBAAmBU,GAAE,YAAYD,GAAE,cAAcoB,GAAG,iBAAiB5B,EAAG,uBAAuBN,EAAG,mBAAmBpE,GAAGyD,GAAGa,GAAG/oB,MAAM,CAAC,UAAUsjB,EAAE,kBAAkBA,EAAE,YAAYvP,EAAEjX,OAAOwmB,EAAE,WAAW,GAAG2F,EAAEG,GAAEiB,GAAE7yB,YAAY,mBAAmB0wB,EAAE,OAAO,GAAGhO,UAASoN,KAAMvxB,EAAEiK,OAAOqrB,UAAU,KAAKhS,GAAE,GAAIsP,EAAE,MAAM4B,GAAE/yB,QAAQ,MAAMuN,cAAiBklB,IAACe,KAAKpB,KAAIJ,GAAEhyB,QAAY,IAAAqM,KAAKwlB,GAAEgB,GAAE7yB,SAASyyB,EAAE9qB,OAAOkH,kBAAkB4jB,EAAE3jB,WAA8B,WAAnB2jB,EAAE9qB,OAAOyL,UAAqByO,GAAE,GAAIkR,GAAE/yB,QAAQ,CAAC4K,EAAE6nB,EAAEhlB,QAAQ5C,EAAE4nB,EAAE/kB,UAAO,EAAKsB,YAAY,KAAS,IAAApE,EAAEkpB,EAAEC,EAAEC,EAAK,GAAArC,IAAKS,GAAE,OAAOW,GAAE/yB,QAAQ,KAAS,IAAAyyB,EAAExzB,QAAuB,OAAd2L,EAAEqQ,GAAEjb,cAAe,EAAO4K,EAAEpC,MAAMyrB,iBAAiB,oBAAoBhvB,QAAQ,KAAK,MAAM,GAAGlI,EAAEkC,QAAuB,OAAd60B,EAAE7Y,GAAEjb,cAAe,EAAO8zB,EAAEtrB,MAAMyrB,iBAAiB,oBAAoBhvB,QAAQ,KAAK,MAAM,GAAGkuB,GAAA,IAAM9mB,MAAOC,WAA0B,OAAdynB,EAAE/B,GAAEhyB,cAAe,EAAO+zB,EAAEznB,WAAWlK,EAAM,MAAJ8uB,EAAQuB,EAAE11B,EAAEm3B,EAAEjmB,KAAKc,IAAI3M,GAAG+wB,EAAE,GAAGllB,KAAKc,IAAI3M,IAAt3I,IAA83I8xB,EAAE,IAAmH,OAA9GrC,GAAEgB,GAAE7yB,SAA0B,OAAhBg0B,EAAEz1B,EAAE41B,YAAkBH,EAAE7G,KAAK5uB,EAAEA,GAAG+yB,EAAM,MAAJJ,EAAQuB,EAAE,EAAE,QAAQ,OAAO11B,EAAE,EAAE,OAAO,MAAM22B,KAAItJ,GAAE,QAAIvf,IAAE,GAAagX,GAAA,GAAIsP,EAAE,KAAI,EAAGxjB,cAAiB8kB,IAAK,IAAAqB,EAAEC,EAAEC,EAAEI,EAAG,IAAIrB,GAAE/yB,UAAUoyB,KAA+B,OAA1B0B,EAAEn3B,OAAO03B,qBAAsB,EAAOP,EAAE30B,WAAWmG,QAAQ,EAAE,OAAO,IAAI6tB,EAAEV,EAAE/kB,QAAQqlB,GAAE/yB,QAAQ6K,EAAEzI,EAAEqwB,EAAEhlB,QAAQslB,GAAE/yB,QAAQ4K,EAAEspB,EAAyB,OAAtBH,EAAErK,EAAE4K,iBAAuBP,EAArpJ,SAAYrK,GAAM,IAACC,EAAEprB,GAAGmrB,EAAElT,MAAM,KAAKrU,EAAE,GAAU,OAAAwnB,GAAGxnB,EAAEsP,KAAKkY,GAAGprB,GAAG4D,EAAEsP,KAAKlT,GAAG4D,CAAC,CAA2kJoyB,CAAGjE,IAAKY,IAAIjjB,KAAKc,IAAI3M,GAAG,GAAG6L,KAAKc,IAAIokB,GAAG,IAAIhC,EAAEljB,KAAKc,IAAI3M,GAAG6L,KAAKc,IAAIokB,GAAG,IAAI,KAAK,IAAIvoB,EAAE,CAACA,EAAE,EAAEC,EAAE,GAAO,MAAJqmB,GAASgD,EAAEnmB,SAAS,QAAQmmB,EAAEnmB,SAAS,aAAammB,EAAEnmB,SAAS,QAAQolB,EAAE,GAAGe,EAAEnmB,SAAS,WAAWolB,EAAE,KAAKvoB,EAAEC,EAAEsoB,GAAO,MAAJjC,IAAUgD,EAAEnmB,SAAS,SAASmmB,EAAEnmB,SAAS,YAAYmmB,EAAEnmB,SAAS,SAAS3L,EAAE,GAAG8xB,EAAEnmB,SAAS,UAAU3L,EAAE,KAAKwI,EAAEA,EAAExI,IAAI6L,KAAKc,IAAInE,EAAEA,GAAG,GAAGqD,KAAKc,IAAInE,EAAEC,GAAG,IAAIA,IAAE,GAAmB,OAAdmpB,EAAE/Y,GAAEjb,UAAgBg0B,EAAExrB,MAAMwC,YAAY,mBAAmB,GAAGJ,EAAEA,OAAuB,OAAfwpB,EAAGnZ,GAAEjb,UAAgBo0B,EAAG5rB,MAAMwC,YAAY,mBAAmB,GAAGJ,EAAEC,MAAK,GAAI6nB,KAAKn0B,EAAEgC,IAAI6wB,EAAEvH,cAAc,SAAS,CAAC,aAAakH,EAAG,gBAAgByC,GAAG,qBAAoB,EAAGtiB,QAAQsiB,KAAKpB,GAAE,OAAO,KAAS,IAAAK,EAAGiB,KAAoB,OAAhBjB,EAAEl0B,EAAE41B,YAAkB1B,EAAEtF,KAAK5uB,EAAEA,EAAC,EAAGsW,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAEf,YAA8C,OAAjChB,EAAM,MAAHrwB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAO2S,EAAGgB,cAA2C,OAA5Bf,EAAM,MAAHgC,OAAQ,EAAOA,EAAE2D,OAAa3F,EAAGpE,IAAI,KAAKlsB,EAAEgC,KAAKk0B,iBAAGl2B,EAAEgpB,OAAOhpB,EAAEgC,IAAIhC,EAAEgC,IAAoB,mBAAThC,EAAEgpB,MAAkBhpB,EAAEgpB,QAAQhpB,EAAEgpB,MAAM6J,EAAEvH,cAAcuH,EAAEzkB,SAAS,KAAKwlB,IAAG5zB,EAAEm2B,MAAMn2B,EAAE8tB,QAAQ+E,EAAEvH,cAAc,MAAM,CAAC,YAAY,GAAGhV,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAE+D,KAAuC,OAAjC5F,EAAM,MAAHvwB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAO6S,EAAG4F,OAAOn2B,EAAE8tB,SAAkB,YAAT9tB,EAAET,OAAmBS,EAAEm2B,KAAKn2B,EAAEm2B,OAA5yG,MAAH7D,GAASA,EAAEzE,QAAQgF,EAAEvH,cAAc,MAAM,CAAChV,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAEgE,OAAwC,OAAhClC,GAAK,MAAHl0B,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAOwW,GAAEkC,OAAO,iBAAiB,eAAmB,YAAJxC,IAAetB,EAAEzE,SAASoE,EAAGY,EAAEvH,cAAc,MAAM,CAAChV,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAEgE,OAAwC,OAAhC53B,GAAK,MAAHwB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAOlf,GAAE43B,OAAO,iBAAiB,eAAmB,YAAJxC,IAAe3B,GAAIY,EAAEvH,cAAcL,GAAG,CAAC3U,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAEgE,OAAwC,OAAhCxB,GAAK,MAAH50B,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAOkX,GAAEwB,QAAQlL,QAAY,YAAJ0I,MAA81F,KAAc,YAAT5zB,EAAET,KAAiBS,EAAEm2B,OAAU,MAAH7D,OAAQ,EAAOA,EAAEsB,MADt1L,CAAGzI,IAAC,OAAOA,GAAG,IAAI,UAAiB,OAAAI,GAAG,IAAI,OAAc,OAAAS,GAAG,IAAI,UAAiB,OAAAD,GAAG,IAAI,QAAe,OAAAE,GAAG,QAAe,YAAI,EAC+tLoK,CAAGzC,IAAG,MAAM,KAAKf,EAAEvH,cAAc,MAAM,CAAC,eAAe,GAAGhV,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAEpO,QAA0C,OAAjCwM,EAAM,MAAHxwB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAO8S,EAAGxM,UAAU6O,EAAEvH,cAAc,MAAM,CAAC,aAAa,GAAGhV,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAEpJ,MAAwC,OAAjCyH,EAAM,MAAHzwB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAO+S,EAAGzH,QAAwB,mBAAThpB,EAAEgpB,MAAkBhpB,EAAEgpB,QAAQhpB,EAAEgpB,OAAOhpB,EAAEipB,YAAY4J,EAAEvH,cAAc,MAAM,CAAC,mBAAmB,GAAGhV,UAAU0Z,GAAE8B,EAAGiC,GAAM,MAAH3B,OAAQ,EAAOA,EAAEnJ,YAA8C,OAAjCyH,EAAM,MAAH1wB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAOgT,EAAGzH,cAAoC,mBAAfjpB,EAAEipB,YAAwBjpB,EAAEipB,cAAcjpB,EAAEipB,aAAa,MAAMiN,EAAE/H,eAACnuB,EAAEs2B,QAAQt2B,EAAEs2B,OAAOt2B,EAAEs2B,QAAQpH,GAAGlvB,EAAEs2B,QAAQzD,EAAEvH,cAAc,SAAS,CAAC,eAAc,EAAG,eAAc,EAAGrhB,MAAMjK,EAAEwxB,mBAAmBC,EAAG9e,QAAWuhB,IAAC,IAAI11B,EAAEo2B,EAAE1F,GAAGlvB,EAAEs2B,SAASzC,KAA8B,OAAzBe,GAAGp2B,EAAEwB,EAAEs2B,QAAQ3jB,UAAgBiiB,EAAEhG,KAAKpwB,EAAE01B,GAAGiB,KAAC,EAAK7e,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAEmE,aAA+C,OAAjC5F,EAAM,MAAH3wB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAOiT,EAAG4F,eAAev2B,EAAEs2B,OAAO/xB,OAAO,KAAK2xB,iBAAGl2B,EAAEL,QAAQK,EAAEL,OAAOK,EAAEL,QAAQuvB,GAAGlvB,EAAEL,QAAQkzB,EAAEvH,cAAc,SAAS,CAAC,eAAc,EAAG,eAAc,EAAGrhB,MAAMjK,EAAE0xB,mBAAmBC,EAAEhf,QAAWuhB,IAAC,IAAI11B,EAAEo2B,EAAE1F,GAAGlvB,EAAEL,UAAoC,OAAzBi1B,GAAGp2B,EAAEwB,EAAEL,QAAQgT,UAAgBiiB,EAAEhG,KAAKpwB,EAAE01B,IAAIA,EAAEnlB,kBAAkBomB,KAAG,EAAG7e,UAAU0Z,GAAK,MAAHoC,OAAQ,EAAOA,EAAEoE,aAA+C,OAAjC5F,EAAM,MAAH5wB,OAAQ,EAAOA,EAAE0d,iBAAkB,EAAOkT,EAAG4F,eAAex2B,EAAEL,OAAO4E,OAAO,OAAlpJ,IAAkB2vB,GAAE11B,GAAEo2B,EAAioJ,EAAG,SAAS6B,KAAK,GAAkB,oBAARr4B,QAAsC,oBAAViJ,SAA4B,YAAM,IAAI8jB,EAAE9jB,SAAS8iB,gBAAgBuM,aAAa,OAAc,MAAI,SAAJvL,GAAaA,EAA8DA,EAA5D/sB,OAAOu4B,iBAAiBtvB,SAAS8iB,iBAAiBlW,SAAW,CAAC,SAAS2iB,GAAGzL,EAAEC,GAAG,IAAIprB,EAAE,GAAG,MAAM,CAACmrB,EAAEC,GAAGlrB,SAAQ,CAAC0D,EAAEypB,KAAS,IAAAC,EAAM,IAAJD,EAAMjI,EAAEkI,EAAE,kBAAkB,WAAWH,EAAEG,EAAt+O,OAAV,OAAw/O,SAASC,EAAEvP,GAAG,CAAC,MAAM,QAAQ,SAAS,QAAQ9d,SAAWwuB,IAAG1uB,EAAA,GAAGolB,KAAKsJ,KAAe,iBAAH1Q,EAAY,GAAGA,MAAMA,CAAA,GAAG,CAAW,iBAAHpa,GAAuB,iBAAHA,EAAY2pB,EAAE3pB,GAAa,iBAAHA,EAAY,CAAC,MAAM,QAAQ,SAAS,QAAQ1D,SAAW8d,SAAQ,IAAPpa,EAAEoa,GAAYhe,EAAE,GAAGolB,KAAKpH,KAAKmP,EAAEntB,EAAE,GAAGolB,KAAKpH,KAAkB,iBAANpa,EAAEoa,GAAa,GAAGpa,EAAEoa,OAAOpa,EAAEoa,EAAC,IAAIuP,EAAEJ,EAAC,IAAIntB,CAAC,CAAiV,IAAI62B,GAAGC,cAAG,SAAS1L,EAAEprB,GAAG,IAAIkhB,OAAOtd,EAAE8G,SAAS2iB,EAAE,eAAennB,OAAOonB,EAAE,CAAC,SAAS,QAAQyJ,OAAO3R,EAAEiM,YAAYlE,EAAE7W,UAAUiX,EAAEyJ,OAAOhZ,EAAEiZ,aAAavI,EAAEjV,MAAM6U,EAAE,QAAQ+G,WAAW7G,EAAEhqB,SAAS8sB,EAAGrnB,MAAMsnB,EAAGP,cAAcS,EAAGlN,GAAG2S,aAAavF,EAAEwF,IAAIvF,EAAG6E,KAAKtV,IAAI2Q,EAAG/B,GAAGiC,YAAYrJ,EAAE0J,MAAMN,EAAGqF,mBAAmB5S,EAAG,gBAAgBiO,sBAAsBR,GAAI7G,GAAG+G,EAAEC,GAAGS,EAAEtJ,SAAS,IAAI+I,EAAEO,EAAEoB,SAAQ,IAAI1wB,MAAMC,KAAK,IAAIib,IAAI,CAAC4O,GAAGgK,OAAOlF,EAAE9xB,WAAUwrB,EAAEnhB,WAAU3K,KAAI8rB,GAAGA,EAAEnhB,eAAa,CAACynB,EAAE9E,KAAKmF,EAAGE,GAAIG,EAAEtJ,SAAS,KAAKoJ,EAAEC,GAAGC,EAAEtJ,UAAS,IAAKuJ,EAAGC,GAAGF,EAAEtJ,UAAS,IAAKyJ,EAAEC,GAAGJ,EAAEtJ,SAAa,WAAJ+E,EAAaA,EAAiB,oBAARlwB,QAAqBA,OAAOyrB,YAAYzrB,OAAOyrB,WAAW,gCAAgCC,QAAQ,OAAO,SAASoJ,EAAEL,EAAE9wB,OAAO,MAAMijB,EAAGsI,EAAE7mB,KAAK,KAAKC,QAAQ,OAAO,IAAIA,QAAQ,SAAS,IAAIysB,EAAEN,EAAE9wB,OAAO,MAAMuhB,EAAEuP,EAAE9wB,QAAO,GAAIqxB,EAAGP,EAAEzvB,aAAeyoB,IAACuG,GAAK9M,IAAK,IAAAhZ,EAAS,OAA2B,OAA3BA,EAAEgZ,EAAErM,MAAQoa,KAAEpzB,KAAK4rB,EAAE5rB,OAAYqM,EAAEjN,QAAQstB,GAAE9rB,QAAQgrB,EAAE5rB,IAAIqlB,EAAEjlB,QAAO,EAAEJ,GAAGozB,KAAKA,IAAIxH,EAAE5rB,IAAE,GAAE,GAAG,IAAI,OAAO4yB,EAAE7vB,WAAU,IAAI2pB,GAAEG,WAAajB,IAAIA,EAAEhrB,QAASuxB,GAAK9M,KAAEvlB,KAAIuM,GAAGA,EAAErM,KAAK4rB,EAAE5rB,GAAG,IAAIqM,EAAEjN,QAAO,GAAIiN,MAAWlN,YAAW,KAAKk4B,EAAGC,WAAU,KAAKnF,GAAK9M,IAAC,IAAIhZ,EAAEgZ,EAAE/b,cAAa8pB,EAAEpzB,KAAK4rB,EAAE5rB,KAAW,OAAO,IAAPqM,EAAO,IAAIgZ,EAAExlB,MAAM,EAAEwM,GAAG,IAAIgZ,EAAEhZ,MAAMuf,MAAMvG,EAAExlB,MAAMwM,EAAE,IAAI,CAACuf,KAAKvG,EAAC,GAAE,GAAE,GAAE,KAAI,IAAIuN,EAAE7vB,WAAU,KAAK,GAAO,WAAJsrB,EAAmB,YAAL2E,EAAE3E,GAAU,GAAO,WAAJA,IAAelwB,OAAOyrB,YAAYzrB,OAAOyrB,WAAW,gCAAgCC,QAAQmJ,EAAE,QAAQA,EAAE,UAAyB,oBAAR70B,OAAoB,OAAW,IAAAytB,EAAEztB,OAAOyrB,WAAW,gCAAmC,IAACgC,EAAEvkB,iBAAiB,UAAS,EAAEwiB,QAAQxE,MAAQ2N,EAAA3N,EAAE,OAAO,QAAO,GAAG,OAAOA,GAAGuG,EAAE2L,aAAY,EAAE1N,QAAQxd,MAAS,IAAG2mB,EAAA3mB,EAAE,OAAO,QAAQ,OAAO+mB,GAAGjuB,QAAQC,MAAMguB,EAAE,IAAG,IAAG,CAAC/E,IAAIuE,EAAE7vB,WAAU,KAAOmvB,EAAAprB,QAAQ,GAAG6rB,GAAE,EAAE,GAAG,CAACT,IAAIU,EAAE7vB,WAAU,KAAK,IAAI6oB,EAAKvG,IAAC,IAAI+N,EAAEC,EAAEhG,EAAEtmB,OAASmd,GAAAmB,EAAEnB,IAAImB,EAAEpe,OAAOid,MAAKyO,GAAE,GAAmB,OAAdS,EAAEH,EAAEzxB,UAAgB4xB,EAAEjsB,SAAkB,WAATke,EAAEpe,OAAkBG,SAASc,gBAAgB+qB,EAAEzxB,SAAwB,OAAd6xB,EAAEJ,EAAEzxB,UAAgB6xB,EAAEtrB,SAASX,SAASc,iBAAiByqB,GAAE,EAAE,EAAU,OAAAvrB,SAASC,iBAAiB,UAAUukB,GAAG,IAAIxkB,SAASE,oBAAoB,UAAUskB,EAAC,GAAG,CAACyB,IAAIuF,EAAE7vB,WAAU,KAAQ,GAAAkwB,EAAEzxB,QAAQ,MAAM,KAAK0xB,EAAE1xB,UAAU0xB,EAAE1xB,QAAQ2F,MAAM,CAACqwB,eAAc,IAAKtE,EAAE1xB,QAAQ,KAAK6hB,EAAE7hB,SAAQ,GAAG,GAAG,CAACyxB,EAAEzxB,UAAUoxB,EAAEvH,cAAc,UAAU,CAACxpB,IAAI9B,EAAE,aAAa,GAAGwkB,KAAMQ,IAAKhb,UAAS,EAAG,YAAY,SAAS,gBAAgB,iBAAiB,cAAc,QAAQ0tB,0BAAyB,GAAIpF,EAAEvyB,KAAI,CAAC8rB,EAAEvG,KAAS,IAAAgO,EAAE,IAAIhnB,EAAE+mB,GAAGxH,EAAE5T,MAAM,KAAK,OAAOka,EAAEprB,OAAO8rB,EAAEvH,cAAc,KAAK,CAACrkB,IAAI4kB,EAAEsL,IAAS,SAALvF,EAAY6E,KAAK7E,EAAG5nB,UAAY,EAAAlI,IAAIoxB,EAAE5c,UAAUiX,EAAE,uBAAsB,EAAG,aAAayF,EAAE,kBAAkB1mB,EAAE,cAAcqmB,GAAGR,EAAEprB,OAAO,IAAIqe,EAAE,kBAAkBiO,EAAEppB,MAAM,CAAC,uBAAuB,IAAe,OAAVqpB,EAAEd,EAAG,SAAU,EAAOc,EAAE5H,SAAS,MAAM,UAAU,QAAU,QAAQ,GAAGoG,SAAUP,KAAMqF,GAAG5Y,EAAE0Q,IAAIiJ,OAAUxT,IAAGb,EAAA7hB,UAAU0iB,EAAEjY,cAAclE,SAASmc,EAAElc,iBAAiBqb,EAAE7hB,SAAQ,EAAG0xB,EAAE1xB,UAAU0xB,EAAE1xB,QAAQ2F,MAAM,CAACqwB,eAAc,IAAKtE,EAAE1xB,QAAQ,QAAQkJ,QAAWwZ,IAACA,EAAE/a,kBAAkBwuB,aAA4C,UAA/BzT,EAAE/a,OAAOsK,QAAQ8Z,aAAuBlK,EAAE7hB,UAAU6hB,EAAE7hB,SAAQ,EAAG0xB,EAAE1xB,QAAQ0iB,EAAElc,cAAA,EAAgB4vB,aAAa,IAAIjF,GAAE,GAAIkF,YAAY,IAAIlF,GAAE,GAAImF,aAAa,KAAKjF,GAAIF,GAAE,EAAE,EAAG0C,UAAU,IAAI1C,GAAE,GAAI5jB,cAAiBmV,IAAGA,EAAA/a,kBAAkBwuB,aAA4C,UAA/BzT,EAAE/a,OAAOsK,QAAQ8Z,aAAuBuF,GAAE,EAAE,EAAGtiB,YAAY,IAAIsiB,GAAE,IAAKZ,EAAE9xB,QAAU8jB,IAACA,EAAEzZ,UAAc,IAAJ4a,GAAOnB,EAAEzZ,WAAWmhB,IAAG9rB,KAAI,CAACokB,EAAEoP,KAAK,IAAIC,EAAEC,EAASZ,SAAEvH,cAAc2E,GAAG,CAAChpB,IAAIkd,EAAElkB,GAAGoyB,MAAMN,EAAGzoB,MAAMiqB,EAAE1zB,MAAMskB,EAAEiN,kBAAkB5C,EAAEhqB,SAAwC,OAA9BgvB,EAAK,MAAH7B,OAAQ,EAAOA,EAAEntB,UAAgBgvB,EAAElC,EAAGhb,UAAa,MAAHqb,OAAQ,EAAOA,EAAErb,UAAUub,qBAAwB,MAAHF,OAAQ,EAAOA,EAAEE,qBAAqB3Q,OAAOtd,EAAEotB,cAAcS,EAAGJ,YAA8C,OAAjCoC,EAAK,MAAH9B,OAAQ,EAAOA,EAAEN,aAAmBoC,EAAEtG,EAAE2D,YAAYgC,EAAGpoB,SAASmhB,EAAE5hB,MAAS,MAAH0nB,OAAQ,EAAOA,EAAE1nB,MAAM4mB,SAAY,MAAHc,OAAQ,EAAOA,EAAEd,SAASnT,WAAc,MAAHiU,OAAQ,EAAOA,EAAEjU,WAAW8T,kBAAqB,MAAHG,OAAQ,EAAOA,EAAEH,kBAAkBE,kBAAqB,MAAHC,OAAQ,EAAOA,EAAED,kBAAkBP,YAAYiC,EAAGxzB,OAAOuyB,EAAE9xB,QAAOqc,GAAGA,EAAEhS,UAAUyZ,EAAEzZ,WAAUumB,QAAQuB,EAAGnyB,QAAOqc,GAAGA,EAAEhS,UAAUyZ,EAAEzZ,WAAUqmB,WAAW2B,EAAGR,gBAAgB9M,EAAEjE,IAAI2Q,EAAGE,YAAYrJ,EAAEuI,SAASyB,EAAEF,sBAAsBR,EAAG8D,gBAAgB3K,EAAE2K,iBAAgB,KAAK,QAAO,ICIjmY,MAAMnN,GAAU,KAAMnoB,MACpB,MAAQgZ,QAAQ,UFOM,MAChBjX,QAAUw1B,aAAW9O,IAC3B,IAAK1mB,EACG,UAAIy1B,MAAM,gDAEXz1B,UEZsB01B,gBAG1BC,IACC1e,QACAnD,UAAU,gBACV5L,SAAS,aACTqsB,QAAQ,EACR1B,YAAU,EACVhE,aAAW,EACX6F,aAAc,CACZ1yB,SAAU,IACVkZ,WAAY,CACV7d,MACE,8MACFopB,YAAa,2CACbuN,aACE,qEACFD,aACE,mFACFlF,YACE,gIACF3D,QACE,mNACFroB,MACE,uMACFuoB,QACE,yNACFD,KACE,8MAEJ1jB,MAAO,CACL2W,aAAc,OACda,QAAS,OACT2W,SAAU,OACVC,WAAY,WAGZ53B,KCvCJ63B,GAAkBC,YAMDl6B,EAGrBiE,YAAA,EAAGgU,YAAWkiB,aAAa,KAAM/3B,GAASqB,IAC1C2mB,MAAC8P,EAAwB,CACvBz2B,MACA02B,aACAliB,UAAW6R,GACT,qYACA7R,MAEE7V,MAGOwB,YAAcs2B,EAAyBt2B,uDCvBlD,IAAAw2B,EAAoC,oBAAZC,QACxBC,EAAwB,mBAAR55B,IAChB65B,EAAwB,mBAARna,IAChBoa,EAAwC,mBAAhBC,eAAgCA,YAAYC,OAI/D,SAAAC,EAAMp1B,EAAGC,GAEZ,GAAAD,IAAMC,EAAU,SAEpB,GAAID,GAAKC,GAAiB,iBAALD,GAA6B,iBAALC,EAAe,CAC1D,GAAID,EAAEgpB,cAAgB/oB,EAAE+oB,YAAoB,SAE5C,IAAI7lB,EAAQiX,EAAGtH,EA6BXgc,EA5BA,GAAAnvB,MAAMkU,QAAQ7T,GAAI,CAEhB,IADJmD,EAASnD,EAAEmD,SACGlD,EAAEkD,OAAe,SAC1B,IAAAiX,EAAIjX,EAAgB,IAARiX,KACX,IAACgb,EAAMp1B,EAAEoa,GAAIna,EAAEma,IAAY,SAC1B,SAwBT,GAAI2a,GAAW/0B,aAAa7E,KAAS8E,aAAa9E,IAAM,CACtD,GAAI6E,EAAE2hB,OAAS1hB,EAAE0hB,KAAa,SAE9B,IADAmN,EAAK9uB,EAAEoT,YACEgH,EAAI0U,EAAGuG,QAAQC,UACjBr1B,EAAE3E,IAAI8e,EAAEnI,MAAM,IAAY,SAEjC,IADA6c,EAAK9uB,EAAEoT,YACEgH,EAAI0U,EAAGuG,QAAQC,UACjBF,EAAMhb,EAAEnI,MAAM,GAAIhS,EAAEgV,IAAImF,EAAEnI,MAAM,KAAa,SAC7C,SAGT,GAAI+iB,GAAWh1B,aAAa6a,KAAS5a,aAAa4a,IAAM,CACtD,GAAI7a,EAAE2hB,OAAS1hB,EAAE0hB,KAAa,SAE9B,IADAmN,EAAK9uB,EAAEoT,YACEgH,EAAI0U,EAAGuG,QAAQC,UACjBr1B,EAAE3E,IAAI8e,EAAEnI,MAAM,IAAY,SAC1B,SAIL,GAAAgjB,GAAkBC,YAAYC,OAAOn1B,IAAMk1B,YAAYC,OAAOl1B,GAAI,CAEhE,IADJkD,EAASnD,EAAEmD,SACGlD,EAAEkD,OAAe,SAC1B,IAAAiX,EAAIjX,EAAgB,IAARiX,KACf,GAAIpa,EAAEoa,KAAOna,EAAEma,GAAW,SACrB,SAGL,GAAApa,EAAEgpB,cAAgBuM,OAAe,OAAAv1B,EAAEw1B,SAAWv1B,EAAEu1B,QAAUx1B,EAAEy1B,QAAUx1B,EAAEw1B,MAK5E,GAAIz1B,EAAE01B,UAAY7iB,OAAO8iB,UAAUD,SAAgC,mBAAd11B,EAAE01B,SAA+C,mBAAdz1B,EAAEy1B,QAAwB,OAAO11B,EAAE01B,YAAcz1B,EAAEy1B,UAC3I,GAAI11B,EAAEhD,WAAa6V,OAAO8iB,UAAU34B,UAAkC,mBAAfgD,EAAEhD,UAAiD,mBAAfiD,EAAEjD,SAAyB,OAAOgD,EAAEhD,aAAeiD,EAAEjD,WAKhJ,IADAmG,GADO2P,EAAAD,OAAOC,KAAK9S,IACLmD,UACC0P,OAAOC,KAAK7S,GAAGkD,OAAe,SAExC,IAAAiX,EAAIjX,EAAgB,IAARiX,KACX,IAACvH,OAAO8iB,UAAUC,eAAe5K,KAAK/qB,EAAG6S,EAAKsH,IAAY,SAK5D,GAAAya,GAAkB70B,aAAa80B,QAAgB,SAG9C,IAAA1a,EAAIjX,EAAgB,IAARiX,KACf,IAAiB,WAAZtH,EAAKsH,IAA+B,QAAZtH,EAAKsH,IAA4B,QAAZtH,EAAKsH,KAAiBpa,EAAE61B,YAarET,EAAMp1B,EAAE8S,EAAKsH,IAAKna,EAAE6S,EAAKsH,KAAa,SAKtC,SAGF,OAAApa,GAAMA,GAAKC,GAAMA,CAC1B,QAGiB61B,GAAA,SAAiB91B,EAAGC,GAC/B,IACK,OAAAm1B,EAAMp1B,EAAGC,SACTwB,GACP,IAAMA,EAAM+nB,SAAW,IAAIuM,MAAM,oBAOxB,OADPv0B,QAAQw0B,KAAK,mDACN,EAGH,MAAAv0B,CAAA,CAEV,uCC1FiBw0B,GA5BD,SAASC,EAAWC,EAAQn2B,EAAGC,EAAG+wB,EAAG/I,EAAGT,EAAGkC,GAOzD,IAAKwM,EAAW,CACV,IAAAz0B,EACJ,QAAe,IAAX00B,EACF10B,EAAQ,IAAI4yB,MACV,qIAGG,CACL,IAAI+B,EAAO,CAACp2B,EAAGC,EAAG+wB,EAAG/I,EAAGT,EAAGkC,GACvB2M,EAAW,GACf50B,EAAQ,IAAI4yB,MACV8B,EAAOrzB,QAAQ,OAAO,WAAa,OAAOszB,EAAKC,IAAc,MAEzDh5B,KAAO,sBAIT,MADNoE,EAAM60B,YAAc,EACd70B,CAAA,CAEV,qCC5Cc80B,GAAG,SAAsBC,EAAMC,EAAMC,EAASC,GAC1D,IAAIC,EAAMF,EAAUA,EAAQ1L,KAAK2L,EAAgBH,EAAMC,QAAQ,EAE/D,QAAY,IAARG,EACF,QAASA,EAGX,GAAIJ,IAASC,EACJ,SAGL,GAAgB,iBAATD,IAAsBA,GAAwB,iBAATC,IAAsBA,EAC7D,SAGL,IAAAI,EAAQhkB,OAAOC,KAAK0jB,GACpBM,EAAQjkB,OAAOC,KAAK2jB,GAEpB,GAAAI,EAAM1zB,SAAW2zB,EAAM3zB,OAClB,SAMT,IAHA,IAAI4zB,EAAkBlkB,OAAO8iB,UAAUC,eAAeoB,KAAKP,GAGlDQ,EAAM,EAAGA,EAAMJ,EAAM1zB,OAAQ8zB,IAAO,CACvC,IAAA5zB,EAAMwzB,EAAMI,GAEZ,IAACF,EAAgB1zB,GACZ,SAGL,IAAA6zB,EAASV,EAAKnzB,GACd8zB,EAASV,EAAKpzB,GAIlB,IAAY,KAFZuzB,EAAMF,EAAUA,EAAQ1L,KAAK2L,EAAgBO,EAAQC,EAAQ9zB,QAAO,SAEtC,IAARuzB,GAAkBM,IAAWC,EAC1C,QAEb,CAES,QACR,ICjCD,IAAIC,IAA8BC,IAChCA,EAAiB,KAAI,OACrBA,EAAiB,KAAI,OACrBA,EAAiB,KAAI,OACrBA,EAAiB,KAAI,OACrBA,EAAiB,KAAI,OACrBA,EAAiB,KAAI,OACrBA,EAAqB,SAAI,WACzBA,EAAmB,OAAI,SACvBA,EAAkB,MAAI,QACtBA,EAAkB,MAAI,QACtBA,EAAqB,SAAI,yBAClBA,IACND,IAAa,IACZE,GACI,CAAEC,IAAK,CAAC,UAAW,YAAa,cADpCD,GAEM,CAAE37B,KAAM,CAAC,wBAFf27B,GAGI,CACJE,QAAS,GACTn6B,KAAM,CAAC,YAAa,SAAU,eAC9BsY,SAAU,CACR,UACA,WACA,SACA,WACA,eACA,iBACA,cACA,gBACA,sBACA,gBACA,oBACA,eACA,iBAIF8hB,GAAkB5kB,OAAO/S,OAAOs3B,IAChCM,GAAgB,CAClBC,UAAW,YACXH,QAAS,UACT/kB,MAAO,YACPmlB,gBAAiB,kBACjBC,YAAa,cACb,aAAc,YACdC,SAAU,WACVC,SAAU,YAERC,GAAenlB,OAAOO,QAAQskB,IAAerkB,QAC/C,CAAC4kB,GAAQ50B,EAAK4O,MACZgmB,EAAMhmB,GAAS5O,EACR40B,IAET,IAEEC,GAAmB,UAGnBC,GACa,eADbA,GAEK,QAFLA,GAGyB,0BAHzBA,GAIsB,sBAJtBA,GAKc,gBALdA,GAMmB,oBAEnBC,GAAuB,CAACC,EAAW1iB,KACrC,QAASyE,EAAIie,EAAUl1B,OAAS,EAAGiX,GAAK,EAAGA,GAAK,EAAG,CAC3C,MAAAvd,EAAQw7B,EAAUje,GACxB,GAAIvH,OAAO8iB,UAAUC,eAAe5K,KAAKnuB,EAAO8Y,GAC9C,OAAO9Y,EAAM8Y,EAEnB,CACS,aAEL2iB,GAAyBD,IAC3B,IAAIE,EAAiBH,GAAqBC,EAAW,SACrD,MAAMG,EAAoBJ,GAAqBC,EAAWF,IAI1D,GAHIx4B,MAAMkU,QAAQ0kB,KACCA,IAAe11B,KAAK,KAEnC21B,GAAqBD,EACvB,OAAOC,EAAkB11B,QAAQ,OAAO,IAAMy1B,IAEhD,MAAME,EAAwBL,GAAqBC,EAAWF,IAC9D,OAAOI,GAAkBE,QAAyB,GAEhDC,GAA0BL,GAAcD,GAAqBC,EAAWF,WAC5E,GACIQ,GAA6B,CAACC,EAASP,IAAcA,EAAU57B,QAAQI,QAAoC,IAAnBA,EAAM+7B,KAA0Bz8B,KAAKU,GAAUA,EAAM+7B,KAAUvlB,QAAO,CAACwlB,EAAUh7B,KAAA,IAAkBg7B,KAAah7B,KAAY,IACpNi7B,GAA0B,CAACC,EAAmBV,IAAcA,EAAU57B,QAAQI,QAA8C,IAA7BA,SAA0CV,KAAKU,GAAUA,EAAwB,OAAEkI,UAAUsO,QAAO,CAAC2lB,EAAkBC,KACpN,IAACD,EAAiB71B,OAAQ,CACtB,MAAA2P,EAAOD,OAAOC,KAAKmmB,GACzB,QAAS7e,EAAI,EAAGA,EAAItH,EAAK3P,OAAQiX,GAAK,EAAG,CACjC,MACA8e,EADepmB,EAAKsH,GACiB+e,cAC3C,IAA+D,IAA3DJ,EAAkB74B,QAAQg5B,IAAiCD,EAAIC,GAC1D,OAAAF,EAAiBvF,OAAOwF,EAEvC,CACA,CACS,OAAAD,CAAA,GACN,IAECI,GAAuB,CAACnoB,EAAS8nB,EAAmBV,KACtD,MAAMgB,EAAmB,CAAE,EACpB,OAAAhB,EAAU57B,QAAQI,IACvB,QAAI8C,MAAMkU,QAAQhX,EAAMoU,WAGM,IAAnBpU,EAAMoU,KAPTqoB,EASJ,WAAWroB,2DAAiEpU,EAAMoU,MATtEzP,SAAmC,mBAAjBA,QAAQw0B,MAAuBx0B,QAAQw0B,KAAKsD,KAYvE,GAZA,IAACA,CAYD,IACNn9B,KAAKU,GAAUA,EAAMoU,KAAUlM,UAAUsO,QAAO,CAACkmB,EAAcC,KAChE,MAAMC,EAAmB,CAAE,EACdD,EAAA/8B,QAAQw8B,IACf,IAAAS,EACE,MAAAC,EAAQ9mB,OAAOC,KAAKmmB,GAC1B,QAAS7e,EAAI,EAAGA,EAAIuf,EAAMx2B,OAAQiX,GAAK,EAAG,CAClC,MAAAwf,EAAeD,EAAMvf,GACrB8e,EAAwBU,EAAaT,mBACvCJ,EAAkB74B,QAAQg5B,IAA2D,QAAxBQ,GAAsF,cAA3CT,EAAIS,GAAqBP,eAA8D,QAA1BD,GAA0F,eAA7CD,EAAIC,GAAuBC,gBACzNO,EAAAR,IAEwB,IAA5CH,EAAkB74B,QAAQ05B,IAA0C,cAAjBA,GAAkE,YAAjBA,GAA8D,aAAjBA,IAC7HF,EAAAE,EAEhC,CACM,IAAKF,IAAwBT,EAAIS,GACxB,SAET,MAAMznB,EAAQgnB,EAAIS,GAAqBP,cAOvC,OANKE,EAAiBK,KACHL,EAAAK,GAAuB,CAAE,GAEvCD,EAAiBC,KACHD,EAAAC,GAAuB,CAAE,IAEvCL,EAAiBK,GAAqBznB,KACxBwnB,EAAAC,GAAqBznB,IAAS,GACxC,EAEF,IACNlN,UAAUzI,SAAS28B,GAAQM,EAAajqB,KAAK2pB,KAC1C,MAAAnmB,EAAOD,OAAOC,KAAK2mB,GACzB,QAASrf,EAAI,EAAGA,EAAItH,EAAK3P,OAAQiX,GAAK,EAAG,CACjC,MAAAwf,EAAe9mB,EAAKsH,GACpByf,EAAW,IACZR,EAAiBO,MACjBH,EAAiBG,IAEtBP,EAAiBO,GAAgBC,CACvC,CACW,OAAAN,CAAA,GACN,IAAIx0B,SAAS,EAEd+0B,GAA0B,CAACzB,EAAW0B,KACxC,GAAIp6B,MAAMkU,QAAQwkB,IAAcA,EAAUl1B,OACxC,QAASuC,EAAQ,EAAGA,EAAQ2yB,EAAUl1B,OAAQuC,GAAS,EAAG,CAEpD,GADS2yB,EAAU3yB,GACdq0B,GACA,QAEf,CAES,UAoCLC,GAAgBC,GAAkBt6B,MAAMkU,QAAQomB,GAAiBA,EAAcp3B,KAAK,IAAMo3B,EAU1FC,GAAc,CAACC,EAAcC,IAC3Bz6B,MAAMkU,QAAQsmB,GACTA,EAAa9mB,QAClB,CAACC,EAAK+mB,KAZY,EAACx9B,EAAOy9B,KACxB,MAAAxnB,EAAOD,OAAOC,KAAKjW,GACzB,QAASud,EAAI,EAAGA,EAAItH,EAAK3P,OAAQiX,GAAK,EACpC,GAAIkgB,EAAQxnB,EAAKsH,KAAOkgB,EAAQxnB,EAAKsH,IAAIxO,SAAS/O,EAAMiW,EAAKsH,KACpD,SAGJ,UAMGmgB,CAAkBF,EAAcD,GAC9B9mB,EAAAknB,SAASlrB,KAAK+qB,GAEd/mB,EAAAqR,QAAQrV,KAAK+qB,GAEZ/mB,IAET,CAAEknB,SAAU,GAAI7V,QAAS,KAGtB,CAAEA,QAASwV,EAAcK,SAAU,IAExCC,GAAU,CAACC,EAAKr3B,KACX,IACFq3B,EACHr3B,CAACA,QAAM,IAKPs3B,GAAoB,CAAC,WAA2B,SAAuB,SACvEC,GAA0B,CAACC,EAAKC,GAAS,KAC5B,IAAXA,EACKC,OAAOF,GAETE,OAAOF,GAAK/3B,QAAQ,KAAM,SAASA,QAAQ,KAAM,QAAQA,QAAQ,KAAM,QAAQA,QAAQ,KAAM,UAAUA,QAAQ,KAAM,UAE1Hk4B,GAAqCC,GAAepoB,OAAOC,KAAKmoB,GAAY5nB,QAAO,CAACwnB,EAAKx3B,KAC3F,MAAM63B,OAAkC,IAApBD,EAAW53B,GAAuB,GAAGA,MAAQ43B,EAAW53B,MAAU,GAAGA,IACzF,OAAOw3B,EAAM,GAAGA,KAAOK,IAASA,CAAA,GAC/B,IAwBCC,GAAuC,CAACF,EAAYG,EAAY,CAAE,IAAKvoB,OAAOC,KAAKmoB,GAAY5nB,QAAO,CAACqnB,EAAKr3B,KAE9Gq3B,EADehD,GAAcr0B,IACfA,GAAO43B,EAAW53B,GACzBq3B,IACNU,GASCC,GAA+B,CAAC1/B,EAAM2/B,IAASA,EAAKn/B,KAAI,CAAC88B,EAAK7e,KAChE,MAAMmhB,EAAY,CAChBl4B,IAAK+W,EACL8d,CAACA,KAAmB,GAYfz9B,OAVPoY,OAAOC,KAAKmmB,GAAK38B,SAASk/B,IAClB,MACAC,EADS/D,GAAc8D,IACKA,EAC9B,GAAoB,cAApBC,GAAwE,YAApBA,EAA8C,CAC9F,MAAArb,EAAU6Y,EAAIyC,WAAazC,EAAIjN,QAC3BuP,EAAAI,wBAA0B,CAAEC,OAAQxb,EACpD,MACgBmb,EAAAE,GAAmBxC,EAAIuC,EACvC,IAES/gC,EAAMitB,cAAc/rB,EAAM4/B,EAAS,IAExCM,GAAmB,CAAClgC,EAAM2/B,EAAMR,GAAS,KAC3C,OAAQn/B,GACN,IAAK,QACI,OACLmgC,YAAa,IA7Be,EAACC,EAAO3W,EAAO6V,KACjD,MAIMp+B,EAAQs+B,GAAqCF,EAJjC,CAChB53B,IAAK+hB,EACL8S,CAACA,KAAmB,IAGtB,MAAO,CAACz9B,EAAMitB,cAAc,QAAqB7qB,EAAOuoB,GAAM,EAuBrC4W,CAA8BrgC,EAAM2/B,EAAKlW,MAAOkW,EAAKW,iBACxEj/B,SAAU,IA1DU,EAACrB,EAAMypB,EAAO6V,EAAYH,KAC9C,MAAAoB,EAAkBlB,GAAkCC,GACpDkB,EAAiBnC,GAAa5U,GACpC,OAAO8W,EAAkB,IAAIvgC,KAAQu8B,aAA2BgE,KAAmBtB,GACjFuB,EACArB,OACIn/B,KAAU,IAAIA,KAAQu8B,aAA2B0C,GACrDuB,EACArB,OACIn/B,IAAI,EAiDYygC,CAAsBzgC,EAAM2/B,EAAKlW,MAAOkW,EAAKW,gBAAiBnB,IAElF,IAAK,iBACL,IAAK,iBACI,OACLgB,YAAa,IAAMX,GAAqCG,GACxDt+B,SAAU,IAAMg+B,GAAkCM,IAEtD,QACS,OACLQ,YAAa,IAAMT,GAA6B1/B,EAAM2/B,GACtDt+B,SAAU,IA1DS,EAACrB,EAAM2/B,EAAMR,GAAS,IAASQ,EAAKjoB,QAAO,CAACwnB,EAAKz+B,KAC1E,MAAM68B,EAAM78B,EACNigC,EAAgBxpB,OAAOC,KAAKmmB,GAAKx8B,QACpC++B,KAA8B,cAAdA,GAA4D,YAAdA,KAC/DnoB,QAAO,CAACqF,EAAQ8iB,KAChB,MAAMN,OAAiC,IAAnBjC,EAAIuC,GAA6BA,EAAY,GAAGA,MAAcZ,GAAwB3B,EAAIuC,GAAYV,MAC1H,OAAOpiB,EAAS,GAAGA,KAAUwiB,IAASA,CAAA,GACrC,IACGoB,EAAarD,EAAIyC,WAAazC,EAAIjN,SAAW,GAC7CuQ,GAAoD,IAApC5B,GAAkBz6B,QAAQvE,GAChD,MAAO,GAAGk/B,KAAOl/B,KAAQu8B,aAA2BmE,IAAgBE,EAAgB,KAAO,IAAID,MAAe3gC,MAAO,GACpH,IA+CqB6gC,CAAqB7gC,EAAM2/B,EAAMR,IAEzD,EA8DI2B,GAlCoB5/B,IAChB,MAAA6/B,QACJA,EAAAC,eACAA,EAAA7B,OACAA,GAAS,EAAA8B,eACTA,EAAAC,aACAA,EAAAC,UACAA,EAAA1X,MACAA,EAAQ,GAAA6W,gBACRA,EAAAc,kBACAA,GACElgC,EACJ,IAAImgC,SAAEA,EAAAC,SAAUA,EAAUC,cAAergC,EACrCsgC,EAAkB,CACpBrB,YAAa,OAEb9+B,SAAU,IAAM,IAKX,OAHH+/B,KACCI,kBAAiBH,WAAUC,WAAUC,cA7CnB,GAAGD,WAAUD,WAAUE,aAAYpC,aAC1D,MAAMsC,EAAOlD,GAAY+C,EAAU3F,IAC7B+F,EAAOnD,GAAY8C,EAAU1F,IAC7BgG,EAASpD,GAAYgD,EAAY5F,IAgBhC,OACL6F,gBAhBsB,CACtBrB,YAAa,IAAM,IACdT,GAA6B,OAAmB+B,EAAK5C,aACrDa,GAA6B,OAAmBgC,EAAK7C,aACrDa,GAA6B,SAAuBiC,EAAO9C,WAEhEx9B,SAAU,OAEL6+B,GAAiB,OAAmBuB,EAAK5C,SAAUM,MAAWe,GAC/D,OACAwB,EAAK7C,SACLM,MACGe,GAAiB,SAAuByB,EAAO9C,SAAUM,MAKhEmC,SAAUG,EAAKzY,QACfqY,SAAUK,EAAK1Y,QACfuY,WAAYI,EAAO3Y,QACpB,EAqBwD4Y,CAAmB1gC,IAErE,CACL29B,SAAU2C,EACV9qB,KAAMwpB,GAAiB,OAAmBa,EAAS5B,GACnD6B,eAAgBd,GAAiB,iBAA6Bc,EAAgB7B,GAC9E8B,eAAgBf,GAAiB,iBAA6Be,EAAgB9B,GAC9EuC,KAAMxB,GAAiB,OAAmBmB,EAAUlC,GACpDsC,KAAMvB,GAAiB,OAAmBoB,EAAUnC,GACpD0C,SAAU3B,GAAiB,WAA2BgB,EAAc/B,GACpEwC,OAAQzB,GAAiB,SAAuBqB,EAAYpC,GAC5Dz0B,MAAOw1B,GAAiB,QAAqBiB,EAAWhC,GACxD1V,MAAOyW,GAAiB,QAAqB,CAAEzW,QAAO6W,mBAAmBnB,GAC1E,EAKC2C,GAAY,GACZC,KAAkC,oBAAXljC,SAA0BA,OAAOiJ,WAAYjJ,OAAOiJ,SAASikB,eACpFiW,GAAa,MAmBf,WAAA3U,CAAYpqB,EAASg/B,GAlBrBC,EAAA5U,KAAA,YAAY,IACA4U,EAAA5U,KAAA,YAAAyU,IACZG,EAAA5U,KAAA,WACQ4U,EAAA5U,KAAA,SACN6U,UAAYC,IACV9U,KAAKrqB,QAAQo/B,OAASD,CAAA,EAExBE,gBAAiB,CACfhpB,IAAK,IAAMgU,KAAK2U,UAAYH,GAAYxU,KAAKwU,UAC7ChX,IAAMyX,KACHjV,KAAK2U,UAAYH,GAAYxU,KAAKwU,WAAWnuB,KAAK4uB,EAAQ,EAE7D1X,OAAS0X,IACP,MAAMx4B,GAASujB,KAAK2U,UAAYH,GAAYxU,KAAKwU,WAAWv9B,QAAQg+B,IACnEjV,KAAK2U,UAAYH,GAAYxU,KAAKwU,WAAWvY,OAAOxf,EAAO,EAAC,KAKjEujB,KAAKrqB,QAAUA,EACfqqB,KAAK2U,UAAYA,IAAa,EACzBA,IACHh/B,EAAQo/B,OAASvB,GAAe,CAC9BC,QAAS,GACTC,eAAgB,CAAE,EAElBC,eAAgB,CAAE,EAClBI,SAAU,GACVC,SAAU,GACVJ,aAAc,GACdK,WAAY,GACZJ,UAAW,GACX1X,MAAO,GACP6W,gBAAiB,KAGzB,GAKIkC,GAAUC,EAAOC,cADF,CAAE,GAEjBC,IAAiB/6B,EAA8Bg7B,0BAGjD,WAAAvV,CAAYnsB,GACV2hC,MAAM3hC,GAFRghC,EAAA5U,KAAA,cAGOA,KAAAwV,WAAa,IAAId,GAAW1U,KAAKpsB,MAAM+B,SAAW,GAAI2E,EAAgBq6B,UAC/E,CACE,MAAAc,GACE,OAA8BC,EAAAjX,cAAcyW,GAAQ9+B,SAAU,CAAE4S,MAAOgX,KAAKwV,WAAWxsB,OAASgX,KAAKpsB,MAAMoB,SAC/G,GARE4/B,EADmBt6B,EACZ,YAAYm6B,IADAn6B,GAiBjBq7B,GAAa,CAACjjC,EAAM2/B,KAChB,MAAAuD,EAAcp7B,SAASioB,MAAQjoB,SAASq7B,cAAc,QACtDC,EAAWF,EAAYh/B,iBAAiB,GAAGlE,KAAQu8B,OACnD8G,EAAU,GAAG9iC,MAAM8uB,KAAK+T,GACxBE,EAAU,GACZ,IAAAC,EAkCG,OAjCH5D,GAAQA,EAAKn4B,QACVm4B,EAAAh/B,SAAS28B,IACN,MAAAkG,EAAa17B,SAASikB,cAAc/rB,GAC1C,UAAW6/B,KAAavC,EACtB,GAAIpmB,OAAO8iB,UAAUC,eAAe5K,KAAKiO,EAAKuC,GAC5C,GAAkB,cAAdA,EACF2D,EAAWzD,UAAYzC,EAAIyC,eACvC,GAAmC,YAAdF,EACL2D,EAAWpT,WACFoT,EAAApT,WAAWC,QAAUiN,EAAIjN,QAEpCmT,EAAWrT,YAAYroB,SAASwoB,eAAegN,EAAIjN,cAEhD,CACL,MAAMkP,EAAOM,EACPvpB,OAA6B,IAAdgnB,EAAIiC,GAAwB,GAAKjC,EAAIiC,GAC/CiE,EAAA52B,aAAaizB,EAAWvpB,EAC/C,CAGiBktB,EAAA52B,aAAa2vB,GAAkB,QACtC8G,EAAQvtB,MAAK,CAAC2tB,EAAa15B,KACbw5B,EAAAx5B,EACTy5B,EAAWE,YAAYD,MAEtBJ,EAAA9Z,OAAOga,EAAe,GAE9BD,EAAQ3vB,KAAK6vB,EACrB,IAGUH,EAAA1iC,SAAS28B,UAAQ,cAAA11B,EAAA01B,EAAIqG,iBAAJ,EAAA/7B,EAAgBg8B,YAAYtG,EAAA,IACrDgG,EAAQ3iC,SAAS28B,GAAQ4F,EAAY/S,YAAYmN,KAC1C,CACL+F,UACAC,UACD,EAECO,GAAmB,CAACvuB,EAASgqB,KAC/B,MAAMwE,EAAah8B,SAASkoB,qBAAqB1a,GAAS,GAC1D,IAAKwuB,EACH,OAEI,MAAAC,EAAwBD,EAAW3M,aAAaoF,IAChDyH,EAAmBD,EAAwBA,EAAsBrrB,MAAM,KAAO,GAC9EurB,EAAqB,IAAID,GACzBE,EAAgBhtB,OAAOC,KAAKmoB,GAClC,UAAWO,KAAaqE,EAAe,CAC/B,MAAA5tB,EAAQgpB,EAAWO,IAAc,GACnCiE,EAAW3M,aAAa0I,KAAevpB,GAC9BwtB,EAAAl3B,aAAaizB,EAAWvpB,IAEW,IAA5C0tB,EAAiBz/B,QAAQs7B,IAC3BmE,EAAiBrwB,KAAKksB,GAElB,MAAAsE,EAAcF,EAAmB1/B,QAAQs7B,IACvB,IAApBsE,GACiBF,EAAA1a,OAAO4a,EAAa,EAE7C,CACE,QAAS1lB,EAAIwlB,EAAmBz8B,OAAS,EAAGiX,GAAK,EAAGA,GAAK,EAC5CqlB,EAAAM,gBAAgBH,EAAmBxlB,IAE5CulB,EAAiBx8B,SAAWy8B,EAAmBz8B,OACjDs8B,EAAWM,gBAAgB7H,IAClBuH,EAAW3M,aAAaoF,MAAsB2H,EAAch9B,KAAK,MAC1E48B,EAAWl3B,aAAa2vB,GAAkB2H,EAAch9B,KAAK,KACjE,EAQIm9B,GAAmB,CAACC,EAAUC,KAC1B,MAAAxD,QACJA,EAAAC,eACAA,EAAAC,eACAA,EAAAI,SACAA,EAAAC,SACAA,EAAAJ,aACAA,EAAAsD,oBACAA,EAAAjD,WACAA,EAAAJ,UACAA,EAAA1X,MACAA,EAAA6W,gBACAA,GACEgE,EACJT,GAAiB,OAAmB7C,GACpC6C,GAAiB,OAAmB5C,GArBpB,EAACxX,EAAO6V,UACH,IAAV7V,GAAyB3hB,SAAS2hB,QAAUA,IAC5C3hB,SAAA2hB,MAAQ4U,GAAa5U,IAEhCoa,GAAiB,QAAqBvE,EAAU,EAkBhDmF,CAAYhb,EAAO6W,GACnB,MAAMoE,EAAa,CACjB3D,QAASkC,GAAW,OAAmBlC,GACvCM,SAAU4B,GAAW,OAAmB5B,GACxCC,SAAU2B,GAAW,OAAmB3B,GACxCJ,aAAc+B,GAAW,WAA2B/B,GACpDK,WAAY0B,GAAW,SAAuB1B,GAC9CJ,UAAW8B,GAAW,QAAqB9B,IAEvCwD,EAAY,CAAE,EACdC,EAAc,CAAE,EACtB1tB,OAAOC,KAAKutB,GAAY/jC,SAASs8B,IAC/B,MAAMqG,QAAEA,EAAAD,QAASA,GAAYqB,EAAWzH,GACpCqG,EAAQ97B,SACVm9B,EAAU1H,GAAWqG,GAEnBD,EAAQ77B,SACVo9B,EAAY3H,GAAWyH,EAAWzH,GAASoG,QACjD,IAEMkB,GACEA,IAEcC,EAAAF,EAAUK,EAAWC,EAAW,EAElDC,GAAkB,KAgBlBC,GAf6BR,IAC3BO,IACF3yB,qBAAqB2yB,IAEnBP,EAASS,MACXF,GAAkB5yB,uBAAsB,KACtCoyB,GAAiBC,GAAU,KACPO,GAAA,OACnB,KAGHR,GAAiBC,GACCO,GAAA,KACtB,EAKIG,GAAmB,cAAcC,YAAd,WAAA5X,GAAAwV,SAAA7lB,WACVklB,EAAA5U,KAAA,eACX,qBAAA4X,CAAsBC,GACpB,OAAQC,GAAaD,EAAW7X,KAAKpsB,MACzC,CACE,kBAAAmkC,GACE/X,KAAKgY,YACT,CACE,oBAAAC,GACE,MAAMjD,gBAAEA,GAAoBhV,KAAKpsB,MAAM+B,QACvCq/B,EAAgBzX,OAAOyC,MACvBA,KAAKgY,YACT,CACE,UAAAA,GACE,MAAMhD,gBAAEA,EAAAH,UAAiBA,GAAc7U,KAAKpsB,MAAM+B,QAClD,IAAIm/B,EAAc,KAClB,MAAMjiC,GAlbgBu8B,EAmbpB4F,EAAgBhpB,MAAM9Y,KAAK+hC,IACzB,MAAMrhC,EAAQ,IAAKqhC,EAASrhC,OAErB,cADAA,EAAM+B,QACN/B,CAAA,IAtb0B,CACvC6/B,QAAS5D,GAAwB,CAAC,QAAoBT,GACtDsE,eAAgBhE,GAA2B,iBAA6BN,GACxEqI,MAAOtI,GAAqBC,EAAWF,IACvC2C,OAAQ1C,GAAqBC,EAAWF,IACxCyE,eAAgBjE,GAA2B,iBAA6BN,GACxE2E,SAAU5D,GACR,OACA,CAAC,MAAiB,QAClBf,GAEF4E,SAAU7D,GACR,OACA,CACE,OACA,UACA,aACA,WACA,YAEFf,GAEFwE,aAAczD,GAAqB,WAA2B,CAAC,aAA+Bf,GAC9F8H,oBAAqBzH,GAAuBL,GAC5C6E,WAAY9D,GACV,SACA,CAAC,MAAiB,aAClBf,GAEFyE,UAAW1D,GAAqB,QAAqB,CAAC,WAA2Bf,GACjFjT,MAAOkT,GAAsBD,GAC7B4D,gBAAiBtD,GAA2B,kBAA+BN,GAC3E0E,kBAAmBjD,GAAwBzB,EAAWF,MAhC/B,IAACE,EAyblBiG,GAAeV,UACjB6C,GAAe3kC,GACN2gC,KACTsB,EAActB,GAAe3gC,IAE/BgiC,EAAUC,EACd,CAIE,IAAAoD,GACE,GAAIlY,KAAKmY,SACP,OAEFnY,KAAKmY,UAAW,EAChB,MAAMnD,gBAAEA,GAAoBhV,KAAKpsB,MAAM+B,QACvCq/B,EAAgBxX,IAAIwC,MACpBA,KAAKgY,YACT,CACE,MAAAvC,GAES,OADPzV,KAAKkY,OACE,IACX,GAIIE,IAASt7B,EAAcu7B,0BAMzB,qBAAAT,CAAsBC,GACb,OAACS,GAAY9G,GAAQxR,KAAKpsB,MAAO,cAAe49B,GAAQqG,EAAW,cAC9E,CACE,wBAAAU,CAAyBC,EAAOC,GAC9B,IAAKA,EACI,YAET,OAAQD,EAAM9lC,MACZ,IAAK,SACL,IAAK,WACI,OACL+/B,UAAWgG,GAEf,IAAK,QACI,OACL1V,QAAS0V,GAEb,QACE,MAAM,IAAIrN,MACR,IAAIoN,EAAM9lC,0GAGpB,CACE,wBAAAgmC,CAAyBF,EAAOG,EAAmBC,EAAeH,GACzD,UACFE,EACH,CAACH,EAAM9lC,MAAO,IACTimC,EAAkBH,EAAM9lC,OAAS,GACpC,IACKkmC,KACA5Y,KAAKuY,yBAAyBC,EAAOC,KAIlD,CACE,qBAAAI,CAAsBL,EAAOM,EAAUF,EAAeH,GACpD,OAAQD,EAAM9lC,MACZ,IAAK,QACI,UACFomC,EACH,CAACN,EAAM9lC,MAAO+lC,EACdzF,gBAAiB,IAAK4F,IAE1B,IAAK,OACI,UACFE,EACHpF,eAAgB,IAAKkF,IAEzB,IAAK,OACI,UACFE,EACHnF,eAAgB,IAAKiF,IAEzB,QACS,UACFE,EACH,CAACN,EAAM9lC,MAAO,IAAKkmC,IAG7B,CACE,2BAAAG,CAA4BJ,EAAmBG,GACzC,IAAAE,EAAoB,IAAKF,GAOtB,OANPlvB,OAAOC,KAAK8uB,GAAmBtlC,SAAS4lC,IAClBD,EAAA,IACfA,EACHC,CAACA,GAAiBN,EAAkBM,GACrC,IAEID,CACX,CACE,qBAAAE,CAAsBV,EAAOC,GAWpB,OAVPzL,GACEwB,GAAgBhmB,MAAMpU,GAASokC,EAAM9lC,OAAS0B,IACxB,mBAAfokC,EAAM9lC,KAAsB,oIAAsI,uBAAuB87B,GAAgB50B,KAC9M,yDACmD4+B,EAAM9lC,0DAE7Ds6B,IACGyL,GAA4C,iBAAnBA,GAA+B/hC,MAAMkU,QAAQ6tB,KAAoBA,EAAejwB,MAAM2wB,GAAuC,iBAAhBA,IACvI,0CAA0CX,EAAM9lC,6DAA6D8lC,EAAM9lC,gBAAgB8lC,EAAM9lC,mDAEpI,CACX,CACE,kBAAA0mC,CAAmBpkC,EAAU8jC,GAC3B,IAAIH,EAAoB,CAAE,EAqCnB,OApCPU,EAAOC,SAASjmC,QAAQ2B,GAAWwjC,IACjC,IAAKA,IAAUA,EAAM5kC,MACnB,OAEF,MAAQoB,SAAUyjC,KAAmBc,GAAef,EAAM5kC,MACpDglC,EAAgBhvB,OAAOC,KAAK0vB,GAAYnvB,QAAO,CAACqnB,EAAKr3B,KACzDq3B,EAAI1C,GAAa30B,IAAQA,GAAOm/B,EAAWn/B,GACpCq3B,IACN,IACC,IAAA/+B,KAAEA,GAAS8lC,EAMf,OALoB,iBAAT9lC,EACTA,EAAOA,EAAKqB,WAEPisB,KAAAkZ,sBAAsBV,EAAOC,GAE5B/lC,GACN,IAAK,yBACQomC,EAAA9Y,KAAKoZ,mBAAmBX,EAAgBK,GACnD,MACF,IAAK,OACL,IAAK,OACL,IAAK,WACL,IAAK,SACL,IAAK,QACHH,EAAoB3Y,KAAK0Y,yBACvBF,EACAG,EACAC,EACAH,GAEF,MACF,QACEK,EAAW9Y,KAAK6Y,sBAAsBL,EAAOM,EAAUF,EAAeH,GAEhF,IAEWzY,KAAK+Y,4BAA4BJ,EAAmBG,EAC/D,CACE,MAAArD,GACE,MAAMzgC,SAAEA,KAAapB,GAAUosB,KAAKpsB,MAChC,IAAAklC,EAAW,IAAKllC,IAChB4hC,WAAEA,GAAe5hC,EAIjB,GAHAoB,IACS8jC,EAAA9Y,KAAKoZ,mBAAmBpkC,EAAU8jC,IAE3CtD,KAAgBA,aAAsBd,IAAa,CAErDc,EAAa,IAAId,GADJc,EACoB7/B,SAAS,UACnCmjC,EAAStD,UACtB,CACW,OAAAA,EAAoCE,EAAAjX,cAAciZ,GAAkB,IAAKoB,EAAUnjC,QAAS6/B,EAAWxsB,QAA2BqwB,EAAO5a,cAAcyW,GAAQsE,SAAU,MAAO7jC,GAAmC+/B,EAAAjX,cAAciZ,GAAkB,IAAKoB,EAAUnjC,aAC7Q,GA7IEi/B,EADW93B,EACJ,eAAe,CACpB26B,OAAO,EACP9F,yBAAyB,EACzBmC,mBAAmB,IAJVh3B,GCvoBb,MAAM28B,GAA2B,UAE5Bv0B,OAAIuE,UAAU,yCACbzU,gBAACkQ,OAAIuE,UAAU,2BACZvE,OAAIuE,UAAU,kHACduN,QAAKvN,UAAU,UAAUzU,SAAA,uBCG5B0kC,GAAY,CAChB,QAAS,CACPC,YCQW,CAAAC,WCpBA,CACbC,QAAS,SACTC,SAAU,WACVC,QAAS,UACTC,QAAS,UACTC,KAAM,SACNC,YAAa,wBACbC,aAAc,0BACdC,YAAa,yBACbC,YAAa,yBACbC,KAAM,CACJ/mC,KAAM,0BACN61B,MAAO,2BACPmR,OAAQ,8BAEVC,SAAU,CACRre,MAAO,gBACP5oB,KAAM,sBACN61B,MAAO,uBACPhN,YAAa,8BACbxP,MAAO,CACL2tB,OAAQ,gBACRE,UAAW,qBACXC,SAAU,uBAEZC,SAAU,CACRzf,OAAQ,qBAEV0f,MAAO,CACLL,OAAQ,eACRM,QAAS,cACT5yB,SAAU,kBAEZ6yB,cAAe,CACbR,KAAM,yBACNle,YAAa,8BAEf2e,SAAU,CACRxnC,KAAM,iBACN6oB,YAAa,6CDlBjBwd,QErBa,CACbzd,MAAO,oEACP6e,IAAK,qRACLC,gBAAiB,mBACjBC,SAAU,kBACVC,WAAY,cACZC,SAAU,WACVhnC,KAAM,2BACNinC,KAAM,uCACNC,KAAM,CACJC,SAAU,cACVC,MAAO,CACLC,WAAY,cACZC,gBAAiB,mBACjBC,iBAAkB,oBAClBC,oBAAqB,0BFOzB/B,SGtBa,CACb1d,MAAO,WACPC,YAAa,sHACbyf,SAAU,cACVC,UAAW,aACXC,SAAU,UACVC,UAAW,WACXC,SAAU,aACVC,SAAU,WACVC,QAAS,eACTC,QAAS,mBACTC,aAAc,oBACdC,OAAQ,CACNC,UAAW,cACXC,wBAAyB,4BACzBC,YAAa,oBACbC,WAAY,cACZC,eAAgB,kBAChBC,sBAAuB,yBACvBC,gBAAiB,mBACjBC,IAAK,MACLC,kBAAmB,uBACnBC,aAAc,gBACdC,cAAe,cACfC,WAAY,eAEdC,OAAQ,CACNhhB,MAAO,UACPihB,SAAU,0BACVvB,SAAU,yJACVC,UAAW,qHACXC,SAAU,wGACVC,UAAW,8GACXC,SAAU,CACR,qGACA,wGACA,2GAEFC,SAAU,yJAEZmB,UAAW,CACTlhB,MAAO,uBACPihB,SAAU,2BACVvB,SAAU,2HACVC,UAAW,0NACXC,SAAU,wLACVC,UAAW,6MACXC,SAAU,CACR,wHACA,oHACA,4GAEFC,SAAU,6IAEZoB,UAAW,CACTnhB,MAAO,YACPihB,SAAU,uBACVvB,SAAU,sIACVC,UAAW,mNACXC,SAAU,4OACVC,UAAW,+KACXC,SAAU,CACR,2HACA,gHACA,4HAEFC,SAAU,uLAEZqB,gBAAiB,CACfphB,MAAO,uBACPihB,SAAU,mCACVvB,SAAU,4HACVC,UAAW,yKACXC,SAAU,mNACVC,UAAW,gKACXC,SAAU,CACR,4HACA,+FACA,4GAEFC,SAAU,0HHzDZpC,QIvBa,CACb3d,MAAO,iCACPC,YAAa,0OACb2f,SAAU,UACVprB,OAAQ,YACR6sB,KAAM,OACNC,QAAS,4BACTC,SAAU,WACVtR,KAAM,UACNhV,MAAO,CACL,CACEumB,UAAW,mEACX5B,SAAU,oFACVprB,OAAQ,gEACR6sB,KAAM,0FAER,CACEG,UAAW,6EACX5B,SAAU,8EACVprB,OAAQ,+DACR6sB,KAAM,iGAER,CACEG,UAAW,2EACX5B,SAAU,gFACVprB,OAAQ,uEACR6sB,KAAM,2FAER,CACEG,UAAW,6DACX5B,SAAU,+EACVprB,OAAQ,0DACR6sB,KAAM,kGAER,CACEG,UAAW,mEACX5B,SAAU,+FACVprB,OAAQ,+EACR6sB,KAAM,uFAER,CACEG,UAAW,6DACX5B,SAAU,uFACVprB,OAAQ,2DACR6sB,KAAM,yFAER,CACEG,UAAW,gFACX5B,SAAU,qFACVprB,OAAQ,iGACR6sB,KAAM,8EAER,CACEG,UAAW,mEACX5B,SAAU,+FACVprB,OAAQ,gEACR6sB,KAAM,wFAER,CACEG,UAAW,mEACX5B,SAAU,mFACVprB,OAAQ,yDACR6sB,KAAM,8FAER,CACEG,UAAW,qEACX5B,SAAU,wFACVprB,OAAQ,qFACR6sB,KAAM,0FAER,CACEG,UAAW,0EACX5B,SAAU,gGACVprB,OAAQ,kGACR6sB,KAAM,gGAER,CACEG,UAAW,yDACX5B,SAAU,gGACVprB,OAAQ,qFACR6sB,KAAM,4FJxDVzD,QKxBa,CACb5d,MAAO,mBACPC,YAAa,sKACbwhB,KAAM,CACJxpC,KAAM,OACNypC,gBAAiB,2BACjBC,MAAO,SACPC,iBAAkB,2BAClBC,QAAS,UACTC,mBAAoB,0CACpB1d,QAAS,WACT2d,mBAAoB,mEACpBC,KAAM,kBACNC,QAAS,cACTvd,QAAS,uDACTroB,MAAO,+FACP6lC,aAAc,qBACdC,cAAe,uBACfC,aAAc,kBACdC,gBAAiB,wBACjBC,gBAAiB,yBACjBC,iBAAkB,8CAEpB5d,KAAM,CACJgd,MAAO,qBACPa,SAAU,oBACVC,aAAc,gEAEhBC,OAAQ,CACNzD,SAAU,WACV0D,SAAU,WACVhB,MAAO,WLNT9D,cMzBa,CACb7d,MAAO,iBACP4iB,SAAU,2CACV3iB,YAAa,oEACb4iB,aAAc,sDACd5V,MAAO,SACP71B,KAAM,QACN0rC,UAAW,yBACXC,YAAa,8CACb3T,SAAU,CACR7zB,MAAO,mBACPynC,SAAU,WACVC,SAAU,WACVC,MAAO,UACPC,cAAe,4BACfC,cAAe,4BACfC,WAAY,4BAEdtrB,SAAU,CACRxc,MAAO,iBACP+nC,OAAQ,wBACRC,QAAS,2BACT7E,QAAS,yBACT5yB,SAAU,6BAEZ03B,YAAa,CACXjoC,MAAO,eACP+nC,OAAQ,sBACRC,QAAS,yBACT7E,QAAS,uBACT5yB,SAAU,2BAEZ23B,aAAc,CACZloC,MAAO,iBACP+nC,OAAQ,wBACRC,QAAS,2BACT7E,QAAS,yBACT5yB,SAAU,6BAEZo3B,MAAO,CACL3nC,MAAO,wBACP5E,OAAQ,4CACR+tB,QAAS,uCAEXgf,SAAU,CACRtU,SAAU,mBACVuU,iBAAkB,iBAClBC,iBAAkB,iBAClBC,cAAe,gBACf9rB,SAAU,iBACV+rB,eAAgB,mBAChBC,gBAAiB,sBACjBP,YAAa,eACbQ,kBAAmB,iBACnBC,mBAAoB,qBAEtBC,UAAW,CACTC,cAAe,gCACfC,iBAAkB,wBAEpB7e,OAAQ,CACNmZ,QAAS,UACT5yB,SAAU,aACVu4B,cAAe,kBACfC,cAAe,kBACfC,UAAW,iBACXC,gBAAiB,yBACjBC,iBAAkB,4BAClBC,eAAgB,uBAChBC,gBAAiB,4BN3CnBhG,MO1Ba,CACbD,QAAS,cACT5yB,SAAU,iBACV84B,YAAa,2BACbC,aAAc,8BACdvB,OAAQ,aACRC,QAAS,gBACTnF,OAAQ,eACR0G,QAAS,oBACTC,OAAQ,SACRC,cAAe,yBPiBfvG,SQ3Ba,CACbhuB,MAAO,CACLw0B,MAAO,2BACPC,KAAM,4BACNC,OAAQ,+BAEV3G,SAAU,CACR4G,OAAQ,gBACR3sC,QAAS,eACT4sC,UAAW,uBAEb5H,WAAY,CACVK,KAAM,iBACNJ,QAAS,iBACTC,SAAU,eACVC,QAAS,0BACTC,QAAS,qBAEX6E,OAAQ,CACNzD,SAAU,qBACV0C,MAAO,gBACPgB,SAAU,yBAEZlE,MAAO,CACL6E,OAAQ,yBACRC,QAAS,6BAEX+B,QAAS,CACPvX,OAAQ,oBACRwX,SAAU,oBACVC,SAAU,iBACVC,MAAO,eACPC,KAAM,cACNC,MAAO,kBACPC,UAAW,mBRNbC,OS5Ba,CACbnhB,QAAS,CACP1E,MAAO,WACP8lB,YAAa,gCACbC,cAAe,uBACfC,WAAY,0CACZC,aAAc,gBACdC,gBAAiB,mBAEnB7pC,MAAO,CACL2jB,MAAO,OACPmmB,eAAgB,0BAChBC,aAAc,kBACdC,aAAc,kBACdC,SAAU,mBAEZ3hB,KAAM,CACJ3E,MAAO,aACP6E,QAAS,gBACT0hB,WAAY,iBACZC,OAAQ,eAEV5hB,QAAS,CACP5E,MAAO,QACPymB,eAAgB,iCAChBC,cAAe,sCTIjB9vC,IU7Ba,CACbopB,MAAO,mDACPC,YAAa,sJACb0mB,SAAU,wGACVC,OAAQ,2BACRC,MAAO,CACL/I,KAAM,CACJ9d,MAAO,mDACPC,YAAa,4JAEf0d,SAAU,CACR3d,MAAO,0CACPC,YAAa,sJAEf2d,QAAS,CACP5d,MAAO,qDACPC,YAAa,gKAEf4d,QAAS,CACP7d,MAAO,yCACPC,YAAa,wIVUjB0gB,OW9Ba,CACbmG,OAAQ,CACN7uC,KAAM,2BACN8uC,SAAU,sBACV9mB,YAAa,oFACbuiB,SAAU,oBACVb,MAAO,qBACPqF,OAAQ,CAAC,YAAa,iBAAkB,uBAAwB,sBAAuB,eAAgB,0BAEzGC,aAAc,CACZhvC,KAAM,2BACNgoB,YAAa,8CACbuiB,SAAU,sBXmBZ0E,KY/Ba,CACbxJ,QAAS,CACPyJ,MAAO,mCACPC,SAAU,wCACVC,SAAU,6BAEZ1J,SAAU,CACRqD,OAAQ,gFACRE,UAAW,6FACXC,UAAW,oEACXC,gBAAiB,iGAEnB/X,MAAO,CACL8U,KAAM,gBACNlR,MAAO,kBACPc,OAAQ,oBACRwX,SAAU,oBACV+B,SAAU,wBACV9B,SAAU,oBACV7D,MAAO,kBACP4F,MAAO,oBACPtI,SAAU,oBACV0D,SAAU,oBACV6E,OAAQ,kBACRC,IAAK,eACLC,KAAM,eACNC,MAAO,iBACP5C,OAAQ,kBACRrgB,QAAS,mBACTroB,MAAO,gBACPuoB,QAAS,iBACTD,KAAM,uBAERijB,WAAY,CACVC,SAAU,uBACVC,QAAS,oBACTC,QAAS,iBACTC,WAAY,+BZLdC,SahCa,CACbnD,QAAS,8BACTrsC,QAAS,eACT4sC,UAAW,sBACX6C,WAAY,YACZC,QAAS,UACTC,QAAS,UACTrpB,OAAQ,qBb0BRyf,McjCa,CACbJ,OAAQ,gBACR0G,QAAS,4BACTG,MAAO,qBACPC,KAAM,sBACNC,OAAQ,iCd6BR10B,OelCa,CACb43B,UAAW,uDACXroB,MAAO,uBfiCPsoB,SgBnCa,CACbtoB,MAAO,WACP4iB,SAAU,sBACV3iB,YAAa,0CACbsoB,aAAc,sDACdtb,MAAO,SACPub,KAAM,SACNxG,KAAM,kBACNC,QAAS,cACTwG,aAAc,mCACdC,cAAe,0BAGfC,QAAS,oBACTC,KAAM,qBACNC,OAAQ,aAGRC,aAAc,oBACdC,UAAW,qBACXC,YAAa,aACbC,aAAc,kBAGdC,mBAAoB,qDACpBC,gBAAiB,gDACjBC,kBAAmB,8BACnBC,mBAAoB,mCAGpBC,mBAAoB,4CACpBC,gBAAiB,uCACjBC,kBAAmB,iCACnBC,mBAAoB,8BAGpBC,WAAY,CACVpH,gBAAiB,yBACjBC,iBAAkB,sBAClBH,aAAc,mBAIhBX,KAAM,CACJlrC,KAAM,mBACN6tB,QAAS,eACTud,MAAO,wBACPK,KAAM,kBACNC,QAAS,cACTvd,QAAS,yEACTroB,MAAO,+FACPimC,gBAAiB,4BAInB/c,OAAQ,CACNb,QAAS,8BACTroB,MAAO,4CACP4lC,QAAS,wBAIX0H,MAAO,CACLC,IAAK,eACLC,WAAY,WACZC,WAAY,SACZC,MAAO,UhB9BTnL,QiBpCa,CACb5e,MAAO,wBACPC,YAAa,oGACb+pB,UAAW,aACXC,UAAW,gBACXC,UAAW,iBACXC,kBAAmB,yBACnBC,gBAAiB,sBACjBC,SAAU,cACVC,YAAa,CACXtqB,MAAO,2BAET2pB,MAAO,CACLY,UAAW,CACTvqB,MAAO,sBACPC,YAAa,0FAEfuqB,UAAW,CACTxqB,MAAO,qBACPC,YAAa,wGACbwqB,UAAW,cAEbC,UAAW,CACT1qB,MAAO,uBACPC,YAAa,+FjBcjB0qB,OkBtCa,CACb1d,MAAO,SACP71B,KAAM,QACNwzC,KAAM,SACNtd,OAAQ,WACRud,QAAS,YACTC,IAAK,MACLC,GAAI,MACJlmB,QAAS,gBACTxoB,MAAO,OACPqoB,QAAS,UACTE,QAAS,QACTD,KAAM,gBnBEN,QAAS,CACP6Y,YoBKW,CAAAC,WCpBA,CACbC,QAAS,UACTC,SAAU,WACVC,QAAS,UACTC,QAAS,UACTC,KAAM,OACNC,YAAa,wBACbC,aAAc,yBACdC,YAAa,wBACbC,YAAa,wBACbC,KAAM,CACJ/mC,KAAM,uBACN61B,MAAO,wBACPmR,OAAQ,0BAEVC,SAAU,CACRre,MAAO,WACP5oB,KAAM,gBACN61B,MAAO,iBACPhN,YAAa,4BACbxP,MAAO,CACL2tB,OAAQ,eACRE,UAAW,qBACXC,SAAU,qBAEZC,SAAU,CACRzf,OAAQ,mBAEV0f,MAAO,CACLL,OAAQ,eACRM,QAAS,gBACT5yB,SAAU,kBAEZ6yB,cAAe,CACbR,KAAM,qBACNle,YAAa,0BAEf2e,SAAU,CACRxnC,KAAM,gBACN6oB,YAAa,2CDlBjBwd,QErBa,CACbzd,MAAO,iEACP6e,IAAK,+RACLC,gBAAiB,mBACjBC,SAAU,aACVC,WAAY,cACZC,SAAU,WACVhnC,KAAM,2BACNinC,KAAM,uCACNC,KAAM,CACJC,SAAU,aACVC,MAAO,CACLC,WAAY,cACZC,gBAAiB,mBACjBC,iBAAkB,oBAClBC,oBAAqB,0BFOzB/B,SGtBa,CACb1d,MAAO,WACPC,YAAa,4GACbyf,SAAU,WACVC,UAAW,YACXC,SAAU,WACVC,UAAW,YACXC,SAAU,WACVC,SAAU,WACVC,QAAS,cACTC,QAAS,eACTC,aAAc,gBACdC,OAAQ,CACNC,UAAW,YACXC,wBAAyB,2BACzBC,YAAa,eACbC,WAAY,cACZC,eAAgB,kBAChBC,sBAAuB,yBACvBC,gBAAiB,mBACjBC,IAAK,MACLC,kBAAmB,qBACnBC,aAAc,gBACdC,cAAe,gBACfC,WAAY,cAEdC,OAAQ,CACNhhB,MAAO,UACPihB,SAAU,2BACVvB,SAAU,uIACVC,UAAW,sGACXC,SAAU,6FACVC,UAAW,2FACXC,SAAU,CACR,iGACA,kGACA,sGAEFC,SAAU,4IAEZmB,UAAW,CACTlhB,MAAO,mBACPihB,SAAU,2BACVvB,SAAU,uHACVC,UAAW,wLACXC,SAAU,wKACVC,UAAW,qMACXC,SAAU,CACR,wGACA,+FACA,iGAEFC,SAAU,gIAEZoB,UAAW,CACTnhB,MAAO,YACPihB,SAAU,qBACVvB,SAAU,iIACVC,UAAW,gOACXC,SAAU,4NACVC,UAAW,4KACXC,SAAU,CACR,mHACA,gGACA,uHAEFC,SAAU,mLAEZqB,gBAAiB,CACfphB,MAAO,uBACPihB,SAAU,sCACVvB,SAAU,sHACVC,UAAW,uLACXC,SAAU,mMACVC,UAAW,mKACXC,SAAU,CACR,sGACA,yFACA,qGAEFC,SAAU,4HHzDZpC,QIvBa,CACb3d,MAAO,0BACPC,YAAa,qOACb2f,SAAU,WACVprB,OAAQ,SACR6sB,KAAM,OACNC,QAAS,yBACTC,SAAU,WACVtR,KAAM,OACNhV,MAAO,CACL,CACEumB,UAAW,6DACX5B,SAAU,2EACVprB,OAAQ,0DACR6sB,KAAM,sFAER,CACEG,UAAW,yEACX5B,SAAU,mFACVprB,OAAQ,qDACR6sB,KAAM,4FAER,CACEG,UAAW,2DACX5B,SAAU,6EACVprB,OAAQ,oEACR6sB,KAAM,yFAER,CACEG,UAAW,uDACX5B,SAAU,4EACVprB,OAAQ,4CACR6sB,KAAM,iFAER,CACEG,UAAW,8DACX5B,SAAU,oFACVprB,OAAQ,yEACR6sB,KAAM,yFAER,CACEG,UAAW,gDACX5B,SAAU,uFACVprB,OAAQ,kDACR6sB,KAAM,yFAER,CACEG,UAAW,kEACX5B,SAAU,sFACVprB,OAAQ,+EACR6sB,KAAM,+EAER,CACEG,UAAW,sDACX5B,SAAU,6FACVprB,OAAQ,8CACR6sB,KAAM,kFAER,CACEG,UAAW,2DACX5B,SAAU,qEACVprB,OAAQ,mDACR6sB,KAAM,4EAER,CACEG,UAAW,gEACX5B,SAAU,wFACVprB,OAAQ,oFACR6sB,KAAM,gFAER,CACEG,UAAW,6DACX5B,SAAU,0FACVprB,OAAQ,4FACR6sB,KAAM,yFAER,CACEG,UAAW,iDACX5B,SAAU,yFACVprB,OAAQ,wEACR6sB,KAAM,wFJxDVzD,QKxBa,CACb5d,MAAO,cACPC,YAAa,8JACbwhB,KAAM,CACJxpC,KAAM,OACNypC,gBAAiB,uBACjBC,MAAO,QACPC,iBAAkB,wBAClBC,QAAS,UACTC,mBAAoB,qCACpB1d,QAAS,UACT2d,mBAAoB,+DACpBC,KAAM,eACNC,QAAS,aACTvd,QAAS,0DACTroB,MAAO,wEACP6lC,aAAc,mBACdC,cAAe,oBACfC,aAAc,gBACdC,gBAAiB,sBACjBC,gBAAiB,sBACjBC,iBAAkB,0CAEpB5d,KAAM,CACJgd,MAAO,qBACPa,SAAU,oBACVC,aAAc,gEAEhBC,OAAQ,CACNzD,SAAU,WACV0D,SAAU,WACVhB,MAAO,ULNT9D,cMzBa,CACb7d,MAAO,gBACP4iB,SAAU,qCACV3iB,YAAa,8DACb4iB,aAAc,oDACd5V,MAAO,QACP71B,KAAM,OACN0rC,UAAW,qBACXC,YAAa,qCACb3T,SAAU,CACR7zB,MAAO,YACPynC,SAAU,WACVC,SAAU,WACVC,MAAO,QACPC,cAAe,qBACfC,cAAe,qBACfC,WAAY,mBAEdtrB,SAAU,CACRxc,MAAO,gBACP+nC,OAAQ,uBACRC,QAAS,wBACT7E,QAAS,wBACT5yB,SAAU,0BAEZ03B,YAAa,CACXjoC,MAAO,eACP+nC,OAAQ,sBACRC,QAAS,uBACT7E,QAAS,uBACT5yB,SAAU,yBAEZ23B,aAAc,CACZloC,MAAO,gBACP+nC,OAAQ,uBACRC,QAAS,wBACT7E,QAAS,wBACT5yB,SAAU,0BAEZo3B,MAAO,CACL3nC,MAAO,iBACP5E,OAAQ,+BACR+tB,QAAS,+BAEXgf,SAAU,CACRtU,SAAU,YACVuU,iBAAkB,gBAClBC,iBAAkB,gBAClBC,cAAe,aACf9rB,SAAU,gBACV+rB,eAAgB,kBAChBC,gBAAiB,mBACjBP,YAAa,eACbQ,kBAAmB,iBACnBC,mBAAoB,mBAEtBC,UAAW,CACTC,cAAe,uBACfC,iBAAkB,sBAEpB7e,OAAQ,CACNmZ,QAAS,UACT5yB,SAAU,WACVu4B,cAAe,iBACfC,cAAe,iBACfC,UAAW,aACXC,gBAAiB,wBACjBC,iBAAkB,yBAClBC,eAAgB,uBAChBC,gBAAiB,0BN3CnBhG,MO1Ba,CACbD,QAAS,gBACT5yB,SAAU,iBACV84B,YAAa,wBACbC,aAAc,yBACdvB,OAAQ,eACRC,QAAS,gBACTnF,OAAQ,eACR0G,QAAS,mBACTC,OAAQ,SACRC,cAAe,uBPiBfvG,SQ3Ba,CACbhuB,MAAO,CACLw0B,MAAO,uBACPC,KAAM,sBACNC,OAAQ,yBAEV3G,SAAU,CACR4G,OAAQ,kBACR3sC,QAAS,mBACT4sC,UAAW,uBAEb5H,WAAY,CACVK,KAAM,aACNJ,QAAS,gBACTC,SAAU,gBACVC,QAAS,yBACTC,QAAS,gBAEX6E,OAAQ,CACNzD,SAAU,mBACV0C,MAAO,aACPgB,SAAU,oBAEZlE,MAAO,CACL6E,OAAQ,uBACRC,QAAS,yBAEX+B,QAAS,CACPvX,OAAQ,iBACRwX,SAAU,mBACVC,SAAU,gBACVC,MAAO,QACPC,KAAM,YACNC,MAAO,aACPC,UAAW,gBRNbC,OS5Ba,CACbnhB,QAAS,CACP1E,MAAO,WACP8lB,YAAa,6BACbC,cAAe,iBACfC,WAAY,2BACZC,aAAc,gBACdC,gBAAiB,oBAEnB7pC,MAAO,CACL2jB,MAAO,QACPmmB,eAAgB,wBAChBC,aAAc,mBACdC,aAAc,uBACdC,SAAU,aAEZ3hB,KAAM,CACJ3E,MAAO,cACP6E,QAAS,aACT0hB,WAAY,gBACZC,OAAQ,aAEV5hB,QAAS,CACP5E,MAAO,UACPymB,eAAgB,2BAChBC,cAAe,uCTIjB9vC,IU7Ba,CACbopB,MAAO,mDACPC,YAAa,oIACb0mB,SAAU,8FACVC,OAAQ,2BACRC,MAAO,CACL/I,KAAM,CACJ9d,MAAO,mDACPC,YAAa,0IAEf0d,SAAU,CACR3d,MAAO,0CACPC,YAAa,wIAEf2d,QAAS,CACP5d,MAAO,mDACPC,YAAa,8JAEf4d,QAAS,CACP7d,MAAO,yCACPC,YAAa,oIVUjB0gB,OW9Ba,CACbmG,OAAQ,CACN7uC,KAAM,2BACN8uC,SAAU,sBACV9mB,YAAa,0EACbuiB,SAAU,oBACVb,MAAO,qBACPqF,OAAQ,CAAC,YAAa,iBAAkB,kBAAmB,gBAAiB,cAAe,sBAE7FC,aAAc,CACZhvC,KAAM,2BACNgoB,YAAa,2CACbuiB,SAAU,sBXmBZ0E,KY/Ba,CACbxJ,QAAS,CACPyJ,MAAO,+BACPC,SAAU,qCACVC,SAAU,2BAEZ1J,SAAU,CACRqD,OAAQ,yEACRE,UAAW,qFACXC,UAAW,6DACXC,gBAAiB,0FAEnB/X,MAAO,CACL8U,KAAM,YACNlR,MAAO,aACPc,OAAQ,cACRwX,SAAU,gBACV+B,SAAU,qBACV9B,SAAU,gBACV7D,MAAO,aACP4F,MAAO,aACPtI,SAAU,gBACV0D,SAAU,gBACV6E,OAAQ,cACRC,IAAK,WACLC,KAAM,YACNC,MAAO,aACP5C,OAAQ,cACRrgB,QAAS,eACTroB,MAAO,aACPuoB,QAAS,eACTD,KAAM,oBAERijB,WAAY,CACVC,SAAU,sBACVC,QAAS,qBACTC,QAAS,iBACTC,WAAY,gCZLdC,SahCa,CACbnD,QAAS,gCACTrsC,QAAS,mBACT4sC,UAAW,sBACX6C,WAAY,YACZC,QAAS,UACTC,QAAS,UACTrpB,OAAQ,mBb0BRyf,McjCa,CACbJ,OAAQ,eACR0G,QAAS,6BACTG,MAAO,qBACPC,KAAM,oBACNC,OAAQ,2Bd6BR10B,OelCa,CACb43B,UAAW,8CACXroB,MAAO,uBfiCPsoB,SgBnCa,CACbtoB,MAAO,WACP4iB,SAAU,uBACV3iB,YAAa,wCACbsoB,aAAc,iDACdtb,MAAO,QACPub,KAAM,OACNxG,KAAM,gBACNC,QAAS,aACTwG,aAAc,gCACdC,cAAe,iBAGfC,QAAS,iBACTC,KAAM,aACNC,OAAQ,cAGRC,aAAc,iBACdC,UAAW,aACXC,YAAa,cACbC,aAAc,gBAGdC,mBAAoB,2CACpBC,gBAAiB,4CACjBC,kBAAmB,yBACnBC,mBAAoB,8BAGpBC,mBAAoB,oCACpBC,gBAAiB,mCACjBC,kBAAmB,4BACnBC,mBAAoB,yBAGpBC,WAAY,CACVpH,gBAAiB,sBACjBC,iBAAkB,uBAClBH,aAAc,iBAIhBX,KAAM,CACJlrC,KAAM,gBACN6tB,QAAS,eACTud,MAAO,wBACPK,KAAM,gBACNC,QAAS,aACTvd,QAAS,uEACTroB,MAAO,yEACPimC,gBAAiB,uBAInB/c,OAAQ,CACNb,QAAS,+BACTroB,MAAO,4CACP4lC,QAAS,uBAIX0H,MAAO,CACLC,IAAK,aACLC,WAAY,aACZC,WAAY,aACZC,MAAO,UhB9BTnL,QiBpCa,CACb5e,MAAO,yBACPC,YAAa,2FACb+pB,UAAW,aACXC,UAAW,aACXC,UAAW,aACXC,kBAAmB,qBACnBC,gBAAiB,mBACjBC,SAAU,WACVC,YAAa,CACXtqB,MAAO,sBAET2pB,MAAO,CACLY,UAAW,CACTvqB,MAAO,oBACPC,YAAa,mFAEfuqB,UAAW,CACTxqB,MAAO,oBACPC,YAAa,gGACbwqB,UAAW,aAEbC,UAAW,CACT1qB,MAAO,oBACPC,YAAa,kFjBcjB0qB,OkBtCa,CACb1d,MAAO,QACP71B,KAAM,OACNwzC,KAAM,OACNtd,OAAQ,SACRud,QAAS,UACTC,IAAK,MACLC,GAAI,KACJlmB,QAAS,aACTxoB,MAAO,QACPqoB,QAAS,UACTE,QAAS,UACTD,KAAM,iBtCKN,QAAS,CACP6Y,YuCEW,CACbC,WCrBa,CACbC,QAAS,SACTC,SAAU,YACVC,QAAS,UACTC,QAAS,WACTC,KAAM,SACNC,YAAa,sBACbC,aAAc,yBACdC,YAAa,uBACbC,YAAa,wBACbC,KAAM,CACJ/mC,KAAM,2BACN61B,MAAO,4BACPmR,OAAQ,+BAEVC,SAAU,CACRre,MAAO,kBACP5oB,KAAM,wBACN61B,MAAO,yBACPhN,YAAa,6BACbxP,MAAO,CACL2tB,OAAQ,gBACRE,UAAW,sBACXC,SAAU,wBAEZC,SAAU,CACRzf,OAAQ,sBAEV0f,MAAO,CACLL,OAAQ,kBACRM,QAAS,kBACT5yB,SAAU,sBAEZ6yB,cAAe,CACbR,KAAM,wBACNle,YAAa,8BAEf2e,SAAU,CACRxnC,KAAM,iBACN6oB,YAAa,2CDjBjByd,QEtBa,CACb1d,MAAO,oEACP6e,IAAK,mSACLC,gBAAiB,qBACjBC,SAAU,cACVC,WAAY,eACZC,SAAU,WACVhnC,KAAM,2BACNinC,KAAM,uCACNC,KAAM,CACJC,SAAU,YACVC,MAAO,CACLC,WAAY,cACZC,gBAAiB,mBACjBC,iBAAkB,oBAClBC,oBAAqB,0BFQzB9B,SGvBa,CACb3d,MAAO,YACPC,YAAa,qHACbyf,SAAU,UACVC,UAAW,iBACXC,SAAU,WACVC,UAAW,YACXC,SAAU,aACVC,SAAU,WACVC,QAAS,eACTC,QAAS,mBACTC,aAAc,sBACdC,OAAQ,CACNC,UAAW,aACXC,wBAAyB,iCACzBC,YAAa,qBACbC,WAAY,cACZC,eAAgB,mBAChBC,sBAAuB,0BACvBC,gBAAiB,yBACjBC,IAAK,MACLC,kBAAmB,yBACnBC,aAAc,gBACdC,cAAe,eACfC,WAAY,cAEdC,OAAQ,CACNhhB,MAAO,UACPihB,SAAU,0BACVvB,SAAU,+JACVC,UAAW,oHACXC,SAAU,2GACVC,UAAW,6HACXC,SAAU,CACR,wGACA,2GACA,yHAEFC,SAAU,gKAEZmB,UAAW,CACTlhB,MAAO,4BACPihB,SAAU,gCACVvB,SAAU,uIACVC,UAAW,mOACXC,SAAU,oMACVC,UAAW,qNACXC,SAAU,CACR,8HACA,iHACA,gHAEFC,SAAU,mJAEZoB,UAAW,CACTnhB,MAAO,YACPihB,SAAU,uBACVvB,SAAU,sIACVC,UAAW,qOACXC,SAAU,wPACVC,UAAW,2LACXC,SAAU,CACR,gIACA,+GACA,+HAEFC,SAAU,2LAEZqB,gBAAiB,CACfphB,MAAO,uBACPihB,SAAU,mCACVvB,SAAU,qIACVC,UAAW,gMACXC,SAAU,wNACVC,UAAW,qLACXC,SAAU,CACR,mIACA,kGACA,6GAEFC,SAAU,yIHxDZnC,QIxBa,CACb5d,MAAO,+BACPC,YAAa,2OACb2f,SAAU,WACVprB,OAAQ,YACR6sB,KAAM,OACNC,QAAS,mCACTC,SAAU,WACVtR,KAAM,YACNhV,MAAO,CACL,CACEumB,UAAW,wEACX5B,SAAU,sFACVprB,OAAQ,uEACR6sB,KAAM,8FAER,CACEG,UAAW,iFACX5B,SAAU,gFACVprB,OAAQ,8DACR6sB,KAAM,mGAER,CACEG,UAAW,0EACX5B,SAAU,8EACVprB,OAAQ,4EACR6sB,KAAM,qGAER,CACEG,UAAW,gEACX5B,SAAU,8EACVprB,OAAQ,6DACR6sB,KAAM,qGAER,CACEG,UAAW,oEACX5B,SAAU,wFACVprB,OAAQ,uFACR6sB,KAAM,4FAER,CACEG,UAAW,qEACX5B,SAAU,wFACVprB,OAAQ,0DACR6sB,KAAM,gGAER,CACEG,UAAW,gFACX5B,SAAU,0FACVprB,OAAQ,sGACR6sB,KAAM,kFAER,CACEG,UAAW,oEACX5B,SAAU,+FACVprB,OAAQ,kEACR6sB,KAAM,yFAER,CACEG,UAAW,qEACX5B,SAAU,qFACVprB,OAAQ,uDACR6sB,KAAM,sGAER,CACEG,UAAW,qEACX5B,SAAU,2FACVprB,OAAQ,mFACR6sB,KAAM,mGAER,CACEG,UAAW,yEACX5B,SAAU,sGACVprB,OAAQ,oGACR6sB,KAAM,mGAER,CACEG,UAAW,0DACX5B,SAAU,mGACVprB,OAAQ,oFACR6sB,KAAM,qGJvDVxD,QKzBa,CACb7d,MAAO,gBACPC,YAAa,0LACbwhB,KAAM,CACJxpC,KAAM,SACNypC,gBAAiB,6BACjBC,MAAO,QACPC,iBAAkB,yBAClBC,QAAS,SACTC,mBAAoB,8BACpB1d,QAAS,UACT2d,mBAAoB,iEACpBC,KAAM,iBACNC,QAAS,cACTvd,QAAS,sDACTroB,MAAO,kFACP6lC,aAAc,2BACdC,cAAe,0BACfC,aAAc,iBACdC,gBAAiB,2BACjBC,gBAAiB,4BACjBC,iBAAkB,gDAEpB5d,KAAM,CACJgd,MAAO,qBACPa,SAAU,oBACVC,aAAc,iEAEhBC,OAAQ,CACNzD,SAAU,WACV0D,SAAU,WACVhB,MAAO,ULLThD,cM1Ba,CACb3e,MAAO,gBACP4iB,SAAU,2CACV3iB,YAAa,mEACb4iB,aAAc,iEACd5V,MAAO,SACP71B,KAAM,QACN0rC,UAAW,wBACXC,YAAa,+CACb3T,SAAU,CACR7zB,MAAO,mBACPynC,SAAU,WACVC,SAAU,YACVC,MAAO,WACPC,cAAe,4BACfC,cAAe,6BACfC,WAAY,6BAEdtrB,SAAU,CACRxc,MAAO,iBACP+nC,OAAQ,yBACRC,QAAS,4BACT7E,QAAS,0BACT5yB,SAAU,8BAEZ03B,YAAa,CACXjoC,MAAO,eACP+nC,OAAQ,uBACRC,QAAS,0BACT7E,QAAS,wBACT5yB,SAAU,4BAEZ23B,aAAc,CACZloC,MAAO,qBACP+nC,OAAQ,6BACRC,QAAS,gCACT7E,QAAS,8BACT5yB,SAAU,kCAEZo3B,MAAO,CACL3nC,MAAO,2BACP5E,OAAQ,8CACR+tB,QAAS,wCAEXgf,SAAU,CACRtU,SAAU,mBACVuU,iBAAkB,kBAClBC,iBAAkB,mBAClBC,cAAe,kBACf9rB,SAAU,iBACV+rB,eAAgB,oBAChBC,gBAAiB,uBACjBP,YAAa,eACbQ,kBAAmB,kBACnBC,mBAAoB,sBAEtBC,UAAW,CACTC,cAAe,gCACfC,iBAAkB,uBAEpB7e,OAAQ,CACNmZ,QAAS,WACT5yB,SAAU,cACVu4B,cAAe,mBACfC,cAAe,oBACfC,UAAW,mBACXC,gBAAiB,0BACjBC,iBAAkB,6BAClBC,eAAgB,wBAChBC,gBAAiB,6BN1CnBlG,MO3Ba,CACbC,QAAS,kBACT5yB,SAAU,qBACV84B,YAAa,8BACbC,aAAc,iCACdvB,OAAQ,iBACRC,QAAS,oBACTnF,OAAQ,kBACR0G,QAAS,oBACTC,OAAQ,UACRC,cAAe,8BPkBfa,SQ5Ba,CACbp1B,MAAO,CACLw0B,MAAO,uBACPC,KAAM,wBACNC,OAAQ,gCAEV3G,SAAU,CACR4G,OAAQ,iBACR3sC,QAAS,gBACT4sC,UAAW,uBAEb5H,WAAY,CACVK,KAAM,eACNJ,QAAS,eACTC,SAAU,gBACVC,QAAS,0BACTC,QAAS,uBAEX6E,OAAQ,CACNzD,SAAU,qBACV0C,MAAO,eACPgB,SAAU,uBAEZlE,MAAO,CACL6E,OAAQ,4BACRC,QAAS,gCAEX+B,QAAS,CACPvX,OAAQ,oBACRwX,SAAU,oBACVC,SAAU,oBACVC,MAAO,YACPC,KAAM,gBACNC,MAAO,kBACPC,UAAW,kBRLbhvC,OS7Ba,CACb8tB,QAAS,CACP1E,MAAO,UACP8lB,YAAa,8BACbC,cAAe,4BACfC,WAAY,iCACZC,aAAc,gBACdC,gBAAiB,mBAEnB7pC,MAAO,CACL2jB,MAAO,QACPmmB,eAAgB,0BAChBC,aAAc,oBACdC,aAAc,iBACdC,SAAU,oBAEZ3hB,KAAM,CACJ3E,MAAO,cACP6E,QAAS,cACT0hB,WAAY,gBACZC,OAAQ,gBAEV5hB,QAAS,CACP5E,MAAO,cACPymB,eAAgB,8BAChBC,cAAe,4CTKjB/F,IU9Ba,CACb3gB,MAAO,mDACPC,YAAa,yJACb0mB,SAAU,0GACVC,OAAQ,2BACRC,MAAO,CACL/I,KAAM,CACJ9d,MAAO,mDACPC,YAAa,8JAEf0d,SAAU,CACR3d,MAAO,2CACPC,YAAa,mJAEf2d,QAAS,CACP5d,MAAO,qDACPC,YAAa,mKAEf4d,QAAS,CACP7d,MAAO,0CACPC,YAAa,6IVWjBinB,OW/Ba,CACbJ,OAAQ,CACN7uC,KAAM,2BACN8uC,SAAU,sBACV9mB,YAAa,qFACbuiB,SAAU,oBACVb,MAAO,qBACPqF,OAAQ,CAAC,YAAa,iBAAkB,uBAAwB,2BAA4B,cAAe,0BAE7GC,aAAc,CACZhvC,KAAM,2BACNgoB,YAAa,6CACbuiB,SAAU,sBXoBZyF,KYhCa,CACbvK,QAAS,CACPyJ,MAAO,mCACPC,SAAU,wCACVC,SAAU,+BAEZ1J,SAAU,CACRqD,OAAQ,4FACRE,UAAW,4GACXC,UAAW,wEACXC,gBAAiB,8GAEnB/X,MAAO,CACL8U,KAAM,gBACNlR,MAAO,kBACPc,OAAQ,oBACRwX,SAAU,oBACV+B,SAAU,0BACV9B,SAAU,oBACV7D,MAAO,iBACP4F,MAAO,oBACPtI,SAAU,oBACV0D,SAAU,oBACV6E,OAAQ,kBACRC,IAAK,eACLC,KAAM,gBACNC,MAAO,iBACP5C,OAAQ,mBACRrgB,QAAS,iBACTroB,MAAO,iBACPuoB,QAAS,uBACTD,KAAM,wBAERijB,WAAY,CACVC,SAAU,uBACVC,QAAS,oBACTC,QAAS,iBACTC,WAAY,+BZJdxJ,SajCa,CACbsG,QAAS,4BACTrsC,QAAS,gBACT4sC,UAAW,sBACX6C,WAAY,YACZC,QAAS,UACTC,QAAS,UACTrpB,OAAQ,sBb2BRtO,MclCa,CACb2tB,OAAQ,gBACR0G,QAAS,0BACTG,MAAO,sBACPC,KAAM,uBACNC,OAAQ,kCd8BRmD,OenCa,CACbD,UAAW,wDACXroB,MAAO,uBfkCP4e,SgBpCa,CACb5e,MAAO,WACP4iB,SAAU,qBACV3iB,YAAa,wCACbsoB,aAAc,+CACdtb,MAAO,SACPub,KAAM,SACNxG,KAAM,kBACNC,QAAS,cACTwG,aAAc,kCACdC,cAAe,yBAGfC,QAAS,oBACTC,KAAM,iBACNC,OAAQ,aAGRC,aAAc,oBACdC,UAAW,iBACXC,YAAa,aACbC,aAAc,kBAGdC,mBAAoB,kDACpBC,gBAAiB,0CACjBC,kBAAmB,yBACnBC,mBAAoB,oCAGpBC,mBAAoB,0CACpBC,gBAAiB,mCACjBC,kBAAmB,4BACnBC,mBAAoB,0BAGpBC,WAAY,CACVpH,gBAAiB,4BACjBC,iBAAkB,sBAClBH,aAAc,kBAIhBX,KAAM,CACJlrC,KAAM,mBACN6tB,QAAS,aACTud,MAAO,sBACPK,KAAM,kBACNC,QAAS,cACTvd,QAAS,0EACTroB,MAAO,mFACPimC,gBAAiB,6BAInB/c,OAAQ,CACNb,QAAS,4BACTroB,MAAO,gDACP4lC,QAAS,wBAIX0H,MAAO,CACLC,IAAK,eACLC,WAAY,aACZC,WAAY,SACZC,MAAO,ShB7BTiB,QiBrCa,CACbhrB,MAAO,yBACPC,YAAa,sGACb+pB,UAAW,YACXC,UAAW,gBACXC,UAAW,iBACXC,kBAAmB,yBACnBC,gBAAiB,uBACjBC,SAAU,cACVC,YAAa,CACXtqB,MAAO,2BAET2pB,MAAO,CACLY,UAAW,CACTvqB,MAAO,qBACPC,YAAa,gGAEfuqB,UAAW,CACTxqB,MAAO,sBACPC,YAAa,+GACbwqB,UAAW,eAEbC,UAAW,CACT1qB,MAAO,uBACPC,YAAa,uGjBcjB0qB,OkBtCa,CACb1d,MAAO,SACP71B,KAAM,QACNwzC,KAAM,UACNtd,OAAQ,WACRud,QAAS,YACTC,IAAK,KACLC,GAAI,KACJlmB,QAAS,cACTxoB,MAAO,QACPqoB,QAAS,QACTE,QAAS,cACTD,KAAM,kBzDWRsmB,EACGC,IAAIC,GACJD,IAAIE,GACJrP,KAAK,CACJwB,aACA8N,IAAK,QACLC,YAAa,QAGbC,OAAO,EAGPj2C,MAAO,CACLk2C,aAAa,GAIfC,cAAe,CACbC,aAAa,GAIfC,UAAW,cAGXC,UAAW,CACTjxB,MAAO,CAAC,eAAgB,aACxBkxB,OAAQ,CAAC,mBAGZ7mB,MAAK,SAQLS,OAAOppB,IACEA,cAAM,gCAAiCA,M0DrDnD,MAAMyvC,GAA4C,EAAGjzC,eACnD,MAAOkzC,EAASC,GAAczrB,YAAS,IACjCvpB,EAAEA,GAAMi1C,IAsCd,OApCAjyC,aAAU,KACR,MAAMkyC,EAAiB,KAEjBjB,EAAKkB,eAAiBlB,EAAKmB,kBAAkB,QAAS,eAIxDJ,GAAW,GAIX51C,WAAW81C,EAAgB,MAK/BA,IAGA,MAAMG,EAAwB,KAE5BL,GAAW,IASb,OANKM,KAAG,kBAAmBD,GACtBC,KAAG,eAAe,KAErBJ,OAGK,KACAK,MAAI,kBAAmBF,GACvBE,MAAI,cAAeL,GAC1B,GACC,CAACl1C,IAEC+0C,QAaES,WAAA,CAAG3zC,mBAXLkQ,OAAIuE,UAAU,0EACbzU,gBAACkQ,OAAIuE,UAAU,wBACbhR,EAAAtD,IAACskC,aACAniB,KAAE7N,UAAU,wCAAwCzU,SAAA,kCCnDzD4zC,GAAW,CACf,CAAEC,KAAM,UAAWvf,KAAMwf,EAAMC,UAAW,SAAUC,QAAS,sBAC7D,CAAEH,KAAM,YAAavf,KAAM2f,EAAQF,UAAW,WAAYC,QAAS,uBACnE,CAAEH,KAAM,WAAYvf,KAAM4f,EAAQH,UAAW,UAAWC,QAAS,sBACjE,CAAEH,KAAM,WAAYvf,KAAM6f,EAAMJ,UAAW,UAAWC,QAAS,uBAI3DI,GAAuB,EAAGC,SAAQtqC,UAASuqC,gBAAeC,aAAYnyB,WACrEiyB,SAGFnkC,OAAIuE,UAAU,+CACZvE,OAAIuE,UAAU,4BAA4B3D,QAAS/G,UACnDmG,OAAIuE,UAAU,sEACbzU,gBAACkQ,OAAIuE,UAAU,uBACZvE,OAAIuE,UAAU,yDACZ+/B,MAAG//B,UAAU,wBAAwBzU,SAAA,eACrCoN,UAAO0D,QAAS/G,EAAS0K,UAAU,uDAClCzU,eAAC8mB,GAAErS,UAAU,uBAGhBggC,OAAIhgC,UAAU,YACZ2N,WAAMlkB,KAAKw2C,UACT3yC,KAEC8xC,KAAMa,EAAKb,KACX/iC,QAAUyY,IACRA,EAAE1hB,iBACF0sC,EAAWG,EAAKX,WAChBhqC,KAEF0K,UAAW,6DACT6/B,IAAkBI,EAAKX,UACnB,gEACA,sDAGLW,EAAKpgB,WACLtS,QAAM0yB,WAAK/wB,SAdP+wB,EAAKt2C,gBAhBJ,KAwCtB,SAAwBu2C,KACtB,MAAOC,EAAUC,GAAentB,YAAS,IAClC4sB,EAAeQ,GAAoBptB,WAAS,WAC5CqtB,EAAcC,GAAmBttB,YAAS,IAC1CutB,EAAgBC,GAAqBxtB,YAAS,GAErDvmB,aAAU,KACR,MAAMg0C,EAAW,KACH54C,SAAO64C,QAAU,IAE7B,IAAIC,EAAQ,SACZ,UAAWX,KAAQd,GAAU,CAC3B,MAAM0B,EAAK9vC,SAAS+vC,eAAeb,EAAKX,WACxC,GAAIuB,EAAI,CACAE,QAAOF,EAAGjiB,wBAChB,GAAImiB,EAAKp0B,KAAO,IAAMo0B,EAAKl0B,OAAS,GAAI,CACtC+zB,EAAQX,EAAKX,UACb,MACF,CACF,CAEFe,EAAiBO,IAInB,OAFO5vC,wBAAiB,SAAU0vC,EAAU,CAAEM,SAAS,IACvDN,IACO,IAAM54C,OAAOmJ,oBAAoB,SAAUyvC,KACjD,IAEH,MAAMO,EAAiBn0C,eAAY,CAACgoB,EAAqBwqB,KACvDxqB,EAAE1hB,iBACIytC,QAAK9vC,SAAS+vC,eAAexB,GAC/BuB,IACF/4C,OAAOo5C,SAAS,CACdv0B,IAAKk0B,EAAGM,UAAY,GACpBC,SAAU,WAEZf,EAAiBf,MAElB,IAEG+B,EAAyBv0C,eAAawyC,IACpCuB,QAAK9vC,SAAS+vC,eAAexB,GAC/BuB,IACF/4C,OAAOo5C,SAAS,CACdv0B,IAAKk0B,EAAGM,UAAY,GACpBC,SAAU,WAEZf,EAAiBf,MAElB,kBAGAgC,UACCthC,UAAW,qHACPmgC,EAAW,sCAAwC,2EAGtDH,OAAIhgC,UAAU,gFAEZ1S,KAAE8xC,KAAK,UAAUp/B,UAAU,oFAC1BzU,eAACgiB,QAAKvN,UAAU,wEAAwEzU,SAAA,gBAIzFg2C,MAAGvhC,UAAU,0CACXm/B,GAAS11C,KAAIw2C,IACZ,MAAMuB,EAAOvB,EAAKpgB,KACZ4hB,EAAW5B,IAAkBI,EAAKX,uBAErClnC,MACC7M,gBAAC+B,KACC8xC,KAAMa,EAAKb,KACX/iC,QAASyY,GAAKmsB,EAAensB,EAAGmrB,EAAKX,WACrCt/B,UAAW,2DACTyhC,EAAW,gBAAkB,wEAG9BD,GAAKxhC,UAAU,wBACfuN,QAAKvN,UAAU,gCACM,WAAnBigC,EAAKX,WAA0B,SACZ,aAAnBW,EAAKX,WAA4B,WACd,YAAnBW,EAAKX,WAA2B,UACb,YAAnBW,EAAKX,WAA2B,aAElCmC,SACEhmC,OAAIuE,UAAU,uFAhBZigC,EAAKb,KAAI,aAyBvB3jC,OAAIuE,UAAU,0CAEZvE,OAAIuE,UAAU,YACbzU,eAACoN,UACC0D,QAAS,IAAMokC,GAAmBD,GAClCxgC,UAAU,oKAETwgC,WAAiBxxC,EAAAtD,IAAC2mB,GAAErS,UAAU,YAAehR,EAAAtD,IAACg2C,GAAK1hC,UAAU,sBAKjEvE,OAAIuE,UAAU,oCACbzU,eAACoN,UACC0D,QAAS,IAAMkkC,GAAgB,GAC/BvgC,UAAU,oLAEVzU,eAACo2C,GAAc3hC,UAAU,4BAOhC2/B,IACCC,OAAQY,EACRlrC,QAAS,IAAMmrC,GAAkB,GACjCZ,gBACAC,WAAYuB,EACZ1zB,MAAO,CACL,CACEhkB,GAAI,SACJ21C,UAAW,SACXpwB,KAAM,SACNkwB,KAAM,UACNvf,WAAOwf,GAAKr/B,UAAU,aAExB,CACErW,GAAI,WACJ21C,UAAW,WACXpwB,KAAM,WACNkwB,KAAM,YACNvf,WAAO2f,GAAOx/B,UAAU,aAE1B,CACErW,GAAI,UACJ21C,UAAW,UACXpwB,KAAM,UACNkwB,KAAM,WACNvf,WAAO4f,GAAOz/B,UAAU,aAE1B,CACErW,GAAI,UACJ21C,UAAW,UACXpwB,KAAM,UACNkwB,KAAM,WACNvf,WAAO6f,GAAK1/B,UAAU,gBAM3BsgC,SACE7kC,OAAIuE,UAAU,kEAAkE3D,QAAS,IAAMkkC,GAAgB,GAC9Gh1C,gBAACkQ,OAAIuE,UAAU,0FACZ4hC,MAAG5hC,UAAU,6BAA6BzU,SAAA,mBAC1CsiB,KAAE7N,UAAU,wCAAwCzU,SAAA,yCACpDoN,UACC0D,QAAS,IAAMkkC,GAAgB,GAC/BvgC,UAAU,+EACXzU,SAAA,kBAQb,CC/MA,MAAMs2C,GAAcC,QAAK,WAAMC,OAAO,kEAChCC,GAAiBF,QAAK,WAAMC,OAAO,oEACnCE,GAAsBH,QAAK,WAAMC,OAAO,iEAGxCG,GAA0BJ,QAAK,IAAMK,GAAA,IAAAJ,OAAO,gFAC5CK,GAAkBN,QAAK,IAAMK,GAAA,IAAAJ,OAAO,6DACpCM,KAAqCP,MAAA,IAAMK,GAAA,IAAAJ,OAAO,0CAAAO,6BAAwC5qB,MAAK6qB,IAAW,CAAEtwB,QAASswB,EAAOtwB,cAK5HuwB,GAAoBV,QAAK,WAAMC,OAAO,2DACtCU,GAAsBX,QAAK,WAAMC,OAAO,mEAGxCW,GAAc,IAAIC,EAElBC,GAAM,KACJ,MAAAl5C,EAAEA,GAAMi1C,iBAGX/S,IACCrgC,eAACs3C,GAAoBz6C,OAAQs6C,GAC3Bn3C,eAACizC,IACCjzC,eAACy2B,IACDz2B,eAACu3C,YAASC,eAAWtnC,OAAIuE,UAAU,gDAAgDzU,SAAA4mB,MAAC6d,SAClFzkC,gBAAC22C,oBAGA50C,KAAE8xC,KAAK,gBAAgBp/B,UAAU,qBAC/BtW,EAAE,gDAEJ4D,KAAE8xC,KAAK,cAAcp/B,UAAU,qBAC7BtW,EAAE,6CAGLsF,EAAAtD,IAAC4mB,OACDtjB,EAAAtD,IAACm2B,aAKAihB,YAASC,SAAU,KAClBx3C,SAAA4mB,MAACkwB,UAIF,GAKA,GAOA,QASAS,YAASC,SAAU,KAClBx3C,SAAA4mB,MAACiwB,eAIFU,YAASC,SAAU,KAClBx3C,eAACi3C,IAAY5xB,MAAO,cAIrBkyB,YAASC,SAAU,KAClBx3C,SAAA4mB,MAACswB,UAIF,EAMHzzC,EAAAtD,IAACw0C,aACA8C,GACCC,SAAS,aACTC,OAAQ,CACNC,oBAAoB,EACpBC,sBAAsB,GAGxB73C,eAACu3C,YAASC,eACPtnC,OAAIuE,UAAU,gDACbzU,SAAA4mB,MAAC6d,SAGHzkC,gBAAC83C,mBACEC,GAAM1/B,KAAK,IAAI2/B,QAASpxB,MAAC0vB,eACzByB,GAAM1/B,KAAK,kBAAkB2/B,QAASpxB,MAAC8vB,eAEvCqB,GAAM1/B,KAAK,IAAI2/B,QAASpxB,MAAC6vB,8BCkG7BwB,GAA+B,KA1JJ,MAChCC,QAAU,cACU,CACxB,CACErE,KAAM,GAAGqE,8BACTC,GAAI,QACJz6C,KAAM,aACN06C,cAAe,QAEjB,CACEvE,KAAM,GAAGqE,6BACTC,GAAI,QACJz6C,KAAM,YACN06C,cAAe,SAMD/5C,SAAQg6C,IAClBjZ,QAAO55B,SAASikB,cAAc,QACpC2V,EAAK9F,IAAM,UACX8F,EAAKyU,KAAOwE,EAASxE,KACrBzU,EAAK+Y,GAAKE,EAASF,GAEfE,EAAS36C,OACX0hC,EAAK1hC,KAAO26C,EAAS36C,MAGnB26C,EAASD,eACN9tC,eAAa,gBAAiB+tC,EAASD,eAG1CC,EAASC,aAAwC,KAAzBD,EAASC,aAC9BhuC,eAAa,cAAe+tC,EAASC,aAI5ClZ,EAAKmZ,QAAU,KACbh1C,QAAQw0B,KAAK,+BAA+BsgB,EAASxE,OAAM,EAGpDpmB,cAAKI,YAAYuR,KAC5B,EAmHEoZ,GArBgBhzC,SAAS5D,iBAAiB,sCAClCvD,SAAQ+gC,IACVyU,QAAOzU,EAAKvK,aAAa,QAC/B,GAAIgf,IAASA,EAAKlmC,SAAS,gBAAiB,CACpC8qC,QAAM,IAAIC,IAAI7E,GAChB8E,eAAah7C,IAAI,UAAW,QAChCyhC,EAAK90B,aAAa,OAAQmuC,EAAI15C,WAAQ,KA9FPqtB,WACnC,GAAI,kBAAmBwsB,UACjB,IAEIC,QAAS,mBAGRC,aAFoBF,UAAUG,cAAcC,SAASH,SAGrDr1C,GAEA,OADCu0B,aAAK,sCAAuCv0B,GAC7C,KAGJ,EAwGLy1C,GAIF,MAAMC,EAtG2B,MACjC,GAAI,yBAA0B38C,OAAQ,CACpC,MAAM28C,EAAgB,IAAIC,sBAAsBhkC,IACtC9W,WAAQ+6C,IACd,GAAIA,EAAMC,eAAgB,CACxB,MAAMC,EAAMF,EAAM7xC,OAGd+xC,EAAIznC,QAAQ0nC,MACVA,MAAMD,EAAIznC,QAAQ0nC,IACtBD,EAAIxX,gBAAgB,aAIlBwX,EAAIznC,QAAQ2nC,SACVA,SAASF,EAAIznC,QAAQ2nC,OACzBF,EAAIxX,gBAAgB,gBAIlBzmB,YAAUkN,OAAO,gBACjBlN,YAAUmN,IAAI,eAGlB0wB,EAAcO,UAAUH,MAE5B,GACC,CACDI,WAAY,WACZrnC,UAAW,MAGN6mC,SAGF,aAmEeS,GAEtB,GAAIT,EAAe,CAEE1zC,SAAS5D,iBAAiB,iBAClCvD,SAAQi7C,GAAOJ,EAAcU,QAAQN,KAIxB,CAIxB,MAAMO,EAAyB,GAK3BA,EAAa30C,OAAS,GA/EW,CAAC20C,IACpC,wBAAyBt9C,OAC3Bu9C,qBAAoB,KACLz7C,WAAQo6C,IACbrZ,QAAO55B,SAASikB,cAAc,QACpC2V,EAAK9F,IAAM,WACX8F,EAAKyU,KAAO4E,EACHhrB,cAAKI,YAAYuR,KAC5B,IAIF7hC,YAAW,KACIc,WAAQo6C,IACbrZ,QAAO55B,SAASikB,cAAc,QACpC2V,EAAK9F,IAAM,WACX8F,EAAKyU,KAAO4E,EACHhrB,cAAKI,YAAYuR,KAC5B,GACC,MA6DD2a,CAA0BF,EAC5B,GCtLEG,GAAaT,GACV,IAAIrtB,SAAQ,CAAC+tB,EAASC,KACrBZ,QAAM,IAAIa,MACZC,SAAS,IAAMH,IACf1B,UAAU,IAAM2B,EAAO,IAAI9jB,MAAM,yBAAyBmjB,MAC9DD,EAAIC,IAAMA,KAORl0B,GAASpC,GACN,IAAIiJ,SAAQ+tB,GAAW18C,WAAW08C,EAASh3B,KAmHvCo3B,GAA+B,KA5LP,MAC7BnC,QAAU,cACO,CACrB,GAAGA,8BACH,GAAGA,6BACH,GAAGA,+BACH,GAAGA,iCAGU75C,SAAQk7C,IACfna,QAAO55B,SAASikB,cAAc,QACpC2V,EAAK9F,IAAM,UACX8F,EAAK+Y,GAAK,QACV/Y,EAAKyU,KAAO0F,EAGZna,EAAKmZ,QAAU,KACLxgB,aAAK,4BAA4BwhB,IAAK,EAGvC9rB,cAAKI,YAAYuR,KAC5B,EAyKAkb,GAGMC,SAvDwCjrC,EAuDI6F,IACxC9W,WAAQ+6C,IACd,GAAIA,EAAMC,eAAgB,CACxB,MAAMC,EAAMF,EAAM7xC,OACZgyC,EAAMD,EAAIznC,QAAQ0nC,IAEpBA,GA5KyBntB,OAAOouB,IAC1C,MAAMjB,IAAEA,EAAK/B,yBAAUiD,EAAgB,EAAGC,aAAa,KAASF,EAEhE,QAASG,EAAU,EAAGA,EAAUF,EAAeE,IACzC,IAEKpB,aADDS,GAAUT,GACTA,QACA/1C,GACPD,QAAQw0B,KAAK,sBAAsB4iB,EAAU,iBAAiBpB,KAE1DoB,EAAUF,EAAgB,SACtBp1B,GAAMq1B,EACd,CAKJ,GAAIlD,EACE,IAEKA,aADDwC,GAAUxC,GACTA,QACAh0C,GACCu0B,aAAK,+BAA+Byf,IAAU,CAK1D,OAAOgD,EAAQx2B,aAAe,IAkJA42B,CAAA,CAAErB,QACrBptB,MAAK0uB,IACJvB,EAAIC,IAAMsB,EACVvB,EAAIxX,gBAAgB,YAChBzmB,YAAUkN,OAAO,gBACjBlN,YAAUmN,IAAI,kBAEnBoE,OAAMppB,IACGu0B,aAAK,6BAA8Bv0B,GACvC6X,YAAUmN,IAAI,iBAIxB+xB,WAAUd,UAAUH,MAExB,EA5EI,yBAA0B/8C,OAKzB,IAAI48C,qBAAqB7pC,EAAU,CACxCoqC,WAAY,WACZrnC,UAAW,MALJ,MAHoC,IAAC/C,EAiF9C,GAAIirC,EAAU,CACO/0C,SAAS5D,iBAAiB,iBAClCvD,SAAQi7C,GAAOiB,EAASX,QAAQN,OCrOxC,MAAMwB,GAIX,gBAAOC,CAAUC,GACX,IACKC,YAAKC,MAAMF,SACXx3C,GAEA,OADCA,cAAM,oCAAqCA,GAC5C,KACT,CAMF,0BAAO23C,CAAoBC,EAAkBC,GAC3C,OAAOD,EAASv2C,QAAQ,kBAAkB,CAACizB,EAAO1yB,SACtB9G,IAAnB+8C,EAAUj2C,GAAqB03B,OAAOue,EAAUj2C,IAAQ0yB,GACjE,CAMF,wBAAOwjB,CAAkB7e,EAAUpkB,GACjC,OAAOA,EAAKjC,MAAM,KAAKhB,QAAO,CAACxV,EAASwF,IAC/BxF,QAA4BtB,IAAjBsB,EAAQwF,GAAqBxF,EAAQwF,QAAO9G,GAC7Dm+B,GAML,wBAAO8e,CAAkB9e,EAAUpkB,EAAcrE,GACzCa,QAAOwD,EAAKjC,MAAM,KAClBolC,EAAU3mC,EAAK4mC,MAErB,IAAKD,EAAS,OAEC3mC,EAAKO,QAAO,CAACxV,EAASwF,KAC9BxF,EAAQwF,IAAgC,iBAAjBxF,EAAQwF,KAC1BA,KAAO,CAAC,GAEXxF,EAAQwF,KACdq3B,GAEI+e,GAAWxnC,EAOpB,2BAAO0nC,CAAqBC,EAAmBh7C,GAC7C,MAAMi7C,EAA2C,CAC/CpzB,IAAO,CAACzmB,EAAWC,IAAcD,EAAIC,EACrC65C,SAAY,CAAC95C,EAAWC,IAAcD,EAAIC,EAC1C85C,SAAY,CAAC/5C,EAAWC,IAAcD,EAAIC,EAC1C6iB,OAAU,CAAC9iB,EAAWC,IAAoB,IAANA,EAAUD,EAAIC,EAAI,EACtDwzB,OAAU,CAACzzB,EAAWC,IAAcD,EAAIC,EACxC+5C,UAAcnf,GAAgBA,EAAIof,cAClCC,UAAcrf,GAAgBA,EAAI1B,cAClCh2B,OAAWg3C,GAAeA,EAAIh3C,OAC9B4B,QAAYo1C,GAAe,IAAIA,GAAKp1C,UACpChF,KAASo6C,GAAe,IAAIA,GAAKp6C,QAG/B85C,SAAeD,GACVC,EAAeD,IAGhB5jB,aAAK,yCAA0C4jB,GAChD,MAMT,uBAAOQ,CAAiB7sC,EAAoB+V,GACtC,MAAoB,mBAAb/V,GACT/L,QAAQC,MAAM,uDACP,GAEFjH,OAAOgB,WAAW+R,EAAU+V,GAGrC,wBAAO+2B,CAAkB9sC,EAAoB+V,GACvC,MAAoB,mBAAb/V,GACT/L,QAAQC,MAAM,wDACP,GAEFjH,OAAO8/C,YAAY/sC,EAAU+V,GAMtC,yBAAai3B,CAAaC,GAExB,IAAKvxB,KAAKwxB,kBAAkBD,GACpB,UAAInmB,MAAM,oCAGd,IACF,aAAaogB,OAA0B+F,SAChC/4C,GAEDA,MADEA,cAAM,wCAAyC+4C,EAAY/4C,GAC7DA,EACR,CAMF,wBAAeg5C,CAAkBnkC,GAS/B,MAPsB,CACpB,QACA,UACA,sCACA,oBAGmB7E,MAAKy7B,GAAWA,EAAQ13B,KAAKc,KAMpD,mBAAOokC,CAAaC,GACZxsC,QAAM1K,SAASikB,cAAc,OAEnC,OADAvZ,EAAIe,YAAcyrC,EACXxsC,EAAIutB,UAMb,qBAAOkf,CAAevoC,EAAcwoC,GAC9B,IACInE,QAAM,IAAIC,IAAItkC,GAMpB,OAJOe,eAAQynC,GAAQv+C,SAAQ,EAAE+G,EAAK4O,MAChC2kC,eAAah7C,IAAIyH,EAAK4O,MAGrBykC,EAAI15C,iBACJyE,GAEA4Q,OADC5Q,cAAM,yCAA0CA,GACjD4Q,EACT,CAMF,6BAAOyoC,GAED,GAAkB,oBAAXtgD,OAAwB,CAG1BugD,YAAO,SAASz3C,GAMb,MADN9B,QAAQC,MAAM,4EACR,IAAI4yB,MAAM,uDAEpB,EAKO2mB,gBAAW,YAAY5kB,GAMpB,MADN50B,QAAQC,MAAM,2DACR,IAAI4yB,MAAM,qEAEpB,EAGA,MAAM4mB,EAAqBzgD,OAAOgB,WAC5B0/C,EAAsB1gD,OAAO8/C,YAEnC9/C,OAAOgB,WAAa,SAASyU,EAAc1U,KAAqB66B,GAC1D,GAAmB,iBAAZnmB,EAMD,MADNzO,QAAQC,MAAM,sEACR,IAAI4yB,MAAM,wEAGpB,OAAO4mB,EAAmBjwB,KAAK/B,KAAMhZ,EAAS1U,KAAY66B,EAC5D,EAEA57B,OAAO8/C,YAAc,SAASrqC,EAAc1U,KAAqB66B,GAC3D,GAAmB,iBAAZnmB,EAMD,MADNzO,QAAQC,MAAM,uEACR,IAAI4yB,MAAM,yEAGpB,OAAO6mB,EAAoBlwB,KAAK/B,KAAMhZ,EAAS1U,KAAY66B,EAC7D,EAGF,CAMF,oBAAO+kB,GACCC,QAAQ,IAAIC,WAAW,IAE7B,OADAC,OAAOC,gBAAgBH,GAChBI,KAAKzgB,OAAO0gB,gBAAgBL,IAMrC,4BAAOM,GACL,MAAMC,EAAuB,GAGzB,GAAkB,oBAAXnhD,OAAwB,CAEdA,OAAOugD,KAAK/9C,WACf4O,SAAS,yBACvB+vC,EAAWrsC,KAAK,oCAIK9U,OAAOwgD,SAASh+C,WACnB4O,SAAS,iCAC3B+vC,EAAWrsC,KAAK,iDAClB,CAGEqsC,UAAWx4C,OAAS,KACd6yB,aAAK,wCAAyC2lB,IAC/C,EAIF,ECrPW,oBAAXnhD,SACRA,OAAeC,MAAQA,GAG1B,MAAMmhD,GAAcn4C,SAAS+vC,eAAe,QAC5C,IAAKoI,GACG,UAAIvnB,MAAM,0BAIlB,MAAM/N,GAAOu1B,EAAS9gD,WAAW6gD,IAGX,oBAAXphD,SAET07C,KACAoC,KAGA98C,YAAW,KD0OW,oBAAXhB,SAGPu+C,GAAY+B,yBAGZt/C,YAAW,KACTu9C,GAAY2C,uBAAqB,GAChC,KCjPLI,GACC,MAILx1B,GAAKoY,OACFjkC,mBAAgB,CACfwD,eAACsnB,IAAcC,aAAa,SAASC,WAAW,kBAC9CxnB,SAAA4mB,MAACywB", "names": ["window", "React", "react", "global", "m", "require$$0", "client", "createRoot", "hydrateRoot", "count", "toastTimeouts", "Map", "addToRemoveQueue", "toastId", "has", "timeout", "setTimeout", "delete", "dispatch", "type", "set", "reducer", "state", "action", "toasts", "toast", "slice", "map", "t", "id", "for<PERSON>ach", "undefined", "open", "filter", "listeners", "memoryState", "listener", "props", "Number", "MAX_SAFE_INTEGER", "toString", "dismiss", "onOpenChange", "update", "createCollection", "name", "PROVIDER_NAME", "createCollectionContext", "createCollectionScope", "createContextScope", "CollectionProviderImpl", "useCollectionContext", "collectionRef", "current", "itemMap", "CollectionProvider", "scope", "children", "ref", "useRef", "jsx", "displayName", "COLLECTION_SLOT_NAME", "CollectionSlotImpl", "createSlot", "CollectionSlot", "forwardRef", "forwardedRef", "context", "composedRefs", "useComposedRefs", "ITEM_SLOT_NAME", "ITEM_DATA_ATTR", "CollectionItemSlotImpl", "CollectionItemSlot", "itemData", "useEffect", "Provider", "Slot", "ItemSlot", "useCallback", "collectionNode", "orderedNodes", "Array", "from", "querySelectorAll", "values", "sort", "a", "b", "indexOf", "Collection", "useCollection", "createToastContext", "createToastScope", "ToastProviderProvider", "useToastProviderContext", "ToastProvider", "__scopeToast", "label", "duration", "swipeDirection", "swipe<PERSON><PERSON><PERSON><PERSON>", "viewport", "setViewport", "React.useState", "toastCount", "setToastCount", "isFocusedToastEscapeKeyDownRef", "React.useRef", "isClosePausedRef", "trim", "console", "error", "jsxRuntimeExports", "onViewportChange", "onToastAdd", "React.useCallback", "prevCount", "onToastRemove", "VIEWPORT_NAME", "VIEWPORT_DEFAULT_HOTKEY", "VIEWPORT_PAUSE", "VIEWPORT_RESUME", "ToastViewport", "React.forwardRef", "hotkey", "viewportProps", "getItems", "wrapperRef", "headFocusProxyRef", "tailFocusProxyRef", "hotkeyLabel", "join", "replace", "hasToasts", "React.useEffect", "handleKeyDown", "event", "length", "every", "key", "code", "_a", "focus", "document", "addEventListener", "removeEventListener", "wrapper", "handlePause", "pauseEvent", "CustomEvent", "dispatchEvent", "handleResume", "resumeEvent", "handleFocusOutResume", "contains", "relatedTarget", "handlePointerLeaveResume", "activeElement", "getSortedTabbableCandidates", "tabbingDirection", "tabbableCandidates", "toastItem", "toastNode", "toastTabbableCandidates", "getTabbableCandidates", "reverse", "flat", "isMetaKey", "altKey", "ctrl<PERSON>ey", "metaKey", "focusedElement", "isTabbingBackwards", "shift<PERSON>ey", "target", "sortedCandidates", "index", "findIndex", "candidate", "focusFirst", "preventDefault", "_b", "_c", "jsxs", "DismissableLayer.Branch", "role", "tabIndex", "style", "pointerEvents", "FocusProxy", "onFocusFromOutsideViewport", "Primitive", "ol", "FOCUS_PROXY_NAME", "proxyProps", "VisuallyHidden", "position", "onFocus", "prevFocusedElement", "TOAST_NAME", "Toast", "forceMount", "openProp", "defaultOpen", "toastProps", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "onChange", "caller", "Presence", "present", "ToastImpl", "onClose", "onPause", "useCallbackRef", "onResume", "onSwipeStart", "composeEventHandlers", "currentTarget", "setAttribute", "onSwipeMove", "x", "y", "detail", "delta", "setProperty", "onSwipeCancel", "removeProperty", "onSwipeEnd", "ToastInteractiveProvider", "useToastInteractiveContext", "durationProp", "onEscapeKeyDown", "node", "setNode", "node2", "pointerStartRef", "swipeDeltaRef", "closeTimerStartTimeRef", "closeTimerRemainingTimeRef", "closeTimerRef", "handleClose", "startTimer", "duration2", "Infinity", "clearTimeout", "Date", "getTime", "elapsedTime", "announceTextContent", "React.useMemo", "getAnnounceTextContent", "Fragment", "ToastAnnounce", "ReactDOM.createPortal", "createPortal", "DismissableLayer.Root", "<PERSON><PERSON><PERSON><PERSON>", "li", "userSelect", "touchAction", "onKeyDown", "nativeEvent", "defaultPrevented", "onPointerDown", "button", "clientX", "clientY", "onPointerMove", "hasSwipeMoveStarted", "Boolean", "isHorizontalSwipe", "includes", "clamp", "Math", "min", "max", "clampedX", "clampedY", "moveStartBuffer", "pointerType", "eventDetail", "originalEvent", "handleAndDispatchCustomEvent", "discrete", "isDeltaInDirection", "setPointerCapture", "pointerId", "abs", "onPointerUp", "hasPointerCapture", "releasePointerCapture", "event2", "once", "announceProps", "renderAnnounceText", "setRenderAnnounceText", "isAnnounced", "set<PERSON>s<PERSON>nn<PERSON>", "callback", "fn", "useLayoutEffect", "raf1", "raf2", "requestAnimationFrame", "cancelAnimationFrame", "useNextFrame", "timer", "Portal", "ToastTitle", "titleProps", "div", "ToastDescription", "descriptionProps", "ACTION_NAME", "ToastAction", "altText", "actionProps", "ToastAnnounceExclude", "ToastClose", "CLOSE_NAME", "closeProps", "interactiveContext", "onClick", "announceExcludeProps", "container", "textContent", "childNodes", "nodeType", "TEXT_NODE", "push", "ELEMENT_NODE", "isHTMLElement", "isHidden", "ariaHidden", "hidden", "display", "isExcluded", "dataset", "radixToastAnnounceExclude", "radixToastAnnounceAlt", "handler", "bubbles", "cancelable", "dispatchDiscreteCustomEvent", "direction", "threshold", "deltaX", "deltaY", "isDeltaX", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "tagName", "disabled", "FILTER_SKIP", "FILTER_ACCEPT", "nextNode", "currentNode", "candidates", "previouslyFocusedElement", "some", "Viewport", "Root2", "Title", "Description", "Action", "Close", "falsyToString", "value", "cx", "clsx", "cva", "base", "config", "_config_compoundVariants", "variants", "class", "className", "defaultVariants", "getVariantClassNames", "Object", "keys", "variant", "variantProp", "defaultVariantProp", "variant<PERSON><PERSON>", "propsWithoutUndefined", "entries", "reduce", "acc", "param", "getCompoundVariantClassNames", "compoundVariants", "cvClass", "cvClassName", "compoundVariantOptions", "isArray", "createClassGroupUtils", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "getClassGroupId", "classParts", "split", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "validators", "classRest", "find", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "theme", "prefix", "getPrefixedClassGroupEntries", "classGroups", "classGroup", "processClassesRecursively", "classDefinition", "isThemeGetter", "get<PERSON>art", "path", "currentClassPartObject", "pathPart", "func", "classGroupEntries", "fromEntries", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "createParseClassName", "separator", "experimentalParseClassName", "isSeparatorSingleCharacter", "firstSeparatorCharacter", "separator<PERSON><PERSON><PERSON>", "parseClassName", "modifiers", "postfixModifierPosition", "<PERSON><PERSON><PERSON><PERSON>", "modifierStart", "currentCharacter", "baseClassNameWithImportantModifier", "hasImportantModifier", "startsWith", "baseClassName", "maybePostfixModifierPosition", "sortModifiers", "sortedModifiers", "unsortedModifiers", "modifier", "SPLIT_CLASSES_REGEX", "twJoin", "argument", "resolvedValue", "string", "arguments", "toValue", "mix", "k", "createTailwindMerge", "createConfigFirst", "createConfigRest", "configUtils", "cacheGet", "cacheSet", "functionToCall", "classList", "previousConfig", "createConfigCurrent", "createConfigUtils", "tailwindMerge", "cachedResult", "result", "classGroupsInConflict", "classNames", "originalClassName", "variantModifier", "modifierId", "classId", "conflictGroups", "i", "group", "mergeClassList", "apply", "fromTheme", "themeGetter", "arbitraryValueRegex", "fractionRegex", "stringLengths", "Set", "tshirtUnitRegex", "lengthUnitRegex", "colorFunctionRegex", "shadowRegex", "imageRegex", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "isArbitraryLength", "getIsArbitraryValue", "is<PERSON>engthOnly", "isNaN", "isArbitraryNumber", "isInteger", "isPercent", "endsWith", "isArbitraryValue", "isTshirtSize", "sizeLabels", "isArbitrarySize", "isNever", "isArbitraryPosition", "imageLabels", "isArbitraryImage", "isImage", "isArbitraryShadow", "is<PERSON><PERSON>ow", "isAny", "testValue", "twMerge", "colors", "spacing", "blur", "brightness", "borderColor", "borderRadius", "borderSpacing", "borderWidth", "contrast", "grayscale", "hueRotate", "invert", "gap", "gradientColorStops", "gradientColorStopPositions", "inset", "margin", "opacity", "padding", "saturate", "scale", "sepia", "skew", "space", "translate", "getSpacingWithAutoAndArbitrary", "getSpacingWithArbitrary", "getLengthWithEmptyAndArbitrary", "getNumberWithAutoAndArbitrary", "getZeroAndEmpty", "getNumberAndArbitrary", "aspect", "columns", "box", "float", "clear", "isolation", "object", "overflow", "overscroll", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "basis", "flex", "grow", "shrink", "order", "col", "span", "row", "justify", "content", "items", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "size", "text", "font", "tracking", "leading", "list", "placeholder", "decoration", "indent", "align", "whitespace", "break", "hyphens", "bg", "repeat", "via", "to", "rounded", "border", "divide", "outline", "ring", "shadow", "table", "caption", "transition", "ease", "delay", "animate", "transform", "rotate", "origin", "accent", "appearance", "cursor", "caret", "resize", "scroll", "snap", "touch", "select", "fill", "stroke", "sr", "cn", "inputs", "ToastPrimitives", "toastVariants", "default", "destructive", "_jsx", "toast-close", "X", "Toaster", "setState", "splice", "useToast", "title", "description", "ThemeContext", "ThemeProvider", "defaultTheme", "storageKey", "setThemeState", "useState", "resolvedTheme", "setResolvedTheme", "isLoading", "setIsLoading", "getSystemTheme", "matchMedia", "matches", "resolveTheme", "currentTheme", "applyTheme", "root", "documentElement", "remove", "add", "savedTheme", "localStorage", "getItem", "initialTheme", "resolved", "mediaQuery", "handleChange", "setTheme", "newTheme", "setItem", "te", "Yt", "visible", "n", "e", "E", "createElement", "ee", "xmlns", "viewBox", "height", "width", "fillRule", "d", "clipRule", "oe", "ae", "se", "<PERSON>t", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x1", "y1", "x2", "y2", "bt", "v", "constructor", "this", "subscribe", "subscribers", "publish", "addToast", "create", "S", "message", "u", "f", "g", "dismissible", "dismissedToasts", "success", "info", "warning", "loading", "promise", "Promise", "then", "async", "re", "isValidElement", "ie", "ok", "T", "status", "F", "catch", "D", "finally", "call", "unwrap", "assign", "custom", "getActiveToasts", "le", "tt", "getHistory", "getToasts", "insertAt", "head", "getElementsByTagName", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "append<PERSON><PERSON><PERSON>", "styleSheet", "cssText", "createTextNode", "wt", "be", "M", "ve", "Dt", "Pt", "Nt", "Bt", "Ct", "kt", "It", "Mt", "Ht", "At", "Lt", "unstyled", "interacting", "setHeights", "visibleToasts", "heights", "expanded", "removeToast", "defaultRichColors", "closeButton", "et", "ut", "cancelButtonStyle", "ft", "actionButtonStyle", "l", "ot", "descriptionClassName", "at", "st", "loadingIcon", "rt", "expandByDefault", "B", "s", "icons", "P", "closeButtonAriaLabel", "nt", "pauseWhenPageIsHidden", "it", "Y", "C", "o", "lt", "J", "W", "H", "A", "L", "ct", "R", "j", "_", "O", "G", "Vt", "Ut", "N", "V", "Kt", "Xt", "dt", "useMemo", "r", "Jt", "Tt", "gt", "U", "St", "K", "Gt", "Qt", "Rt", "c", "Et", "$t", "Ft", "qt", "ht", "getBoundingClientRect", "$", "onAutoClose", "richColors", "onDragEnd", "Q", "q", "Z", "getPropertyValue", "I", "on<PERSON><PERSON><PERSON>", "zt", "getSelection", "swipeDirections", "xe", "close", "xt", "icon", "loader", "jt", "cancel", "cancelButton", "actionButton", "_t", "getAttribute", "getComputedStyle", "Te", "$e", "fe", "expand", "offset", "mobileOffset", "toastOptions", "dir", "containerAriaLabel", "concat", "vt", "flushSync", "addListener", "preventScroll", "suppressHydrationWarning", "onBlur", "HTMLElement", "onMouseEnter", "onMouseMove", "onMouseLeave", "useContext", "Error", "useTheme", "<PERSON><PERSON>", "fontSize", "fontWeight", "TooltipProvider", "TooltipPrimitive", "sideOffset", "hasElementType", "Element", "hasMap", "hasSet", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "equal", "next", "done", "RegExp", "source", "flags", "valueOf", "prototype", "hasOwnProperty", "$$typeof", "reactFastCompare", "match", "warn", "invariant", "condition", "format", "args", "argIndex", "framesToPop", "shallowequal", "objA", "objB", "compare", "compareContext", "ret", "keysA", "keysB", "bHasOwnProperty", "bind", "idx", "valueA", "valueB", "TAG_NAMES", "TAG_NAMES2", "SEO_PRIORITY_TAGS", "rel", "charset", "VALID_TAG_NAMES", "REACT_TAG_MAP", "accesskey", "contenteditable", "contextmenu", "itemprop", "tabindex", "HTML_TAG_MAP", "carry", "HELMET_ATTRIBUTE", "HELMET_PROPS", "getInnermostProperty", "propsList", "getTitleFromPropsList", "innermostTitle", "innermostTemplate", "innermostDefaultTitle", "getOnChangeClientState", "getAttributesFromPropsList", "tagType", "tagAttrs", "getBaseTagFromPropsList", "primaryAttributes", "innermostBaseTag", "tag", "lowerCaseAttributeKey", "toLowerCase", "getTagsFromPropsList", "approvedSeenTags", "msg", "approvedTags", "instanceTags", "instanceSeenTags", "primaryAttributeKey", "keys2", "<PERSON><PERSON><PERSON>", "tagUnion", "getAnyTrueFromPropsList", "checkedTag", "flattenArray", "possible<PERSON><PERSON>y", "prioritizer", "elementsList", "propsToMatch", "elementAttrs", "toMatch", "checkIfPropsMatch", "priority", "without", "obj", "SELF_CLOSING_TAGS", "encodeSpecialCharacters", "str", "encode", "String", "generateElementAttributesAsString", "attributes", "attr", "convertElementAttributesToReactProps", "initProps", "generateTagsAsReactComponent", "tags", "mappedTag", "attribute", "mappedAttribute", "innerHTML", "dangerouslySetInnerHTML", "__html", "getMethodsForTag", "toComponent", "_type", "generateTitleAsReactComponent", "titleAttributes", "attributeString", "flattenedTitle", "generateTitleAsString", "attributeHtml", "tagContent", "isSelfClosing", "generateTagsAsString", "server_default", "baseTag", "bodyAttributes", "htmlAttributes", "noscriptTags", "styleTags", "prioritizeSeoTags", "linkTags", "metaTags", "scriptTags", "priorityMethods", "meta", "link", "script", "getPriorityMethods", "noscript", "instances", "isDocument", "HelmetData", "canUseDOM", "__publicField", "setHelmet", "serverState", "helmet", "helmetInstances", "instance", "Context", "React2", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "super", "helmetData", "render", "React$1", "updateTags", "headElement", "querySelector", "tagNodes", "oldTags", "newTags", "indexToDelete", "newElement", "existingTag", "isEqualNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "updateAttributes", "elementTag", "helmetAttributeString", "helmetAttributes", "attributesToRemove", "<PERSON><PERSON><PERSON><PERSON>", "indexToSave", "removeAttribute", "commitTagChanges", "newState", "cb", "onChangeClientState", "updateTitle", "tagUpdates", "addedTags", "removedTags", "_helmet<PERSON><PERSON><PERSON>", "client_default", "defer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component2", "shouldComponentUpdate", "nextProps", "shallowEqual", "componentDidUpdate", "emitChange", "componentWillUnmount", "init", "rendered", "<PERSON><PERSON><PERSON>", "Component3", "fastCompare", "mapNestedChildrenToProps", "child", "nested<PERSON><PERSON><PERSON><PERSON>", "flattenArrayTypeChildren", "arrayTypeChildren", "newChildProps", "mapObjectTypeChildren", "newProps", "mapArrayTypeChildrenToProps", "newFlattenedProps", "arrayChildName", "warnOnInvalidChildren", "nested<PERSON><PERSON><PERSON>", "mapChildrenToProps", "React3", "Children", "childProps", "Consumer", "LoadingSpinner", "resources", "translation", "navigation", "profile", "projects", "backlog", "contact", "home", "goToProfile", "goToProjetos", "goToBacklog", "goToContato", "menu", "toggle", "settings", "lightMode", "darkMode", "language", "sound", "enabled", "accessibility", "feedback", "bio", "exploreProjects", "letsChat", "downloadCV", "linkedin", "ixdf", "hero", "greeting", "roles", "uxDesigner", "productDesigner", "designStrategist", "interactionDesigner", "overview", "discovery", "solution", "iteration", "outcomes", "insights", "seeM<PERSON>", "see<PERSON>ess", "projectImage", "badges", "usability", "informationArchitecture", "userTesting", "uxResearch", "journeyMapping", "stakeholderManagement", "productStrategy", "seo", "productValidation", "visualDesign", "communication", "engagement", "fgvLaw", "category", "direitoGV", "taliparts", "tvInstitucional", "note", "noItems", "previous", "challenge", "form", "namePlaceholder", "email", "emailPlaceholder", "subject", "subjectPlaceholder", "messagePlaceholder", "send", "sending", "nameRequired", "emailRequired", "emailInvalid", "subjectRequired", "messageRequired", "messageMinLength", "location", "availability", "social", "whatsapp", "subtitle", "instructions", "menuLabel", "menuTooltip", "increase", "decrease", "reset", "increaseLabel", "decreaseLabel", "reset<PERSON><PERSON><PERSON>", "enable", "disable", "readingMode", "screenReader", "features", "fontSizeIncrease", "fontSizeDecrease", "fontSizeReset", "contrastEnable", "contrastDisable", "readingModeEnable", "readingModeDisable", "skipLinks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skipToNavigation", "fontIncreased", "fontDecreased", "fontReset", "contrastEnabled", "contrastDisabled", "readingEnabled", "readingDisabled", "enabledDesc", "disabledDesc", "changed", "volume", "volumeControl", "light", "dark", "system", "switch", "available", "actions", "collapse", "download", "share", "copy", "print", "backToTop", "tooltips", "messageSent", "settingsSaved", "linkCopied", "themeChanged", "languageChanged", "messageNotSent", "networkError", "genericError", "try<PERSON><PERSON>n", "processing", "saving", "unsavedChanges", "confirmAction", "keywords", "author", "pages", "person", "jobTitle", "skills", "organization", "schema", "photo", "ixdfLogo", "ixdfSeal", "external", "phone", "github", "sun", "moon", "globe", "decorative", "gradient", "pattern", "divider", "background", "alts", "portuguese", "english", "spanish", "copyright", "footer", "typeQuestion", "back", "includeEmail", "privacyPolicy", "problem", "idea", "praise", "problemTitle", "ideaTitle", "praiseTitle", "defaultTitle", "problemInstruction", "ideaInstruction", "praiseInstruction", "defaultInstruction", "problemPlaceholder", "ideaPlaceholder", "praisePlaceholder", "defaultPlaceholder", "validation", "types", "bug", "suggestion", "compliment", "other", "learnMore", "acceptAll", "rejectAll", "managePreferences", "savePreferences", "required", "preferences", "necessary", "analytics", "providers", "marketing", "common", "save", "confirm", "yes", "no", "cookies", "i18n", "use", "LanguageDetector", "initReactI18next", "lng", "fallbackLng", "debug", "useSuspense", "interpolation", "escapeValue", "defaultNS", "detection", "caches", "I18nProvider", "isReady", "setIsReady", "useTranslation", "checkI18nReady", "isInitialized", "hasResourceBundle", "handleLanguageChanged", "on", "off", "_Fragment", "navItems", "href", "User", "sectionId", "i18nKey", "Folder", "Repeat", "Mail", "MobileNavigationMenu", "isOpen", "activeSection", "onNavigate", "h2", "nav", "item", "Header", "scrolled", "setScrolled", "setActiveSection", "feedbackOpen", "setFeedbackOpen", "mobileMenuOpen", "setMobileMenuOpen", "onScroll", "scrollY", "found", "el", "getElementById", "rect", "passive", "handleNavClick", "scrollTo", "offsetTop", "behavior", "handleMobileNavigation", "header", "ul", "Icon", "isActive", "<PERSON><PERSON>", "MessageCircle", "h3", "Index", "lazy", "import", "NotFound", "PrivacyPolicy", "AnalyticsProvider", "__vitePreload", "BackToTop", "FluidGradientBackground", "__VITE_PRELOAD__", "module", "LazyScripts", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "queryClient", "QueryClient", "App", "QueryClientProvider", "Suspense", "fallback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basename", "future", "v7_startTransition", "v7_relativeSplatPath", "Routes", "Route", "element", "initializeCacheOptimizations", "baseUrl", "as", "fetchpriority", "resource", "crossorigin", "onerror", "preloadCriticalResources", "url", "URL", "searchParams", "navigator", "swPath", "registration", "serviceWorker", "register", "registerServiceWorker", "imageObserver", "IntersectionObserver", "entry", "isIntersecting", "img", "src", "srcset", "unobserve", "rootMargin", "createImageObserver", "observe", "nextPageUrls", "requestIdleCallback", "prefetchNextPageResources", "loadImage", "resolve", "reject", "Image", "onload", "initializeImageOptimizations", "preloadCriticalImages", "observer", "options", "retryAttempts", "retry<PERSON><PERSON><PERSON>", "attempt", "loadImageWithFallback", "loadedSrc", "CSPSecurity", "parseJSON", "jsonString", "JSON", "parse", "interpolateTemplate", "template", "variables", "getNestedProperty", "setNestedProperty", "last<PERSON>ey", "pop", "executeSafeOperation", "operation", "safeOperations", "subtract", "multiply", "uppercase", "toUpperCase", "lowercase", "arr", "secureSetTimeout", "secureSetInterval", "setInterval", "secureImport", "modulePath", "isValidModulePath", "sanitizeHTML", "html", "buildSecureURL", "params", "installCSPInterceptors", "eval", "Function", "originalSetTimeout", "originalSetInterval", "generateNonce", "array", "Uint8Array", "crypto", "getRandomValues", "btoa", "fromCharCode", "validateCSPCompliance", "violations", "rootElement", "ReactDOM", "initializeCSPSecurity"], "ignoreList": [1, 3, 4, 5, 6, 11, 14, 15, 16, 17], "sources": ["../../src/react-fix.ts", "../../node_modules/react-dom/client.js", "../../src/hooks/use-toast.ts", "../../node_modules/@radix-ui/react-collection/dist/index.mjs", "../../node_modules/@radix-ui/react-toast/dist/index.mjs", "../../node_modules/class-variance-authority/dist/index.mjs", "../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "../../src/lib/utils.ts", "../../src/components/ui/toast.tsx", "../../src/components/ui/toaster.tsx", "../../src/components/providers/ThemeProvider.tsx", "../../node_modules/sonner/dist/index.mjs", "../../src/components/ui/sonner.tsx", "../../src/components/ui/tooltip.tsx", "../../node_modules/react-fast-compare/index.js", "../../node_modules/invariant/browser.js", "../../node_modules/shallowequal/index.js", "../../node_modules/react-helmet-async/lib/index.esm.js", "../../src/components/LoadingSpinner.tsx", "../../src/i18n/config.ts", "../../src/i18n/locales/pt-BR/index.ts", "../../src/i18n/locales/pt-BR/navigation.ts", "../../src/i18n/locales/pt-BR/profile.ts", "../../src/i18n/locales/pt-BR/projects.ts", "../../src/i18n/locales/pt-BR/backlog.ts", "../../src/i18n/locales/pt-BR/contact.ts", "../../src/i18n/locales/pt-BR/accessibility.ts", "../../src/i18n/locales/pt-BR/sound.ts", "../../src/i18n/locales/pt-BR/tooltips.ts", "../../src/i18n/locales/pt-BR/toasts.ts", "../../src/i18n/locales/pt-BR/seo.ts", "../../src/i18n/locales/pt-BR/schema.ts", "../../src/i18n/locales/pt-BR/alts.ts", "../../src/i18n/locales/pt-BR/language.ts", "../../src/i18n/locales/pt-BR/theme.ts", "../../src/i18n/locales/pt-BR/footer.ts", "../../src/i18n/locales/pt-BR/feedback.ts", "../../src/i18n/locales/pt-BR/cookies.ts", "../../src/i18n/locales/pt-BR/common.ts", "../../src/i18n/locales/en-US/index.ts", "../../src/i18n/locales/en-US/navigation.ts", "../../src/i18n/locales/en-US/profile.ts", "../../src/i18n/locales/en-US/projects.ts", "../../src/i18n/locales/en-US/backlog.ts", "../../src/i18n/locales/en-US/contact.ts", "../../src/i18n/locales/en-US/accessibility.ts", "../../src/i18n/locales/en-US/sound.ts", "../../src/i18n/locales/en-US/tooltips.ts", "../../src/i18n/locales/en-US/toasts.ts", "../../src/i18n/locales/en-US/seo.ts", "../../src/i18n/locales/en-US/schema.ts", "../../src/i18n/locales/en-US/alts.ts", "../../src/i18n/locales/en-US/language.ts", "../../src/i18n/locales/en-US/theme.ts", "../../src/i18n/locales/en-US/footer.ts", "../../src/i18n/locales/en-US/feedback.ts", "../../src/i18n/locales/en-US/cookies.ts", "../../src/i18n/locales/en-US/common.ts", "../../src/i18n/locales/es-ES/index.ts", "../../src/i18n/locales/es-ES/navigation.ts", "../../src/i18n/locales/es-ES/profile.ts", "../../src/i18n/locales/es-ES/projects.ts", "../../src/i18n/locales/es-ES/backlog.ts", "../../src/i18n/locales/es-ES/contact.ts", "../../src/i18n/locales/es-ES/accessibility.ts", "../../src/i18n/locales/es-ES/sound.ts", "../../src/i18n/locales/es-ES/tooltips.ts", "../../src/i18n/locales/es-ES/toasts.ts", "../../src/i18n/locales/es-ES/seo.ts", "../../src/i18n/locales/es-ES/schema.ts", "../../src/i18n/locales/es-ES/alts.ts", "../../src/i18n/locales/es-ES/language.ts", "../../src/i18n/locales/es-ES/theme.ts", "../../src/i18n/locales/es-ES/footer.ts", "../../src/i18n/locales/es-ES/feedback.ts", "../../src/i18n/locales/es-ES/cookies.ts", "../../src/i18n/locales/es-ES/common.ts", "../../src/components/I18nProvider.tsx", "../../src/components/Header.tsx", "../../src/App.tsx", "../../src/utils/cacheOptimization.ts", "../../src/utils/imageOptimization.ts", "../../src/utils/cspSecurity.ts", "../../src/main.tsx"], "sourcesContent": ["// Fix para problemas de React em produção\r\n// Este arquivo garante que o React esteja disponível globalmente\r\n\r\nimport * as React from 'react';\r\n\r\n// Garantir que React está disponível no window para bibliotecas que dependem dele\r\nif (typeof window !== 'undefined') {\r\n  (window as any).React = React;\r\n  (window as any).react = React;\r\n}\r\n\r\n// Garantir que React está disponível no global para Node.js/SSR\r\nif (typeof global !== 'undefined') {\r\n  (global as any).React = React;\r\n  (global as any).react = React;\r\n}\r\n\r\n// Export para garantir que o módulo seja processado\r\nexport default React;\r\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "import * as React from \"react\"\r\n\r\nimport type {\r\n  ToastActionElement,\r\n  ToastProps,\r\n} from \"@/components/ui/toast\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: ToastActionElement\r\n}\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = typeof actionTypes\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t\r\n        ),\r\n      }\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n  }\r\n}\r\n\r\nconst listeners: Array<(state: State) => void> = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: ToasterToast) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  }\r\n}\r\n\r\nexport { useToast, toast }\r\n", "\"use client\";\n\n// src/collection-legacy.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }\n  );\n  const CollectionProvider = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef(null);\n    const itemMap = React.useRef(/* @__PURE__ */ new Map()).current;\n    return /* @__PURE__ */ jsx(CollectionProviderImpl, { scope, itemMap, collectionRef: ref, children });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...itemData });\n        return () => void context.itemMap.delete(ref);\n      });\n      return /* @__PURE__ */ jsx(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope\n  ];\n}\n\n// src/collection.tsx\nimport React2 from \"react\";\nimport { createContextScope as createContextScope2 } from \"@radix-ui/react-context\";\nimport { useComposedRefs as useComposedRefs2 } from \"@radix-ui/react-compose-refs\";\nimport { createSlot as createSlot2 } from \"@radix-ui/react-slot\";\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n  #keys;\n  constructor(entries) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n  set(key, value) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n  insert(index, key, value) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n    if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n    const size = this.size + (has ? 0 : 1);\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n    const keys = [...this.#keys];\n    let nextValue;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i];\n        if (keys[i] === key) {\n          nextKey = keys[i + 1];\n        }\n        if (has) {\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1];\n        const currentValue = nextValue;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n  with(index, key, value) {\n    const copy = new _OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n  before(key) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n  after(key) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n  first() {\n    return this.entryAt(0);\n  }\n  last() {\n    return this.entryAt(-1);\n  }\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n  delete(key) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n  deleteAt(index) {\n    const key = this.keyAt(index);\n    if (key !== void 0) {\n      return this.delete(key);\n    }\n    return false;\n  }\n  at(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return this.get(key);\n    }\n  }\n  entryAt(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return [key, this.get(key)];\n    }\n  }\n  indexOf(key) {\n    return this.#keys.indexOf(key);\n  }\n  keyAt(index) {\n    return at(this.#keys, index);\n  }\n  from(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n  keyFrom(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n  find(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return void 0;\n  }\n  findIndex(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  filter(predicate, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  map(callbackfn, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  reduce(...args) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0);\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n  reduceRight(...args) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1);\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index);\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n  toSorted(compareFn) {\n    const entries = [...this.entries()].sort(compareFn);\n    return new _OrderedDict(entries);\n  }\n  toReversed() {\n    const reversed = new _OrderedDict();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n  toSpliced(...args) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new _OrderedDict(entries);\n  }\n  slice(start, end) {\n    const result = new _OrderedDict();\n    let stop = this.size - 1;\n    if (start === void 0) {\n      return result;\n    }\n    if (start < 0) {\n      start = start + this.size;\n    }\n    if (end !== void 0 && end > 0) {\n      stop = end - 1;\n    }\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      result.set(key, element);\n    }\n    return result;\n  }\n  every(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  some(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n};\nfunction at(array, index) {\n  if (\"at\" in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n\n// src/collection.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction createCollection2(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope2(PROVIDER_NAME);\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0\n    }\n  );\n  const CollectionProvider = ({ state, ...props }) => {\n    return state ? /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state }) : /* @__PURE__ */ jsx2(CollectionInit, { ...props });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const CollectionInit = (props) => {\n    const state = useInitCollection();\n    return /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state });\n  };\n  CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n  const CollectionProviderImpl = (props) => {\n    const { scope, children, state } = props;\n    const ref = React2.useRef(null);\n    const [collectionElement, setCollectionElement] = React2.useState(\n      null\n    );\n    const composeRefs = useComposedRefs2(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n    React2.useEffect(() => {\n      if (!collectionElement) return;\n      const observer = getChildListObserver(() => {\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n    return /* @__PURE__ */ jsx2(\n      CollectionContextProvider,\n      {\n        scope,\n        itemMap,\n        setItemMap,\n        collectionRef: composeRefs,\n        collectionRefObject: ref,\n        collectionElement,\n        children\n      }\n    );\n  };\n  CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot2(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs2(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx2(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot2(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React2.useRef(null);\n      const [element, setElement] = React2.useState(null);\n      const composedRefs = useComposedRefs2(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      const { setItemMap } = context;\n      const itemDataRef = React2.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n      React2.useEffect(() => {\n        const itemData2 = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n          if (!map.has(element)) {\n            map.set(element, { ...itemData2, element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n          return map.set(element, { ...itemData2, element }).toSorted(sortByDocumentPosition);\n        });\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n      return /* @__PURE__ */ jsx2(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useInitCollection() {\n    return React2.useState(new OrderedDict());\n  }\n  function useCollection(scope) {\n    const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n    return itemMap;\n  }\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection\n  };\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions\n  ];\n}\nfunction shallowEqual(a, b) {\n  if (a === b) return true;\n  if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\nfunction isElementPreceding(a, b) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n  return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === \"childList\") {\n        callback();\n        return;\n      }\n    }\n  });\n  return observer;\n}\nexport {\n  createCollection,\n  createCollection2 as unstable_createCollection\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/toast.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport * as DismissableLayer from \"@radix-ui/react-dismissable-layer\";\nimport { Portal } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(\"Toast\");\nvar [createToastContext, createToastScope] = createContextScope(\"Toast\", [createCollectionScope]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props) => {\n  const {\n    __scopeToast,\n    label = \"Notification\",\n    duration = 5e3,\n    swipeDirection = \"right\",\n    swipeThreshold = 50,\n    children\n  } = props;\n  const [viewport, setViewport] = React.useState(null);\n  const [toastCount, setToastCount] = React.useState(0);\n  const isFocusedToastEscapeKeyDownRef = React.useRef(false);\n  const isClosePausedRef = React.useRef(false);\n  if (!label.trim()) {\n    console.error(\n      `Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`\n    );\n  }\n  return /* @__PURE__ */ jsx(Collection.Provider, { scope: __scopeToast, children: /* @__PURE__ */ jsx(\n    ToastProviderProvider,\n    {\n      scope: __scopeToast,\n      label,\n      duration,\n      swipeDirection,\n      swipeThreshold,\n      toastCount,\n      viewport,\n      onViewportChange: setViewport,\n      onToastAdd: React.useCallback(() => setToastCount((prevCount) => prevCount + 1), []),\n      onToastRemove: React.useCallback(() => setToastCount((prevCount) => prevCount - 1), []),\n      isFocusedToastEscapeKeyDownRef,\n      isClosePausedRef,\n      children\n    }\n  ) });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\"F8\"];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeToast,\n      hotkey = VIEWPORT_DEFAULT_HOTKEY,\n      label = \"Notifications ({hotkey})\",\n      ...viewportProps\n    } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = React.useRef(null);\n    const headFocusProxyRef = React.useRef(null);\n    const tailFocusProxyRef = React.useRef(null);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    React.useEffect(() => {\n      const handleKeyDown = (event) => {\n        const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key) => event[key] || event.code === key);\n        if (isHotkeyPressed) ref.current?.focus();\n      };\n      document.addEventListener(\"keydown\", handleKeyDown);\n      return () => document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [hotkey]);\n    React.useEffect(() => {\n      const wrapper = wrapperRef.current;\n      const viewport = ref.current;\n      if (hasToasts && wrapper && viewport) {\n        const handlePause = () => {\n          if (!context.isClosePausedRef.current) {\n            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n            viewport.dispatchEvent(pauseEvent);\n            context.isClosePausedRef.current = true;\n          }\n        };\n        const handleResume = () => {\n          if (context.isClosePausedRef.current) {\n            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n            viewport.dispatchEvent(resumeEvent);\n            context.isClosePausedRef.current = false;\n          }\n        };\n        const handleFocusOutResume = (event) => {\n          const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n          if (isFocusMovingOutside) handleResume();\n        };\n        const handlePointerLeaveResume = () => {\n          const isFocusInside = wrapper.contains(document.activeElement);\n          if (!isFocusInside) handleResume();\n        };\n        wrapper.addEventListener(\"focusin\", handlePause);\n        wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n        wrapper.addEventListener(\"pointermove\", handlePause);\n        wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n        window.addEventListener(\"blur\", handlePause);\n        window.addEventListener(\"focus\", handleResume);\n        return () => {\n          wrapper.removeEventListener(\"focusin\", handlePause);\n          wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n          wrapper.removeEventListener(\"pointermove\", handlePause);\n          wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n          window.removeEventListener(\"blur\", handlePause);\n          window.removeEventListener(\"focus\", handleResume);\n        };\n      }\n    }, [hasToasts, context.isClosePausedRef]);\n    const getSortedTabbableCandidates = React.useCallback(\n      ({ tabbingDirection }) => {\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem) => {\n          const toastNode = toastItem.ref.current;\n          const toastTabbableCandidates = [toastNode, ...getTabbableCandidates(toastNode)];\n          return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n      },\n      [getItems]\n    );\n    React.useEffect(() => {\n      const viewport = ref.current;\n      if (viewport) {\n        const handleKeyDown = (event) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === \"Tab\" && !isMetaKey;\n          if (isTabKey) {\n            const focusedElement = document.activeElement;\n            const isTabbingBackwards = event.shiftKey;\n            const targetIsViewport = event.target === viewport;\n            if (targetIsViewport && isTabbingBackwards) {\n              headFocusProxyRef.current?.focus();\n              return;\n            }\n            const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n            const sortedCandidates = getSortedTabbableCandidates({ tabbingDirection });\n            const index = sortedCandidates.findIndex((candidate) => candidate === focusedElement);\n            if (focusFirst(sortedCandidates.slice(index + 1))) {\n              event.preventDefault();\n            } else {\n              isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n            }\n          }\n        };\n        viewport.addEventListener(\"keydown\", handleKeyDown);\n        return () => viewport.removeEventListener(\"keydown\", handleKeyDown);\n      }\n    }, [getItems, getSortedTabbableCandidates]);\n    return /* @__PURE__ */ jsxs(\n      DismissableLayer.Branch,\n      {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: { pointerEvents: hasToasts ? void 0 : \"none\" },\n        children: [\n          hasToasts && /* @__PURE__ */ jsx(\n            FocusProxy,\n            {\n              ref: headFocusProxyRef,\n              onFocusFromOutsideViewport: () => {\n                const tabbableCandidates = getSortedTabbableCandidates({\n                  tabbingDirection: \"forwards\"\n                });\n                focusFirst(tabbableCandidates);\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeToast, children: /* @__PURE__ */ jsx(Primitive.ol, { tabIndex: -1, ...viewportProps, ref: composedRefs }) }),\n          hasToasts && /* @__PURE__ */ jsx(\n            FocusProxy,\n            {\n              ref: tailFocusProxyRef,\n              onFocusFromOutsideViewport: () => {\n                const tabbableCandidates = getSortedTabbableCandidates({\n                  tabbingDirection: \"backwards\"\n                });\n                focusFirst(tabbableCandidates);\n              }\n            }\n          )\n        ]\n      }\n    );\n  }\n);\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ jsx(\n      VisuallyHidden,\n      {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: { position: \"fixed\" },\n        onFocus: (event) => {\n          const prevFocusedElement = event.relatedTarget;\n          const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n          if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n      }\n    );\n  }\n);\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = React.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? true,\n      onChange: onOpenChange,\n      caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || open, children: /* @__PURE__ */ jsx(\n      ToastImpl,\n      {\n        open,\n        ...toastProps,\n        ref: forwardedRef,\n        onClose: () => setOpen(false),\n        onPause: useCallbackRef(props.onPause),\n        onResume: useCallbackRef(props.onResume),\n        onSwipeStart: composeEventHandlers(props.onSwipeStart, (event) => {\n          event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n        }),\n        onSwipeMove: composeEventHandlers(props.onSwipeMove, (event) => {\n          const { x, y } = event.detail.delta;\n          event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n        }),\n        onSwipeCancel: composeEventHandlers(props.onSwipeCancel, (event) => {\n          event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n        }),\n        onSwipeEnd: composeEventHandlers(props.onSwipeEnd, (event) => {\n          const { x, y } = event.detail.delta;\n          event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n          setOpen(false);\n        })\n      }\n    ) });\n  }\n);\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n  onClose() {\n  }\n});\nvar ToastImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeToast,\n      type = \"foreground\",\n      duration: durationProp,\n      open,\n      onClose,\n      onEscapeKeyDown,\n      onPause,\n      onResume,\n      onSwipeStart,\n      onSwipeMove,\n      onSwipeCancel,\n      onSwipeEnd,\n      ...toastProps\n    } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const pointerStartRef = React.useRef(null);\n    const swipeDeltaRef = React.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = React.useRef(0);\n    const closeTimerRemainingTimeRef = React.useRef(duration);\n    const closeTimerRef = React.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = useCallbackRef(() => {\n      const isFocusInToast = node?.contains(document.activeElement);\n      if (isFocusInToast) context.viewport?.focus();\n      onClose();\n    });\n    const startTimer = React.useCallback(\n      (duration2) => {\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = (/* @__PURE__ */ new Date()).getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n      },\n      [handleClose]\n    );\n    React.useEffect(() => {\n      const viewport = context.viewport;\n      if (viewport) {\n        const handleResume = () => {\n          startTimer(closeTimerRemainingTimeRef.current);\n          onResume?.();\n        };\n        const handlePause = () => {\n          const elapsedTime = (/* @__PURE__ */ new Date()).getTime() - closeTimerStartTimeRef.current;\n          closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n          window.clearTimeout(closeTimerRef.current);\n          onPause?.();\n        };\n        viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n        viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n        return () => {\n          viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n          viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n        };\n      }\n    }, [context.viewport, duration, onPause, onResume, startTimer]);\n    React.useEffect(() => {\n      if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [open, duration, context.isClosePausedRef, startTimer]);\n    React.useEffect(() => {\n      onToastAdd();\n      return () => onToastRemove();\n    }, [onToastAdd, onToastRemove]);\n    const announceTextContent = React.useMemo(() => {\n      return node ? getAnnounceTextContent(node) : null;\n    }, [node]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      announceTextContent && /* @__PURE__ */ jsx(\n        ToastAnnounce,\n        {\n          __scopeToast,\n          role: \"status\",\n          \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n          \"aria-atomic\": true,\n          children: announceTextContent\n        }\n      ),\n      /* @__PURE__ */ jsx(ToastInteractiveProvider, { scope: __scopeToast, onClose: handleClose, children: ReactDOM.createPortal(\n        /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: __scopeToast, children: /* @__PURE__ */ jsx(\n          DismissableLayer.Root,\n          {\n            asChild: true,\n            onEscapeKeyDown: composeEventHandlers(onEscapeKeyDown, () => {\n              if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n              context.isFocusedToastEscapeKeyDownRef.current = false;\n            }),\n            children: /* @__PURE__ */ jsx(\n              Primitive.li,\n              {\n                role: \"status\",\n                \"aria-live\": \"off\",\n                \"aria-atomic\": true,\n                tabIndex: 0,\n                \"data-state\": open ? \"open\" : \"closed\",\n                \"data-swipe-direction\": context.swipeDirection,\n                ...toastProps,\n                ref: composedRefs,\n                style: { userSelect: \"none\", touchAction: \"none\", ...props.style },\n                onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n                  if (event.key !== \"Escape\") return;\n                  onEscapeKeyDown?.(event.nativeEvent);\n                  if (!event.nativeEvent.defaultPrevented) {\n                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                    handleClose();\n                  }\n                }),\n                onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n                  if (event.button !== 0) return;\n                  pointerStartRef.current = { x: event.clientX, y: event.clientY };\n                }),\n                onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n                  if (!pointerStartRef.current) return;\n                  const x = event.clientX - pointerStartRef.current.x;\n                  const y = event.clientY - pointerStartRef.current.y;\n                  const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                  const isHorizontalSwipe = [\"left\", \"right\"].includes(context.swipeDirection);\n                  const clamp = [\"left\", \"up\"].includes(context.swipeDirection) ? Math.min : Math.max;\n                  const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                  const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                  const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                  const delta = { x: clampedX, y: clampedY };\n                  const eventDetail = { originalEvent: event, delta };\n                  if (hasSwipeMoveStarted) {\n                    swipeDeltaRef.current = delta;\n                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                      discrete: false\n                    });\n                  } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                    swipeDeltaRef.current = delta;\n                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                      discrete: false\n                    });\n                    event.target.setPointerCapture(event.pointerId);\n                  } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                    pointerStartRef.current = null;\n                  }\n                }),\n                onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n                  const delta = swipeDeltaRef.current;\n                  const target = event.target;\n                  if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                  }\n                  swipeDeltaRef.current = null;\n                  pointerStartRef.current = null;\n                  if (delta) {\n                    const toast = event.currentTarget;\n                    const eventDetail = { originalEvent: event, delta };\n                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                        discrete: true\n                      });\n                    } else {\n                      handleAndDispatchCustomEvent(\n                        TOAST_SWIPE_CANCEL,\n                        onSwipeCancel,\n                        eventDetail,\n                        {\n                          discrete: true\n                        }\n                      );\n                    }\n                    toast.addEventListener(\"click\", (event2) => event2.preventDefault(), {\n                      once: true\n                    });\n                  }\n                })\n              }\n            )\n          }\n        ) }),\n        context.viewport\n      ) })\n    ] });\n  }\n);\nvar ToastAnnounce = (props) => {\n  const { __scopeToast, children, ...announceProps } = props;\n  const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n  const [renderAnnounceText, setRenderAnnounceText] = React.useState(false);\n  const [isAnnounced, setIsAnnounced] = React.useState(false);\n  useNextFrame(() => setRenderAnnounceText(true));\n  React.useEffect(() => {\n    const timer = window.setTimeout(() => setIsAnnounced(true), 1e3);\n    return () => window.clearTimeout(timer);\n  }, []);\n  return isAnnounced ? null : /* @__PURE__ */ jsx(Portal, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { ...announceProps, children: renderAnnounceText && /* @__PURE__ */ jsxs(Fragment, { children: [\n    context.label,\n    \" \",\n    children\n  ] }) }) });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...titleProps, ref: forwardedRef });\n  }\n);\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...descriptionProps, ref: forwardedRef });\n  }\n);\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = React.forwardRef(\n  (props, forwardedRef) => {\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n      console.error(\n        `Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`\n      );\n      return null;\n    }\n    return /* @__PURE__ */ jsx(ToastAnnounceExclude, { altText, asChild: true, children: /* @__PURE__ */ jsx(ToastClose, { ...actionProps, ref: forwardedRef }) });\n  }\n);\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ jsx(ToastAnnounceExclude, { asChild: true, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, interactiveContext.onClose)\n      }\n    ) });\n  }\n);\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = React.forwardRef((props, forwardedRef) => {\n  const { __scopeToast, altText, ...announceExcludeProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-radix-toast-announce-exclude\": \"\",\n      \"data-radix-toast-announce-alt\": altText || void 0,\n      ...announceExcludeProps,\n      ref: forwardedRef\n    }\n  );\n});\nfunction getAnnounceTextContent(container) {\n  const textContent = [];\n  const childNodes = Array.from(container.childNodes);\n  childNodes.forEach((node) => {\n    if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n    if (isHTMLElement(node)) {\n      const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n      const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n      if (!isHidden) {\n        if (isExcluded) {\n          const altText = node.dataset.radixToastAnnounceAlt;\n          if (altText) textContent.push(altText);\n        } else {\n          textContent.push(...getAnnounceTextContent(node));\n        }\n      }\n    }\n  });\n  return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const currentTarget = detail.originalEvent.currentTarget;\n  const event = new CustomEvent(name, { bubbles: true, cancelable: true, detail });\n  if (handler) currentTarget.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(currentTarget, event);\n  } else {\n    currentTarget.dispatchEvent(event);\n  }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0) => {\n  const deltaX = Math.abs(delta.x);\n  const deltaY = Math.abs(delta.y);\n  const isDeltaX = deltaX > deltaY;\n  if (direction === \"left\" || direction === \"right\") {\n    return isDeltaX && deltaX > threshold;\n  } else {\n    return !isDeltaX && deltaY > threshold;\n  }\n};\nfunction useNextFrame(callback = () => {\n}) {\n  const fn = useCallbackRef(callback);\n  useLayoutEffect(() => {\n    let raf1 = 0;\n    let raf2 = 0;\n    raf1 = window.requestAnimationFrame(() => raf2 = window.requestAnimationFrame(fn));\n    return () => {\n      window.cancelAnimationFrame(raf1);\n      window.cancelAnimationFrame(raf2);\n    };\n  }, [fn]);\n}\nfunction isHTMLElement(node) {\n  return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction focusFirst(candidates) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\nexport {\n  Action,\n  Close,\n  Description,\n  Provider,\n  Root2 as Root,\n  Title,\n  Toast,\n  ToastAction,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n  Viewport,\n  createToastScope\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n", "const CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    prefix\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  const prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n  prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n    processClassesRecursively(classGroup, classMap, classGroupId, theme);\n  });\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\nconst getPrefixedClassGroupEntries = (classGroupEntries, prefix) => {\n  if (!prefix) {\n    return classGroupEntries;\n  }\n  return classGroupEntries.map(([classGroupId, classGroup]) => {\n    const prefixedClassGroup = classGroup.map(classDefinition => {\n      if (typeof classDefinition === 'string') {\n        return prefix + classDefinition;\n      }\n      if (typeof classDefinition === 'object') {\n        return Object.fromEntries(Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]));\n      }\n      return classDefinition;\n    });\n    return [classGroupId, prefixedClassGroup];\n  });\n};\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst createParseClassName = config => {\n  const {\n    separator,\n    experimentalParseClassName\n  } = config;\n  const isSeparatorSingleCharacter = separator.length === 1;\n  const firstSeparatorCharacter = separator[0];\n  const separatorLength = separator.length;\n  // parseClassName inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n  const parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0) {\n        if (currentCharacter === firstSeparatorCharacter && (isSeparatorSingleCharacter || className.slice(index, index + separatorLength) === separator)) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + separatorLength;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n    const baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (experimentalParseClassName) {\n    return className => experimentalParseClassName({\n      className,\n      parseClassName\n    });\n  }\n  return parseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst sortModifiers = modifiers => {\n  if (modifiers.length <= 1) {\n    return modifiers;\n  }\n  const sortedModifiers = [];\n  let unsortedModifiers = [];\n  modifiers.forEach(modifier => {\n    const isArbitraryVariant = modifier[0] === '[';\n    if (isArbitraryVariant) {\n      sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n      unsortedModifiers = [];\n    } else {\n      unsortedModifiers.push(modifier);\n    }\n  });\n  sortedModifiers.push(...unsortedModifiers.sort());\n  return sortedModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    let hasPostfixModifier = Boolean(maybePostfixModifierPosition);\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst stringLengths = /*#__PURE__*/new Set(['px', 'full', 'screen']);\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isLength = value => isNumber(value) || stringLengths.has(value) || fractionRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, 'length', isLengthOnly);\nconst isNumber = value => Boolean(value) && !Number.isNaN(Number(value));\nconst isArbitraryNumber = value => getIsArbitraryValue(value, 'number', isNumber);\nconst isInteger = value => Boolean(value) && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst sizeLabels = /*#__PURE__*/new Set(['length', 'size', 'percentage']);\nconst isArbitrarySize = value => getIsArbitraryValue(value, sizeLabels, isNever);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, 'position', isNever);\nconst imageLabels = /*#__PURE__*/new Set(['image', 'url']);\nconst isArbitraryImage = value => getIsArbitraryValue(value, imageLabels, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, '', isShadow);\nconst isAny = () => true;\nconst getIsArbitraryValue = (value, label, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return typeof label === 'string' ? result[1] === label : label.has(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isInteger,\n  isLength,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  const colors = fromTheme('colors');\n  const spacing = fromTheme('spacing');\n  const blur = fromTheme('blur');\n  const brightness = fromTheme('brightness');\n  const borderColor = fromTheme('borderColor');\n  const borderRadius = fromTheme('borderRadius');\n  const borderSpacing = fromTheme('borderSpacing');\n  const borderWidth = fromTheme('borderWidth');\n  const contrast = fromTheme('contrast');\n  const grayscale = fromTheme('grayscale');\n  const hueRotate = fromTheme('hueRotate');\n  const invert = fromTheme('invert');\n  const gap = fromTheme('gap');\n  const gradientColorStops = fromTheme('gradientColorStops');\n  const gradientColorStopPositions = fromTheme('gradientColorStopPositions');\n  const inset = fromTheme('inset');\n  const margin = fromTheme('margin');\n  const opacity = fromTheme('opacity');\n  const padding = fromTheme('padding');\n  const saturate = fromTheme('saturate');\n  const scale = fromTheme('scale');\n  const sepia = fromTheme('sepia');\n  const skew = fromTheme('skew');\n  const space = fromTheme('space');\n  const translate = fromTheme('translate');\n  const getOverscroll = () => ['auto', 'contain', 'none'];\n  const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing];\n  const getSpacingWithArbitrary = () => [isArbitraryValue, spacing];\n  const getLengthWithEmptyAndArbitrary = () => ['', isLength, isArbitraryLength];\n  const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue];\n  const getPositions = () => ['bottom', 'center', 'left', 'left-bottom', 'left-top', 'right', 'right-bottom', 'right-top', 'top'];\n  const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'];\n  const getBlendModes = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const getAlign = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'];\n  const getZeroAndEmpty = () => ['', '0', isArbitraryValue];\n  const getBreaks = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const getNumberAndArbitrary = () => [isNumber, isArbitraryValue];\n  return {\n    cacheSize: 500,\n    separator: ':',\n    theme: {\n      colors: [isAny],\n      spacing: [isLength, isArbitraryLength],\n      blur: ['none', '', isTshirtSize, isArbitraryValue],\n      brightness: getNumberAndArbitrary(),\n      borderColor: [colors],\n      borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n      borderSpacing: getSpacingWithArbitrary(),\n      borderWidth: getLengthWithEmptyAndArbitrary(),\n      contrast: getNumberAndArbitrary(),\n      grayscale: getZeroAndEmpty(),\n      hueRotate: getNumberAndArbitrary(),\n      invert: getZeroAndEmpty(),\n      gap: getSpacingWithArbitrary(),\n      gradientColorStops: [colors],\n      gradientColorStopPositions: [isPercent, isArbitraryLength],\n      inset: getSpacingWithAutoAndArbitrary(),\n      margin: getSpacingWithAutoAndArbitrary(),\n      opacity: getNumberAndArbitrary(),\n      padding: getSpacingWithArbitrary(),\n      saturate: getNumberAndArbitrary(),\n      scale: getNumberAndArbitrary(),\n      sepia: getZeroAndEmpty(),\n      skew: getNumberAndArbitrary(),\n      space: getSpacingWithArbitrary(),\n      translate: getSpacingWithArbitrary()\n    },\n    classGroups: {\n      // Layout\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', 'video', isArbitraryValue]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isTshirtSize]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': getBreaks()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': getBreaks()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: [...getPositions(), isArbitraryValue]\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: getOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': getOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': getOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': getOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: [inset]\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': [inset]\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': [inset]\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: [inset]\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: [inset]\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: [inset]\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: [inset]\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: [inset]\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: [inset]\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: ['auto', isInteger, isArbitraryValue]\n      }],\n      // Flexbox and Grid\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: getSpacingWithAutoAndArbitrary()\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['wrap', 'wrap-reverse', 'nowrap']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: ['1', 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: getZeroAndEmpty()\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: getZeroAndEmpty()\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: ['first', 'last', 'none', isInteger, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': [isAny]\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: ['auto', {\n          span: ['full', isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': [isAny]\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: ['auto', {\n          span: [isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: [gap]\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': [gap]\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': [gap]\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: ['normal', ...getAlign()]\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': ['start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...getAlign(), 'baseline']\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline']\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': [...getAlign(), 'baseline']\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: [padding]\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: [padding]\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: [padding]\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: [padding]\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: [padding]\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: [padding]\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: [padding]\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: [padding]\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: [padding]\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: [margin]\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: [margin]\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: [margin]\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: [margin]\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: [margin]\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: [margin]\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: [margin]\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: [margin]\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: [margin]\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x': [{\n        'space-x': [space]\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y': [{\n        'space-y': [space]\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // Sizing\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: ['auto', 'min', 'max', 'fit', 'svw', 'lvw', 'dvw', isArbitraryValue, spacing]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [isArbitraryValue, spacing, 'min', 'max', 'fit']\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [isArbitraryValue, spacing, 'none', 'full', 'min', 'max', 'fit', 'prose', {\n          screen: [isTshirtSize]\n        }, isTshirtSize]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/size\n       */\n      size: [{\n        size: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit']\n      }],\n      // Typography\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', isTshirtSize, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black', isArbitraryNumber]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isAny]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest', isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': ['none', isNumber, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose', isLength, isArbitraryValue]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryValue]\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['none', 'disc', 'decimal', isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: [colors]\n      }],\n      /**\n       * Placeholder Opacity\n       * @see https://tailwindcss.com/docs/placeholder-opacity\n       */\n      'placeholder-opacity': [{\n        'placeholder-opacity': [opacity]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: [colors]\n      }],\n      /**\n       * Text Opacity\n       * @see https://tailwindcss.com/docs/text-opacity\n       */\n      'text-opacity': [{\n        'text-opacity': [opacity]\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...getLineStyles(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: ['auto', 'from-font', isLength, isArbitraryLength]\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': ['auto', isLength, isArbitraryValue]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: [colors]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: getSpacingWithArbitrary()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryValue]\n      }],\n      // Backgrounds\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Opacity\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/background-opacity\n       */\n      'bg-opacity': [{\n        'bg-opacity': [opacity]\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: [...getPositions(), isArbitraryPosition]\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: ['no-repeat', {\n          repeat: ['', 'x', 'y', 'round', 'space']\n        }]\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: ['auto', 'cover', 'contain', isArbitrarySize]\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n        }, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: [colors]\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: [gradientColorStops]\n      }],\n      // Borders\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: [borderRadius]\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': [borderRadius]\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': [borderRadius]\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': [borderRadius]\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': [borderRadius]\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': [borderRadius]\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': [borderRadius]\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': [borderRadius]\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': [borderRadius]\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': [borderRadius]\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: [borderWidth]\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': [borderWidth]\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': [borderWidth]\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': [borderWidth]\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': [borderWidth]\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': [borderWidth]\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': [borderWidth]\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': [borderWidth]\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': [borderWidth]\n      }],\n      /**\n       * Border Opacity\n       * @see https://tailwindcss.com/docs/border-opacity\n       */\n      'border-opacity': [{\n        'border-opacity': [opacity]\n      }],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...getLineStyles(), 'hidden']\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x': [{\n        'divide-x': [borderWidth]\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y': [{\n        'divide-y': [borderWidth]\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Divide Opacity\n       * @see https://tailwindcss.com/docs/divide-opacity\n       */\n      'divide-opacity': [{\n        'divide-opacity': [opacity]\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/divide-style\n       */\n      'divide-style': [{\n        divide: getLineStyles()\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: [borderColor]\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': [borderColor]\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': [borderColor]\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': [borderColor]\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': [borderColor]\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': [borderColor]\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': [borderColor]\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': [borderColor]\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': [borderColor]\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: [borderColor]\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: ['', ...getLineStyles()]\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isLength, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: [isLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: [colors]\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w': [{\n        ring: getLengthWithEmptyAndArbitrary()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/ring-color\n       */\n      'ring-color': [{\n        ring: [colors]\n      }],\n      /**\n       * Ring Opacity\n       * @see https://tailwindcss.com/docs/ring-opacity\n       */\n      'ring-opacity': [{\n        'ring-opacity': [opacity]\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://tailwindcss.com/docs/ring-offset-width\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isLength, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://tailwindcss.com/docs/ring-offset-color\n       */\n      'ring-offset-color': [{\n        'ring-offset': [colors]\n      }],\n      // Effects\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow-color\n       */\n      'shadow-color': [{\n        shadow: [isAny]\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [opacity]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...getBlendModes(), 'plus-lighter', 'plus-darker']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': getBlendModes()\n      }],\n      // Filters\n      /**\n       * Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: ['', 'none']\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: [blur]\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [brightness]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [contrast]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue]\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: [grayscale]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [hueRotate]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: [invert]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [saturate]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: [sepia]\n      }],\n      /**\n       * Backdrop Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': ['', 'none']\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': [blur]\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [brightness]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [contrast]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': [grayscale]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [hueRotate]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': [invert]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [opacity]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [saturate]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': [sepia]\n      }],\n      // Tables\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': [borderSpacing]\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': [borderSpacing]\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': [borderSpacing]\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // Transitions and Animation\n      /**\n       * Tranisition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['none', 'all', '', 'colors', 'opacity', 'shadow', 'transform', isArbitraryValue]\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: getNumberAndArbitrary()\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: getNumberAndArbitrary()\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue]\n      }],\n      // Transforms\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: ['', 'gpu', 'none']\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: [scale]\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': [scale]\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': [scale]\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: [isInteger, isArbitraryValue]\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': [translate]\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': [translate]\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': [skew]\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': [skew]\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: ['center', 'top', 'top-right', 'right', 'bottom-right', 'bottom', 'bottom-left', 'left', 'top-left', isArbitraryValue]\n      }],\n      // Interactivity\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: ['auto', colors]\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryValue]\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: [colors]\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['none', 'auto']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', 'y', 'x', '']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue]\n      }],\n      // SVG\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: [colors, 'none']\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: [colors, 'none']\n      }],\n      // Accessibility\n      /**\n       * Screen Readers\n       * @see https://tailwindcss.com/docs/screen-readers\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    }\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  separator,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'separator', separator);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  for (const configKey in override) {\n    overrideConfigProperties(baseConfig[configKey], override[configKey]);\n  }\n  for (const key in extend) {\n    mergeConfigProperties(baseConfig[key], extend[key]);\n  }\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      const mergeValue = mergeObject[key];\n      if (mergeValue !== undefined) {\n        baseObject[key] = (baseObject[key] || []).concat(mergeValue);\n      }\n    }\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\nexport { createTailwindMerge, extendTailwindMerge, fromTheme, getDefaultConfig, mergeConfigs, twJoin, twMerge, validators };\n//# sourceMappingURL=bundle-mjs.mjs.map\n", "import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n", "import * as React from \"react\"\r\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ToastProvider = ToastPrimitives.Provider\r\n\r\nconst ToastViewport = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\r\n\r\nconst toastVariants = cva(\r\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border bg-background text-foreground\",\r\n        destructive:\r\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Toast = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\r\n    VariantProps<typeof toastVariants>\r\n>(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    <ToastPrimitives.Root\r\n      ref={ref}\r\n      className={cn(toastVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nToast.displayName = ToastPrimitives.Root.displayName\r\n\r\nconst ToastAction = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Action>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Action\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastAction.displayName = ToastPrimitives.Action.displayName\r\n\r\nconst ToastClose = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Close>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\r\n      className\r\n    )}\r\n    toast-close=\"\"\r\n    {...props}\r\n  >\r\n    <X className=\"h-4 w-4\" />\r\n  </ToastPrimitives.Close>\r\n))\r\nToastClose.displayName = ToastPrimitives.Close.displayName\r\n\r\nconst ToastTitle = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Title>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    ref={ref}\r\n    className={cn(\"text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastTitle.displayName = ToastPrimitives.Title.displayName\r\n\r\nconst ToastDescription = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Description>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm opacity-90\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastDescription.displayName = ToastPrimitives.Description.displayName\r\n\r\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\r\n\r\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\r\n\r\nexport {\r\n  type ToastProps,\r\n  type ToastActionElement,\r\n  ToastProvider,\r\n  ToastViewport,\r\n  Toast,\r\n  ToastTitle,\r\n  ToastDescription,\r\n  ToastClose,\r\n  ToastAction,\r\n}\r\n", "import { useToast } from \"@/hooks/use-toast\"\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n} from \"@/components/ui/toast\"\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast()\r\n\r\n  return (\r\n    <ToastProvider>\r\n      {toasts.map(function ({ id, title, description, action, ...props }) {\r\n        return (\r\n          <Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action}\r\n            <ToastClose />\r\n          </Toast>\r\n        )\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>\r\n  )\r\n}\r\n", "import React, { createContext, useContext, useEffect, useState } from 'react';\r\n\r\ntype Theme = 'light' | 'dark' | 'system';\r\n\r\ninterface ThemeContextType {\r\n  theme: Theme;\r\n  setTheme: (theme: Theme) => void;\r\n  resolvedTheme: 'light' | 'dark';\r\n  isLoading: boolean;\r\n}\r\n\r\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\r\n\r\nexport const useTheme = () => {\r\n  const context = useContext(ThemeContext);\r\n  if (!context) {\r\n    throw new Error('useTheme must be used within a ThemeProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface ThemeProviderProps {\r\n  children: React.ReactNode;\r\n  defaultTheme?: Theme;\r\n  storageKey?: string;\r\n}\r\n\r\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({\r\n  children,\r\n  defaultTheme = 'system',\r\n  storageKey = 'portfolio-theme',\r\n}) => {\r\n  const [theme, setThemeState] = useState<Theme>(defaultTheme);\r\n  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Detecta preferência do sistema\r\n  const getSystemTheme = (): 'light' | 'dark' => {\r\n    if (typeof window !== 'undefined') {\r\n      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\r\n    }\r\n    return 'light';\r\n  };\r\n\r\n  // Resolve o tema atual\r\n  const resolveTheme = (currentTheme: Theme): 'light' | 'dark' => {\r\n    if (currentTheme === 'system') {\r\n      return getSystemTheme();\r\n    }\r\n    return currentTheme;\r\n  };\r\n\r\n  // Aplica o tema no DOM\r\n  const applyTheme = (resolvedTheme: 'light' | 'dark') => {\r\n    const root = document.documentElement;\r\n\r\n    // Remove classes anteriores\r\n    root.classList.remove('light', 'dark');\r\n\r\n    // Adiciona a nova classe\r\n    root.classList.add(resolvedTheme);\r\n\r\n    // Atualiza atributo para compatibilidade\r\n    root.setAttribute('data-theme', resolvedTheme);\r\n  };\r\n\r\n  // Função para alterar tema\r\n  const setTheme = (newTheme: Theme) => {\r\n    setThemeState(newTheme);\r\n\r\n    // Salva no localStorage\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem(storageKey, newTheme);\r\n    }\r\n\r\n    // Resolve e aplica o tema\r\n    const resolved = resolveTheme(newTheme);\r\n    setResolvedTheme(resolved);\r\n    applyTheme(resolved);\r\n  };\r\n\r\n  // Inicialização\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      // Carrega tema do localStorage\r\n      const savedTheme = localStorage.getItem(storageKey) as Theme;\r\n      const initialTheme = savedTheme && ['light', 'dark', 'system'].includes(savedTheme)\r\n        ? savedTheme\r\n        : defaultTheme;\r\n\r\n      setThemeState(initialTheme);\r\n\r\n      // Resolve e aplica o tema inicial\r\n      const resolved = resolveTheme(initialTheme);\r\n      setResolvedTheme(resolved);\r\n      applyTheme(resolved);\r\n\r\n      setIsLoading(false);\r\n    }\r\n  }, [defaultTheme, storageKey]);\r\n\r\n  // Escuta mudanças na preferência do sistema\r\n  useEffect(() => {\r\n    if (typeof window === 'undefined') return;\r\n\r\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n\r\n    const handleChange = () => {\r\n      if (theme === 'system') {\r\n        const resolved = getSystemTheme();\r\n        setResolvedTheme(resolved);\r\n        applyTheme(resolved);\r\n      }\r\n    };\r\n\r\n    mediaQuery.addEventListener('change', handleChange);\r\n    return () => mediaQuery.removeEventListener('change', handleChange);\r\n  }, [theme]);\r\n\r\n  const value: ThemeContextType = {\r\n    theme,\r\n    setTheme,\r\n    resolvedTheme,\r\n    isLoading,\r\n  };\r\n\r\n  return (\r\n    <ThemeContext.Provider value={value}>\r\n      {children}\r\n    </ThemeContext.Provider>\r\n  );\r\n};\r\n\r\nexport default ThemeProvider;\r\n", "\"use client\";import o,{forwardRef as fe,isValidElement as xt}from\"react\";import vt from\"react-dom\";import E from\"react\";var jt=n=>{switch(n){case\"success\":return ee;case\"info\":return ae;case\"warning\":return oe;case\"error\":return se;default:return null}},te=Array(12).fill(0),Yt=({visible:n,className:e})=>E.createElement(\"div\",{className:[\"sonner-loading-wrapper\",e].filter(Boolean).join(\" \"),\"data-visible\":n},E.createElement(\"div\",{className:\"sonner-spinner\"},te.map((t,a)=>E.createElement(\"div\",{className:\"sonner-loading-bar\",key:`spinner-bar-${a}`})))),ee=E.createElement(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 20 20\",fill:\"currentColor\",height:\"20\",width:\"20\"},<PERSON><PERSON>createElement(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",clipRule:\"evenodd\"})),oe=E.createElement(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",height:\"20\",width:\"20\"},E.createElement(\"path\",{fillRule:\"evenodd\",d:\"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",clipRule:\"evenodd\"})),ae=E.createElement(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 20 20\",fill:\"currentColor\",height:\"20\",width:\"20\"},E.createElement(\"path\",{fillRule:\"evenodd\",d:\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",clipRule:\"evenodd\"})),se=E.createElement(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 20 20\",fill:\"currentColor\",height:\"20\",width:\"20\"},E.createElement(\"path\",{fillRule:\"evenodd\",d:\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",clipRule:\"evenodd\"})),Ot=E.createElement(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",width:\"12\",height:\"12\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"1.5\",strokeLinecap:\"round\",strokeLinejoin:\"round\"},E.createElement(\"line\",{x1:\"18\",y1:\"6\",x2:\"6\",y2:\"18\"}),E.createElement(\"line\",{x1:\"6\",y1:\"6\",x2:\"18\",y2:\"18\"}));import $t from\"react\";var Ft=()=>{let[n,e]=$t.useState(document.hidden);return $t.useEffect(()=>{let t=()=>{e(document.hidden)};return document.addEventListener(\"visibilitychange\",t),()=>window.removeEventListener(\"visibilitychange\",t)},[]),n};import re from\"react\";var bt=1,yt=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)});this.publish=e=>{this.subscribers.forEach(t=>t(e))};this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]};this.create=e=>{var S;let{message:t,...a}=e,u=typeof(e==null?void 0:e.id)==\"number\"||((S=e.id)==null?void 0:S.length)>0?e.id:bt++,f=this.toasts.find(g=>g.id===u),w=e.dismissible===void 0?!0:e.dismissible;return this.dismissedToasts.has(u)&&this.dismissedToasts.delete(u),f?this.toasts=this.toasts.map(g=>g.id===u?(this.publish({...g,...e,id:u,title:t}),{...g,...e,id:u,dismissible:w,title:t}):g):this.addToast({title:t,...a,dismissible:w,id:u}),u};this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(t=>{this.subscribers.forEach(a=>a({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e);this.message=(e,t)=>this.create({...t,message:e});this.error=(e,t)=>this.create({...t,message:e,type:\"error\"});this.success=(e,t)=>this.create({...t,type:\"success\",message:e});this.info=(e,t)=>this.create({...t,type:\"info\",message:e});this.warning=(e,t)=>this.create({...t,type:\"warning\",message:e});this.loading=(e,t)=>this.create({...t,type:\"loading\",message:e});this.promise=(e,t)=>{if(!t)return;let a;t.loading!==void 0&&(a=this.create({...t,promise:e,type:\"loading\",message:t.loading,description:typeof t.description!=\"function\"?t.description:void 0}));let u=e instanceof Promise?e:e(),f=a!==void 0,w,S=u.then(async i=>{if(w=[\"resolve\",i],re.isValidElement(i))f=!1,this.create({id:a,type:\"default\",message:i});else if(ie(i)&&!i.ok){f=!1;let T=typeof t.error==\"function\"?await t.error(`HTTP error! status: ${i.status}`):t.error,F=typeof t.description==\"function\"?await t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:a,type:\"error\",message:T,description:F})}else if(t.success!==void 0){f=!1;let T=typeof t.success==\"function\"?await t.success(i):t.success,F=typeof t.description==\"function\"?await t.description(i):t.description;this.create({id:a,type:\"success\",message:T,description:F})}}).catch(async i=>{if(w=[\"reject\",i],t.error!==void 0){f=!1;let D=typeof t.error==\"function\"?await t.error(i):t.error,T=typeof t.description==\"function\"?await t.description(i):t.description;this.create({id:a,type:\"error\",message:D,description:T})}}).finally(()=>{var i;f&&(this.dismiss(a),a=void 0),(i=t.finally)==null||i.call(t)}),g=()=>new Promise((i,D)=>S.then(()=>w[0]===\"reject\"?D(w[1]):i(w[1])).catch(D));return typeof a!=\"string\"&&typeof a!=\"number\"?{unwrap:g}:Object.assign(a,{unwrap:g})};this.custom=(e,t)=>{let a=(t==null?void 0:t.id)||bt++;return this.create({jsx:e(a),id:a,...t}),a};this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id));this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},v=new yt,ne=(n,e)=>{let t=(e==null?void 0:e.id)||bt++;return v.addToast({title:n,...e,id:t}),t},ie=n=>n&&typeof n==\"object\"&&\"ok\"in n&&typeof n.ok==\"boolean\"&&\"status\"in n&&typeof n.status==\"number\",le=ne,ce=()=>v.toasts,de=()=>v.getActiveToasts(),ue=Object.assign(le,{success:v.success,info:v.info,warning:v.warning,error:v.error,custom:v.custom,message:v.message,promise:v.promise,dismiss:v.dismiss,loading:v.loading},{getHistory:ce,getToasts:de});function wt(n,{insertAt:e}={}){if(!n||typeof document==\"undefined\")return;let t=document.head||document.getElementsByTagName(\"head\")[0],a=document.createElement(\"style\");a.type=\"text/css\",e===\"top\"&&t.firstChild?t.insertBefore(a,t.firstChild):t.appendChild(a),a.styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n))}wt(`:where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\"true\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\"true\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\"right\"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position=\"left\"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n`);function tt(n){return n.label!==void 0}var pe=3,me=\"32px\",ge=\"16px\",Wt=4e3,he=356,be=14,ye=20,we=200;function M(...n){return n.filter(Boolean).join(\" \")}function xe(n){let[e,t]=n.split(\"-\"),a=[];return e&&a.push(e),t&&a.push(t),a}var ve=n=>{var Dt,Pt,Nt,Bt,Ct,kt,It,Mt,Ht,At,Lt;let{invert:e,toast:t,unstyled:a,interacting:u,setHeights:f,visibleToasts:w,heights:S,index:g,toasts:i,expanded:D,removeToast:T,defaultRichColors:F,closeButton:et,style:ut,cancelButtonStyle:ft,actionButtonStyle:l,className:ot=\"\",descriptionClassName:at=\"\",duration:X,position:st,gap:pt,loadingIcon:rt,expandByDefault:B,classNames:s,icons:P,closeButtonAriaLabel:nt=\"Close toast\",pauseWhenPageIsHidden:it}=n,[Y,C]=o.useState(null),[lt,J]=o.useState(null),[W,H]=o.useState(!1),[A,mt]=o.useState(!1),[L,z]=o.useState(!1),[ct,d]=o.useState(!1),[h,y]=o.useState(!1),[R,j]=o.useState(0),[p,_]=o.useState(0),O=o.useRef(t.duration||X||Wt),G=o.useRef(null),k=o.useRef(null),Vt=g===0,Ut=g+1<=w,N=t.type,V=t.dismissible!==!1,Kt=t.className||\"\",Xt=t.descriptionClassName||\"\",dt=o.useMemo(()=>S.findIndex(r=>r.toastId===t.id)||0,[S,t.id]),Jt=o.useMemo(()=>{var r;return(r=t.closeButton)!=null?r:et},[t.closeButton,et]),Tt=o.useMemo(()=>t.duration||X||Wt,[t.duration,X]),gt=o.useRef(0),U=o.useRef(0),St=o.useRef(0),K=o.useRef(null),[Gt,Qt]=st.split(\"-\"),Rt=o.useMemo(()=>S.reduce((r,m,c)=>c>=dt?r:r+m.height,0),[S,dt]),Et=Ft(),qt=t.invert||e,ht=N===\"loading\";U.current=o.useMemo(()=>dt*pt+Rt,[dt,Rt]),o.useEffect(()=>{O.current=Tt},[Tt]),o.useEffect(()=>{H(!0)},[]),o.useEffect(()=>{let r=k.current;if(r){let m=r.getBoundingClientRect().height;return _(m),f(c=>[{toastId:t.id,height:m,position:t.position},...c]),()=>f(c=>c.filter(b=>b.toastId!==t.id))}},[f,t.id]),o.useLayoutEffect(()=>{if(!W)return;let r=k.current,m=r.style.height;r.style.height=\"auto\";let c=r.getBoundingClientRect().height;r.style.height=m,_(c),f(b=>b.find(x=>x.toastId===t.id)?b.map(x=>x.toastId===t.id?{...x,height:c}:x):[{toastId:t.id,height:c,position:t.position},...b])},[W,t.title,t.description,f,t.id]);let $=o.useCallback(()=>{mt(!0),j(U.current),f(r=>r.filter(m=>m.toastId!==t.id)),setTimeout(()=>{T(t)},we)},[t,T,f,U]);o.useEffect(()=>{if(t.promise&&N===\"loading\"||t.duration===1/0||t.type===\"loading\")return;let r;return D||u||it&&Et?(()=>{if(St.current<gt.current){let b=new Date().getTime()-gt.current;O.current=O.current-b}St.current=new Date().getTime()})():(()=>{O.current!==1/0&&(gt.current=new Date().getTime(),r=setTimeout(()=>{var b;(b=t.onAutoClose)==null||b.call(t,t),$()},O.current))})(),()=>clearTimeout(r)},[D,u,t,N,it,Et,$]),o.useEffect(()=>{t.delete&&$()},[$,t.delete]);function Zt(){var r,m,c;return P!=null&&P.loading?o.createElement(\"div\",{className:M(s==null?void 0:s.loader,(r=t==null?void 0:t.classNames)==null?void 0:r.loader,\"sonner-loader\"),\"data-visible\":N===\"loading\"},P.loading):rt?o.createElement(\"div\",{className:M(s==null?void 0:s.loader,(m=t==null?void 0:t.classNames)==null?void 0:m.loader,\"sonner-loader\"),\"data-visible\":N===\"loading\"},rt):o.createElement(Yt,{className:M(s==null?void 0:s.loader,(c=t==null?void 0:t.classNames)==null?void 0:c.loader),visible:N===\"loading\"})}return o.createElement(\"li\",{tabIndex:0,ref:k,className:M(ot,Kt,s==null?void 0:s.toast,(Dt=t==null?void 0:t.classNames)==null?void 0:Dt.toast,s==null?void 0:s.default,s==null?void 0:s[N],(Pt=t==null?void 0:t.classNames)==null?void 0:Pt[N]),\"data-sonner-toast\":\"\",\"data-rich-colors\":(Nt=t.richColors)!=null?Nt:F,\"data-styled\":!(t.jsx||t.unstyled||a),\"data-mounted\":W,\"data-promise\":!!t.promise,\"data-swiped\":h,\"data-removed\":A,\"data-visible\":Ut,\"data-y-position\":Gt,\"data-x-position\":Qt,\"data-index\":g,\"data-front\":Vt,\"data-swiping\":L,\"data-dismissible\":V,\"data-type\":N,\"data-invert\":qt,\"data-swipe-out\":ct,\"data-swipe-direction\":lt,\"data-expanded\":!!(D||B&&W),style:{\"--index\":g,\"--toasts-before\":g,\"--z-index\":i.length-g,\"--offset\":`${A?R:U.current}px`,\"--initial-height\":B?\"auto\":`${p}px`,...ut,...t.style},onDragEnd:()=>{z(!1),C(null),K.current=null},onPointerDown:r=>{ht||!V||(G.current=new Date,j(U.current),r.target.setPointerCapture(r.pointerId),r.target.tagName!==\"BUTTON\"&&(z(!0),K.current={x:r.clientX,y:r.clientY}))},onPointerUp:()=>{var x,Q,q,Z;if(ct||!V)return;K.current=null;let r=Number(((x=k.current)==null?void 0:x.style.getPropertyValue(\"--swipe-amount-x\").replace(\"px\",\"\"))||0),m=Number(((Q=k.current)==null?void 0:Q.style.getPropertyValue(\"--swipe-amount-y\").replace(\"px\",\"\"))||0),c=new Date().getTime()-((q=G.current)==null?void 0:q.getTime()),b=Y===\"x\"?r:m,I=Math.abs(b)/c;if(Math.abs(b)>=ye||I>.11){j(U.current),(Z=t.onDismiss)==null||Z.call(t,t),J(Y===\"x\"?r>0?\"right\":\"left\":m>0?\"down\":\"up\"),$(),d(!0),y(!1);return}z(!1),C(null)},onPointerMove:r=>{var Q,q,Z,zt;if(!K.current||!V||((Q=window.getSelection())==null?void 0:Q.toString().length)>0)return;let c=r.clientY-K.current.y,b=r.clientX-K.current.x,I=(q=n.swipeDirections)!=null?q:xe(st);!Y&&(Math.abs(b)>1||Math.abs(c)>1)&&C(Math.abs(b)>Math.abs(c)?\"x\":\"y\");let x={x:0,y:0};Y===\"y\"?(I.includes(\"top\")||I.includes(\"bottom\"))&&(I.includes(\"top\")&&c<0||I.includes(\"bottom\")&&c>0)&&(x.y=c):Y===\"x\"&&(I.includes(\"left\")||I.includes(\"right\"))&&(I.includes(\"left\")&&b<0||I.includes(\"right\")&&b>0)&&(x.x=b),(Math.abs(x.x)>0||Math.abs(x.y)>0)&&y(!0),(Z=k.current)==null||Z.style.setProperty(\"--swipe-amount-x\",`${x.x}px`),(zt=k.current)==null||zt.style.setProperty(\"--swipe-amount-y\",`${x.y}px`)}},Jt&&!t.jsx?o.createElement(\"button\",{\"aria-label\":nt,\"data-disabled\":ht,\"data-close-button\":!0,onClick:ht||!V?()=>{}:()=>{var r;$(),(r=t.onDismiss)==null||r.call(t,t)},className:M(s==null?void 0:s.closeButton,(Bt=t==null?void 0:t.classNames)==null?void 0:Bt.closeButton)},(Ct=P==null?void 0:P.close)!=null?Ct:Ot):null,t.jsx||xt(t.title)?t.jsx?t.jsx:typeof t.title==\"function\"?t.title():t.title:o.createElement(o.Fragment,null,N||t.icon||t.promise?o.createElement(\"div\",{\"data-icon\":\"\",className:M(s==null?void 0:s.icon,(kt=t==null?void 0:t.classNames)==null?void 0:kt.icon)},t.promise||t.type===\"loading\"&&!t.icon?t.icon||Zt():null,t.type!==\"loading\"?t.icon||(P==null?void 0:P[N])||jt(N):null):null,o.createElement(\"div\",{\"data-content\":\"\",className:M(s==null?void 0:s.content,(It=t==null?void 0:t.classNames)==null?void 0:It.content)},o.createElement(\"div\",{\"data-title\":\"\",className:M(s==null?void 0:s.title,(Mt=t==null?void 0:t.classNames)==null?void 0:Mt.title)},typeof t.title==\"function\"?t.title():t.title),t.description?o.createElement(\"div\",{\"data-description\":\"\",className:M(at,Xt,s==null?void 0:s.description,(Ht=t==null?void 0:t.classNames)==null?void 0:Ht.description)},typeof t.description==\"function\"?t.description():t.description):null),xt(t.cancel)?t.cancel:t.cancel&&tt(t.cancel)?o.createElement(\"button\",{\"data-button\":!0,\"data-cancel\":!0,style:t.cancelButtonStyle||ft,onClick:r=>{var m,c;tt(t.cancel)&&V&&((c=(m=t.cancel).onClick)==null||c.call(m,r),$())},className:M(s==null?void 0:s.cancelButton,(At=t==null?void 0:t.classNames)==null?void 0:At.cancelButton)},t.cancel.label):null,xt(t.action)?t.action:t.action&&tt(t.action)?o.createElement(\"button\",{\"data-button\":!0,\"data-action\":!0,style:t.actionButtonStyle||l,onClick:r=>{var m,c;tt(t.action)&&((c=(m=t.action).onClick)==null||c.call(m,r),!r.defaultPrevented&&$())},className:M(s==null?void 0:s.actionButton,(Lt=t==null?void 0:t.classNames)==null?void 0:Lt.actionButton)},t.action.label):null))};function _t(){if(typeof window==\"undefined\"||typeof document==\"undefined\")return\"ltr\";let n=document.documentElement.getAttribute(\"dir\");return n===\"auto\"||!n?window.getComputedStyle(document.documentElement).direction:n}function Te(n,e){let t={};return[n,e].forEach((a,u)=>{let f=u===1,w=f?\"--mobile-offset\":\"--offset\",S=f?ge:me;function g(i){[\"top\",\"right\",\"bottom\",\"left\"].forEach(D=>{t[`${w}-${D}`]=typeof i==\"number\"?`${i}px`:i})}typeof a==\"number\"||typeof a==\"string\"?g(a):typeof a==\"object\"?[\"top\",\"right\",\"bottom\",\"left\"].forEach(i=>{a[i]===void 0?t[`${w}-${i}`]=S:t[`${w}-${i}`]=typeof a[i]==\"number\"?`${a[i]}px`:a[i]}):g(S)}),t}function Oe(){let[n,e]=o.useState([]);return o.useEffect(()=>v.subscribe(t=>{if(t.dismiss){setTimeout(()=>{vt.flushSync(()=>{e(a=>a.filter(u=>u.id!==t.id))})});return}setTimeout(()=>{vt.flushSync(()=>{e(a=>{let u=a.findIndex(f=>f.id===t.id);return u!==-1?[...a.slice(0,u),{...a[u],...t},...a.slice(u+1)]:[t,...a]})})})}),[]),{toasts:n}}var $e=fe(function(e,t){let{invert:a,position:u=\"bottom-right\",hotkey:f=[\"altKey\",\"KeyT\"],expand:w,closeButton:S,className:g,offset:i,mobileOffset:D,theme:T=\"light\",richColors:F,duration:et,style:ut,visibleToasts:ft=pe,toastOptions:l,dir:ot=_t(),gap:at=be,loadingIcon:X,icons:st,containerAriaLabel:pt=\"Notifications\",pauseWhenPageIsHidden:rt}=e,[B,s]=o.useState([]),P=o.useMemo(()=>Array.from(new Set([u].concat(B.filter(d=>d.position).map(d=>d.position)))),[B,u]),[nt,it]=o.useState([]),[Y,C]=o.useState(!1),[lt,J]=o.useState(!1),[W,H]=o.useState(T!==\"system\"?T:typeof window!=\"undefined\"&&window.matchMedia&&window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"),A=o.useRef(null),mt=f.join(\"+\").replace(/Key/g,\"\").replace(/Digit/g,\"\"),L=o.useRef(null),z=o.useRef(!1),ct=o.useCallback(d=>{s(h=>{var y;return(y=h.find(R=>R.id===d.id))!=null&&y.delete||v.dismiss(d.id),h.filter(({id:R})=>R!==d.id)})},[]);return o.useEffect(()=>v.subscribe(d=>{if(d.dismiss){s(h=>h.map(y=>y.id===d.id?{...y,delete:!0}:y));return}setTimeout(()=>{vt.flushSync(()=>{s(h=>{let y=h.findIndex(R=>R.id===d.id);return y!==-1?[...h.slice(0,y),{...h[y],...d},...h.slice(y+1)]:[d,...h]})})})}),[]),o.useEffect(()=>{if(T!==\"system\"){H(T);return}if(T===\"system\"&&(window.matchMedia&&window.matchMedia(\"(prefers-color-scheme: dark)\").matches?H(\"dark\"):H(\"light\")),typeof window==\"undefined\")return;let d=window.matchMedia(\"(prefers-color-scheme: dark)\");try{d.addEventListener(\"change\",({matches:h})=>{H(h?\"dark\":\"light\")})}catch(h){d.addListener(({matches:y})=>{try{H(y?\"dark\":\"light\")}catch(R){console.error(R)}})}},[T]),o.useEffect(()=>{B.length<=1&&C(!1)},[B]),o.useEffect(()=>{let d=h=>{var R,j;f.every(p=>h[p]||h.code===p)&&(C(!0),(R=A.current)==null||R.focus()),h.code===\"Escape\"&&(document.activeElement===A.current||(j=A.current)!=null&&j.contains(document.activeElement))&&C(!1)};return document.addEventListener(\"keydown\",d),()=>document.removeEventListener(\"keydown\",d)},[f]),o.useEffect(()=>{if(A.current)return()=>{L.current&&(L.current.focus({preventScroll:!0}),L.current=null,z.current=!1)}},[A.current]),o.createElement(\"section\",{ref:t,\"aria-label\":`${pt} ${mt}`,tabIndex:-1,\"aria-live\":\"polite\",\"aria-relevant\":\"additions text\",\"aria-atomic\":\"false\",suppressHydrationWarning:!0},P.map((d,h)=>{var j;let[y,R]=d.split(\"-\");return B.length?o.createElement(\"ol\",{key:d,dir:ot===\"auto\"?_t():ot,tabIndex:-1,ref:A,className:g,\"data-sonner-toaster\":!0,\"data-theme\":W,\"data-y-position\":y,\"data-lifted\":Y&&B.length>1&&!w,\"data-x-position\":R,style:{\"--front-toast-height\":`${((j=nt[0])==null?void 0:j.height)||0}px`,\"--width\":`${he}px`,\"--gap\":`${at}px`,...ut,...Te(i,D)},onBlur:p=>{z.current&&!p.currentTarget.contains(p.relatedTarget)&&(z.current=!1,L.current&&(L.current.focus({preventScroll:!0}),L.current=null))},onFocus:p=>{p.target instanceof HTMLElement&&p.target.dataset.dismissible===\"false\"||z.current||(z.current=!0,L.current=p.relatedTarget)},onMouseEnter:()=>C(!0),onMouseMove:()=>C(!0),onMouseLeave:()=>{lt||C(!1)},onDragEnd:()=>C(!1),onPointerDown:p=>{p.target instanceof HTMLElement&&p.target.dataset.dismissible===\"false\"||J(!0)},onPointerUp:()=>J(!1)},B.filter(p=>!p.position&&h===0||p.position===d).map((p,_)=>{var O,G;return o.createElement(ve,{key:p.id,icons:st,index:_,toast:p,defaultRichColors:F,duration:(O=l==null?void 0:l.duration)!=null?O:et,className:l==null?void 0:l.className,descriptionClassName:l==null?void 0:l.descriptionClassName,invert:a,visibleToasts:ft,closeButton:(G=l==null?void 0:l.closeButton)!=null?G:S,interacting:lt,position:d,style:l==null?void 0:l.style,unstyled:l==null?void 0:l.unstyled,classNames:l==null?void 0:l.classNames,cancelButtonStyle:l==null?void 0:l.cancelButtonStyle,actionButtonStyle:l==null?void 0:l.actionButtonStyle,removeToast:ct,toasts:B.filter(k=>k.position==p.position),heights:nt.filter(k=>k.position==p.position),setHeights:it,expandByDefault:w,gap:at,loadingIcon:X,expanded:Y,pauseWhenPageIsHidden:rt,swipeDirections:e.swipeDirections})})):null}))});export{$e as Toaster,ue as toast,Oe as useSonner};\n//# sourceMappingURL=index.mjs.map", "import { useTheme } from \"@/components/providers/ThemeProvider\"\r\nimport { Toaster as Sonner, toast } from \"sonner\"\r\n\r\ntype ToasterProps = React.ComponentProps<typeof Sonner>\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = \"system\" } = useTheme()\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps[\"theme\"]}\r\n      className=\"toaster group\"\r\n      position=\"top-center\"\r\n      expand={false}\r\n      richColors\r\n      closeButton\r\n      toastOptions={{\r\n        duration: 3000,\r\n        classNames: {\r\n          toast:\r\n            \"group toast group-[.toaster]:bg-[var(--color-surface)] group-[.toaster]:text-[var(--color-text)] group-[.toaster]:border-[var(--color-border)] group-[.toaster]:shadow-xl group-[.toaster]:backdrop-blur-sm\",\r\n          description: \"group-[.toast]:text-[var(--color-muted)]\",\r\n          actionButton:\r\n            \"group-[.toast]:bg-[var(--color-primary)] group-[.toast]:text-white\",\r\n          cancelButton:\r\n            \"group-[.toast]:bg-[var(--color-neutral)] group-[.toast]:text-[var(--color-text)]\",\r\n          closeButton:\r\n            \"group-[.toast]:bg-[var(--color-surface)] group-[.toast]:text-[var(--color-muted)] group-[.toast]:border-[var(--color-border)]\",\r\n          success:\r\n            \"group-[.toaster]:bg-green-50 group-[.toaster]:text-green-900 group-[.toaster]:border-green-200 dark:group-[.toaster]:bg-green-900/20 dark:group-[.toaster]:text-green-100 dark:group-[.toaster]:border-green-800\",\r\n          error:\r\n            \"group-[.toaster]:bg-red-50 group-[.toaster]:text-red-900 group-[.toaster]:border-red-200 dark:group-[.toaster]:bg-red-900/20 dark:group-[.toaster]:text-red-100 dark:group-[.toaster]:border-red-800\",\r\n          warning:\r\n            \"group-[.toaster]:bg-yellow-50 group-[.toaster]:text-yellow-900 group-[.toaster]:border-yellow-200 dark:group-[.toaster]:bg-yellow-900/20 dark:group-[.toaster]:text-yellow-100 dark:group-[.toaster]:border-yellow-800\",\r\n          info:\r\n            \"group-[.toaster]:bg-blue-50 group-[.toaster]:text-blue-900 group-[.toaster]:border-blue-200 dark:group-[.toaster]:bg-blue-900/20 dark:group-[.toaster]:text-blue-100 dark:group-[.toaster]:border-blue-800\",\r\n        },\r\n        style: {\r\n          borderRadius: '12px',\r\n          padding: '16px',\r\n          fontSize: '14px',\r\n          fontWeight: '500',\r\n        }\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster, toast }\r\n", "import * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n", "/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && (a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n\n    if (hasSet && (a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (((error.message || '').match(/stack|recursion/i))) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar invariant = function(condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error(\n        'Minified exception occurred; use the non-minified dev environment ' +\n        'for the full error message and additional helpful warnings.'\n      );\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(\n        format.replace(/%s/g, function() { return args[argIndex++]; })\n      );\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\n\nmodule.exports = invariant;\n", "//\n\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n\n  if (ret !== void 0) {\n    return !!ret;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = objA[key];\n    var valueB = objB[key];\n\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n\n    if (ret === false || (ret === void 0 && valueA !== valueB)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n", "// src/index.tsx\nimport React3, { Component as Component3 } from \"react\";\nimport fastCompare from \"react-fast-compare\";\nimport invariant from \"invariant\";\n\n// src/Provider.tsx\nimport React2, { Component } from \"react\";\n\n// src/server.ts\nimport React from \"react\";\n\n// src/constants.ts\nvar TAG_NAMES = /* @__PURE__ */ ((TAG_NAMES2) => {\n  TAG_NAMES2[\"BASE\"] = \"base\";\n  TAG_NAMES2[\"BODY\"] = \"body\";\n  TAG_NAMES2[\"HEAD\"] = \"head\";\n  TAG_NAMES2[\"HTML\"] = \"html\";\n  TAG_NAMES2[\"LINK\"] = \"link\";\n  TAG_NAMES2[\"META\"] = \"meta\";\n  TAG_NAMES2[\"NOSCRIPT\"] = \"noscript\";\n  TAG_NAMES2[\"SCRIPT\"] = \"script\";\n  TAG_NAMES2[\"STYLE\"] = \"style\";\n  TAG_NAMES2[\"TITLE\"] = \"title\";\n  TAG_NAMES2[\"FRAGMENT\"] = \"Symbol(react.fragment)\";\n  return TAG_NAMES2;\n})(TAG_NAMES || {});\nvar SEO_PRIORITY_TAGS = {\n  link: { rel: [\"amphtml\", \"canonical\", \"alternate\"] },\n  script: { type: [\"application/ld+json\"] },\n  meta: {\n    charset: \"\",\n    name: [\"generator\", \"robots\", \"description\"],\n    property: [\n      \"og:type\",\n      \"og:title\",\n      \"og:url\",\n      \"og:image\",\n      \"og:image:alt\",\n      \"og:description\",\n      \"twitter:url\",\n      \"twitter:title\",\n      \"twitter:description\",\n      \"twitter:image\",\n      \"twitter:image:alt\",\n      \"twitter:card\",\n      \"twitter:site\"\n    ]\n  }\n};\nvar VALID_TAG_NAMES = Object.values(TAG_NAMES);\nvar REACT_TAG_MAP = {\n  accesskey: \"accessKey\",\n  charset: \"charSet\",\n  class: \"className\",\n  contenteditable: \"contentEditable\",\n  contextmenu: \"contextMenu\",\n  \"http-equiv\": \"httpEquiv\",\n  itemprop: \"itemProp\",\n  tabindex: \"tabIndex\"\n};\nvar HTML_TAG_MAP = Object.entries(REACT_TAG_MAP).reduce(\n  (carry, [key, value]) => {\n    carry[value] = key;\n    return carry;\n  },\n  {}\n);\nvar HELMET_ATTRIBUTE = \"data-rh\";\n\n// src/utils.ts\nvar HELMET_PROPS = {\n  DEFAULT_TITLE: \"defaultTitle\",\n  DEFER: \"defer\",\n  ENCODE_SPECIAL_CHARACTERS: \"encodeSpecialCharacters\",\n  ON_CHANGE_CLIENT_STATE: \"onChangeClientState\",\n  TITLE_TEMPLATE: \"titleTemplate\",\n  PRIORITIZE_SEO_TAGS: \"prioritizeSeoTags\"\n};\nvar getInnermostProperty = (propsList, property) => {\n  for (let i = propsList.length - 1; i >= 0; i -= 1) {\n    const props = propsList[i];\n    if (Object.prototype.hasOwnProperty.call(props, property)) {\n      return props[property];\n    }\n  }\n  return null;\n};\nvar getTitleFromPropsList = (propsList) => {\n  let innermostTitle = getInnermostProperty(propsList, \"title\" /* TITLE */);\n  const innermostTemplate = getInnermostProperty(propsList, HELMET_PROPS.TITLE_TEMPLATE);\n  if (Array.isArray(innermostTitle)) {\n    innermostTitle = innermostTitle.join(\"\");\n  }\n  if (innermostTemplate && innermostTitle) {\n    return innermostTemplate.replace(/%s/g, () => innermostTitle);\n  }\n  const innermostDefaultTitle = getInnermostProperty(propsList, HELMET_PROPS.DEFAULT_TITLE);\n  return innermostTitle || innermostDefaultTitle || void 0;\n};\nvar getOnChangeClientState = (propsList) => getInnermostProperty(propsList, HELMET_PROPS.ON_CHANGE_CLIENT_STATE) || (() => {\n});\nvar getAttributesFromPropsList = (tagType, propsList) => propsList.filter((props) => typeof props[tagType] !== \"undefined\").map((props) => props[tagType]).reduce((tagAttrs, current) => ({ ...tagAttrs, ...current }), {});\nvar getBaseTagFromPropsList = (primaryAttributes, propsList) => propsList.filter((props) => typeof props[\"base\" /* BASE */] !== \"undefined\").map((props) => props[\"base\" /* BASE */]).reverse().reduce((innermostBaseTag, tag) => {\n  if (!innermostBaseTag.length) {\n    const keys = Object.keys(tag);\n    for (let i = 0; i < keys.length; i += 1) {\n      const attributeKey = keys[i];\n      const lowerCaseAttributeKey = attributeKey.toLowerCase();\n      if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && tag[lowerCaseAttributeKey]) {\n        return innermostBaseTag.concat(tag);\n      }\n    }\n  }\n  return innermostBaseTag;\n}, []);\nvar warn = (msg) => console && typeof console.warn === \"function\" && console.warn(msg);\nvar getTagsFromPropsList = (tagName, primaryAttributes, propsList) => {\n  const approvedSeenTags = {};\n  return propsList.filter((props) => {\n    if (Array.isArray(props[tagName])) {\n      return true;\n    }\n    if (typeof props[tagName] !== \"undefined\") {\n      warn(\n        `Helmet: ${tagName} should be of type \"Array\". Instead found type \"${typeof props[tagName]}\"`\n      );\n    }\n    return false;\n  }).map((props) => props[tagName]).reverse().reduce((approvedTags, instanceTags) => {\n    const instanceSeenTags = {};\n    instanceTags.filter((tag) => {\n      let primaryAttributeKey;\n      const keys2 = Object.keys(tag);\n      for (let i = 0; i < keys2.length; i += 1) {\n        const attributeKey = keys2[i];\n        const lowerCaseAttributeKey = attributeKey.toLowerCase();\n        if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && !(primaryAttributeKey === \"rel\" /* REL */ && tag[primaryAttributeKey].toLowerCase() === \"canonical\") && !(lowerCaseAttributeKey === \"rel\" /* REL */ && tag[lowerCaseAttributeKey].toLowerCase() === \"stylesheet\")) {\n          primaryAttributeKey = lowerCaseAttributeKey;\n        }\n        if (primaryAttributes.indexOf(attributeKey) !== -1 && (attributeKey === \"innerHTML\" /* INNER_HTML */ || attributeKey === \"cssText\" /* CSS_TEXT */ || attributeKey === \"itemprop\" /* ITEM_PROP */)) {\n          primaryAttributeKey = attributeKey;\n        }\n      }\n      if (!primaryAttributeKey || !tag[primaryAttributeKey]) {\n        return false;\n      }\n      const value = tag[primaryAttributeKey].toLowerCase();\n      if (!approvedSeenTags[primaryAttributeKey]) {\n        approvedSeenTags[primaryAttributeKey] = {};\n      }\n      if (!instanceSeenTags[primaryAttributeKey]) {\n        instanceSeenTags[primaryAttributeKey] = {};\n      }\n      if (!approvedSeenTags[primaryAttributeKey][value]) {\n        instanceSeenTags[primaryAttributeKey][value] = true;\n        return true;\n      }\n      return false;\n    }).reverse().forEach((tag) => approvedTags.push(tag));\n    const keys = Object.keys(instanceSeenTags);\n    for (let i = 0; i < keys.length; i += 1) {\n      const attributeKey = keys[i];\n      const tagUnion = {\n        ...approvedSeenTags[attributeKey],\n        ...instanceSeenTags[attributeKey]\n      };\n      approvedSeenTags[attributeKey] = tagUnion;\n    }\n    return approvedTags;\n  }, []).reverse();\n};\nvar getAnyTrueFromPropsList = (propsList, checkedTag) => {\n  if (Array.isArray(propsList) && propsList.length) {\n    for (let index = 0; index < propsList.length; index += 1) {\n      const prop = propsList[index];\n      if (prop[checkedTag]) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\nvar reducePropsToState = (propsList) => ({\n  baseTag: getBaseTagFromPropsList([\"href\" /* HREF */], propsList),\n  bodyAttributes: getAttributesFromPropsList(\"bodyAttributes\" /* BODY */, propsList),\n  defer: getInnermostProperty(propsList, HELMET_PROPS.DEFER),\n  encode: getInnermostProperty(propsList, HELMET_PROPS.ENCODE_SPECIAL_CHARACTERS),\n  htmlAttributes: getAttributesFromPropsList(\"htmlAttributes\" /* HTML */, propsList),\n  linkTags: getTagsFromPropsList(\n    \"link\" /* LINK */,\n    [\"rel\" /* REL */, \"href\" /* HREF */],\n    propsList\n  ),\n  metaTags: getTagsFromPropsList(\n    \"meta\" /* META */,\n    [\n      \"name\" /* NAME */,\n      \"charset\" /* CHARSET */,\n      \"http-equiv\" /* HTTPEQUIV */,\n      \"property\" /* PROPERTY */,\n      \"itemprop\" /* ITEM_PROP */\n    ],\n    propsList\n  ),\n  noscriptTags: getTagsFromPropsList(\"noscript\" /* NOSCRIPT */, [\"innerHTML\" /* INNER_HTML */], propsList),\n  onChangeClientState: getOnChangeClientState(propsList),\n  scriptTags: getTagsFromPropsList(\n    \"script\" /* SCRIPT */,\n    [\"src\" /* SRC */, \"innerHTML\" /* INNER_HTML */],\n    propsList\n  ),\n  styleTags: getTagsFromPropsList(\"style\" /* STYLE */, [\"cssText\" /* CSS_TEXT */], propsList),\n  title: getTitleFromPropsList(propsList),\n  titleAttributes: getAttributesFromPropsList(\"titleAttributes\" /* TITLE */, propsList),\n  prioritizeSeoTags: getAnyTrueFromPropsList(propsList, HELMET_PROPS.PRIORITIZE_SEO_TAGS)\n});\nvar flattenArray = (possibleArray) => Array.isArray(possibleArray) ? possibleArray.join(\"\") : possibleArray;\nvar checkIfPropsMatch = (props, toMatch) => {\n  const keys = Object.keys(props);\n  for (let i = 0; i < keys.length; i += 1) {\n    if (toMatch[keys[i]] && toMatch[keys[i]].includes(props[keys[i]])) {\n      return true;\n    }\n  }\n  return false;\n};\nvar prioritizer = (elementsList, propsToMatch) => {\n  if (Array.isArray(elementsList)) {\n    return elementsList.reduce(\n      (acc, elementAttrs) => {\n        if (checkIfPropsMatch(elementAttrs, propsToMatch)) {\n          acc.priority.push(elementAttrs);\n        } else {\n          acc.default.push(elementAttrs);\n        }\n        return acc;\n      },\n      { priority: [], default: [] }\n    );\n  }\n  return { default: elementsList, priority: [] };\n};\nvar without = (obj, key) => {\n  return {\n    ...obj,\n    [key]: void 0\n  };\n};\n\n// src/server.ts\nvar SELF_CLOSING_TAGS = [\"noscript\" /* NOSCRIPT */, \"script\" /* SCRIPT */, \"style\" /* STYLE */];\nvar encodeSpecialCharacters = (str, encode = true) => {\n  if (encode === false) {\n    return String(str);\n  }\n  return String(str).replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#x27;\");\n};\nvar generateElementAttributesAsString = (attributes) => Object.keys(attributes).reduce((str, key) => {\n  const attr = typeof attributes[key] !== \"undefined\" ? `${key}=\"${attributes[key]}\"` : `${key}`;\n  return str ? `${str} ${attr}` : attr;\n}, \"\");\nvar generateTitleAsString = (type, title, attributes, encode) => {\n  const attributeString = generateElementAttributesAsString(attributes);\n  const flattenedTitle = flattenArray(title);\n  return attributeString ? `<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeString}>${encodeSpecialCharacters(\n    flattenedTitle,\n    encode\n  )}</${type}>` : `<${type} ${HELMET_ATTRIBUTE}=\"true\">${encodeSpecialCharacters(\n    flattenedTitle,\n    encode\n  )}</${type}>`;\n};\nvar generateTagsAsString = (type, tags, encode = true) => tags.reduce((str, t) => {\n  const tag = t;\n  const attributeHtml = Object.keys(tag).filter(\n    (attribute) => !(attribute === \"innerHTML\" /* INNER_HTML */ || attribute === \"cssText\" /* CSS_TEXT */)\n  ).reduce((string, attribute) => {\n    const attr = typeof tag[attribute] === \"undefined\" ? attribute : `${attribute}=\"${encodeSpecialCharacters(tag[attribute], encode)}\"`;\n    return string ? `${string} ${attr}` : attr;\n  }, \"\");\n  const tagContent = tag.innerHTML || tag.cssText || \"\";\n  const isSelfClosing = SELF_CLOSING_TAGS.indexOf(type) === -1;\n  return `${str}<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeHtml}${isSelfClosing ? `/>` : `>${tagContent}</${type}>`}`;\n}, \"\");\nvar convertElementAttributesToReactProps = (attributes, initProps = {}) => Object.keys(attributes).reduce((obj, key) => {\n  const mapped = REACT_TAG_MAP[key];\n  obj[mapped || key] = attributes[key];\n  return obj;\n}, initProps);\nvar generateTitleAsReactComponent = (_type, title, attributes) => {\n  const initProps = {\n    key: title,\n    [HELMET_ATTRIBUTE]: true\n  };\n  const props = convertElementAttributesToReactProps(attributes, initProps);\n  return [React.createElement(\"title\" /* TITLE */, props, title)];\n};\nvar generateTagsAsReactComponent = (type, tags) => tags.map((tag, i) => {\n  const mappedTag = {\n    key: i,\n    [HELMET_ATTRIBUTE]: true\n  };\n  Object.keys(tag).forEach((attribute) => {\n    const mapped = REACT_TAG_MAP[attribute];\n    const mappedAttribute = mapped || attribute;\n    if (mappedAttribute === \"innerHTML\" /* INNER_HTML */ || mappedAttribute === \"cssText\" /* CSS_TEXT */) {\n      const content = tag.innerHTML || tag.cssText;\n      mappedTag.dangerouslySetInnerHTML = { __html: content };\n    } else {\n      mappedTag[mappedAttribute] = tag[attribute];\n    }\n  });\n  return React.createElement(type, mappedTag);\n});\nvar getMethodsForTag = (type, tags, encode = true) => {\n  switch (type) {\n    case \"title\" /* TITLE */:\n      return {\n        toComponent: () => generateTitleAsReactComponent(type, tags.title, tags.titleAttributes),\n        toString: () => generateTitleAsString(type, tags.title, tags.titleAttributes, encode)\n      };\n    case \"bodyAttributes\" /* BODY */:\n    case \"htmlAttributes\" /* HTML */:\n      return {\n        toComponent: () => convertElementAttributesToReactProps(tags),\n        toString: () => generateElementAttributesAsString(tags)\n      };\n    default:\n      return {\n        toComponent: () => generateTagsAsReactComponent(type, tags),\n        toString: () => generateTagsAsString(type, tags, encode)\n      };\n  }\n};\nvar getPriorityMethods = ({ metaTags, linkTags, scriptTags, encode }) => {\n  const meta = prioritizer(metaTags, SEO_PRIORITY_TAGS.meta);\n  const link = prioritizer(linkTags, SEO_PRIORITY_TAGS.link);\n  const script = prioritizer(scriptTags, SEO_PRIORITY_TAGS.script);\n  const priorityMethods = {\n    toComponent: () => [\n      ...generateTagsAsReactComponent(\"meta\" /* META */, meta.priority),\n      ...generateTagsAsReactComponent(\"link\" /* LINK */, link.priority),\n      ...generateTagsAsReactComponent(\"script\" /* SCRIPT */, script.priority)\n    ],\n    toString: () => (\n      // generate all the tags as strings and concatenate them\n      `${getMethodsForTag(\"meta\" /* META */, meta.priority, encode)} ${getMethodsForTag(\n        \"link\" /* LINK */,\n        link.priority,\n        encode\n      )} ${getMethodsForTag(\"script\" /* SCRIPT */, script.priority, encode)}`\n    )\n  };\n  return {\n    priorityMethods,\n    metaTags: meta.default,\n    linkTags: link.default,\n    scriptTags: script.default\n  };\n};\nvar mapStateOnServer = (props) => {\n  const {\n    baseTag,\n    bodyAttributes,\n    encode = true,\n    htmlAttributes,\n    noscriptTags,\n    styleTags,\n    title = \"\",\n    titleAttributes,\n    prioritizeSeoTags\n  } = props;\n  let { linkTags, metaTags, scriptTags } = props;\n  let priorityMethods = {\n    toComponent: () => {\n    },\n    toString: () => \"\"\n  };\n  if (prioritizeSeoTags) {\n    ({ priorityMethods, linkTags, metaTags, scriptTags } = getPriorityMethods(props));\n  }\n  return {\n    priority: priorityMethods,\n    base: getMethodsForTag(\"base\" /* BASE */, baseTag, encode),\n    bodyAttributes: getMethodsForTag(\"bodyAttributes\" /* BODY */, bodyAttributes, encode),\n    htmlAttributes: getMethodsForTag(\"htmlAttributes\" /* HTML */, htmlAttributes, encode),\n    link: getMethodsForTag(\"link\" /* LINK */, linkTags, encode),\n    meta: getMethodsForTag(\"meta\" /* META */, metaTags, encode),\n    noscript: getMethodsForTag(\"noscript\" /* NOSCRIPT */, noscriptTags, encode),\n    script: getMethodsForTag(\"script\" /* SCRIPT */, scriptTags, encode),\n    style: getMethodsForTag(\"style\" /* STYLE */, styleTags, encode),\n    title: getMethodsForTag(\"title\" /* TITLE */, { title, titleAttributes }, encode)\n  };\n};\nvar server_default = mapStateOnServer;\n\n// src/HelmetData.ts\nvar instances = [];\nvar isDocument = !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\nvar HelmetData = class {\n  instances = [];\n  canUseDOM = isDocument;\n  context;\n  value = {\n    setHelmet: (serverState) => {\n      this.context.helmet = serverState;\n    },\n    helmetInstances: {\n      get: () => this.canUseDOM ? instances : this.instances,\n      add: (instance) => {\n        (this.canUseDOM ? instances : this.instances).push(instance);\n      },\n      remove: (instance) => {\n        const index = (this.canUseDOM ? instances : this.instances).indexOf(instance);\n        (this.canUseDOM ? instances : this.instances).splice(index, 1);\n      }\n    }\n  };\n  constructor(context, canUseDOM) {\n    this.context = context;\n    this.canUseDOM = canUseDOM || false;\n    if (!canUseDOM) {\n      context.helmet = server_default({\n        baseTag: [],\n        bodyAttributes: {},\n        encodeSpecialCharacters: true,\n        htmlAttributes: {},\n        linkTags: [],\n        metaTags: [],\n        noscriptTags: [],\n        scriptTags: [],\n        styleTags: [],\n        title: \"\",\n        titleAttributes: {}\n      });\n    }\n  }\n};\n\n// src/Provider.tsx\nvar defaultValue = {};\nvar Context = React2.createContext(defaultValue);\nvar HelmetProvider = class _HelmetProvider extends Component {\n  static canUseDOM = isDocument;\n  helmetData;\n  constructor(props) {\n    super(props);\n    this.helmetData = new HelmetData(this.props.context || {}, _HelmetProvider.canUseDOM);\n  }\n  render() {\n    return /* @__PURE__ */ React2.createElement(Context.Provider, { value: this.helmetData.value }, this.props.children);\n  }\n};\n\n// src/Dispatcher.tsx\nimport { Component as Component2 } from \"react\";\nimport shallowEqual from \"shallowequal\";\n\n// src/client.ts\nvar updateTags = (type, tags) => {\n  const headElement = document.head || document.querySelector(\"head\" /* HEAD */);\n  const tagNodes = headElement.querySelectorAll(`${type}[${HELMET_ATTRIBUTE}]`);\n  const oldTags = [].slice.call(tagNodes);\n  const newTags = [];\n  let indexToDelete;\n  if (tags && tags.length) {\n    tags.forEach((tag) => {\n      const newElement = document.createElement(type);\n      for (const attribute in tag) {\n        if (Object.prototype.hasOwnProperty.call(tag, attribute)) {\n          if (attribute === \"innerHTML\" /* INNER_HTML */) {\n            newElement.innerHTML = tag.innerHTML;\n          } else if (attribute === \"cssText\" /* CSS_TEXT */) {\n            if (newElement.styleSheet) {\n              newElement.styleSheet.cssText = tag.cssText;\n            } else {\n              newElement.appendChild(document.createTextNode(tag.cssText));\n            }\n          } else {\n            const attr = attribute;\n            const value = typeof tag[attr] === \"undefined\" ? \"\" : tag[attr];\n            newElement.setAttribute(attribute, value);\n          }\n        }\n      }\n      newElement.setAttribute(HELMET_ATTRIBUTE, \"true\");\n      if (oldTags.some((existingTag, index) => {\n        indexToDelete = index;\n        return newElement.isEqualNode(existingTag);\n      })) {\n        oldTags.splice(indexToDelete, 1);\n      } else {\n        newTags.push(newElement);\n      }\n    });\n  }\n  oldTags.forEach((tag) => tag.parentNode?.removeChild(tag));\n  newTags.forEach((tag) => headElement.appendChild(tag));\n  return {\n    oldTags,\n    newTags\n  };\n};\nvar updateAttributes = (tagName, attributes) => {\n  const elementTag = document.getElementsByTagName(tagName)[0];\n  if (!elementTag) {\n    return;\n  }\n  const helmetAttributeString = elementTag.getAttribute(HELMET_ATTRIBUTE);\n  const helmetAttributes = helmetAttributeString ? helmetAttributeString.split(\",\") : [];\n  const attributesToRemove = [...helmetAttributes];\n  const attributeKeys = Object.keys(attributes);\n  for (const attribute of attributeKeys) {\n    const value = attributes[attribute] || \"\";\n    if (elementTag.getAttribute(attribute) !== value) {\n      elementTag.setAttribute(attribute, value);\n    }\n    if (helmetAttributes.indexOf(attribute) === -1) {\n      helmetAttributes.push(attribute);\n    }\n    const indexToSave = attributesToRemove.indexOf(attribute);\n    if (indexToSave !== -1) {\n      attributesToRemove.splice(indexToSave, 1);\n    }\n  }\n  for (let i = attributesToRemove.length - 1; i >= 0; i -= 1) {\n    elementTag.removeAttribute(attributesToRemove[i]);\n  }\n  if (helmetAttributes.length === attributesToRemove.length) {\n    elementTag.removeAttribute(HELMET_ATTRIBUTE);\n  } else if (elementTag.getAttribute(HELMET_ATTRIBUTE) !== attributeKeys.join(\",\")) {\n    elementTag.setAttribute(HELMET_ATTRIBUTE, attributeKeys.join(\",\"));\n  }\n};\nvar updateTitle = (title, attributes) => {\n  if (typeof title !== \"undefined\" && document.title !== title) {\n    document.title = flattenArray(title);\n  }\n  updateAttributes(\"title\" /* TITLE */, attributes);\n};\nvar commitTagChanges = (newState, cb) => {\n  const {\n    baseTag,\n    bodyAttributes,\n    htmlAttributes,\n    linkTags,\n    metaTags,\n    noscriptTags,\n    onChangeClientState,\n    scriptTags,\n    styleTags,\n    title,\n    titleAttributes\n  } = newState;\n  updateAttributes(\"body\" /* BODY */, bodyAttributes);\n  updateAttributes(\"html\" /* HTML */, htmlAttributes);\n  updateTitle(title, titleAttributes);\n  const tagUpdates = {\n    baseTag: updateTags(\"base\" /* BASE */, baseTag),\n    linkTags: updateTags(\"link\" /* LINK */, linkTags),\n    metaTags: updateTags(\"meta\" /* META */, metaTags),\n    noscriptTags: updateTags(\"noscript\" /* NOSCRIPT */, noscriptTags),\n    scriptTags: updateTags(\"script\" /* SCRIPT */, scriptTags),\n    styleTags: updateTags(\"style\" /* STYLE */, styleTags)\n  };\n  const addedTags = {};\n  const removedTags = {};\n  Object.keys(tagUpdates).forEach((tagType) => {\n    const { newTags, oldTags } = tagUpdates[tagType];\n    if (newTags.length) {\n      addedTags[tagType] = newTags;\n    }\n    if (oldTags.length) {\n      removedTags[tagType] = tagUpdates[tagType].oldTags;\n    }\n  });\n  if (cb) {\n    cb();\n  }\n  onChangeClientState(newState, addedTags, removedTags);\n};\nvar _helmetCallback = null;\nvar handleStateChangeOnClient = (newState) => {\n  if (_helmetCallback) {\n    cancelAnimationFrame(_helmetCallback);\n  }\n  if (newState.defer) {\n    _helmetCallback = requestAnimationFrame(() => {\n      commitTagChanges(newState, () => {\n        _helmetCallback = null;\n      });\n    });\n  } else {\n    commitTagChanges(newState);\n    _helmetCallback = null;\n  }\n};\nvar client_default = handleStateChangeOnClient;\n\n// src/Dispatcher.tsx\nvar HelmetDispatcher = class extends Component2 {\n  rendered = false;\n  shouldComponentUpdate(nextProps) {\n    return !shallowEqual(nextProps, this.props);\n  }\n  componentDidUpdate() {\n    this.emitChange();\n  }\n  componentWillUnmount() {\n    const { helmetInstances } = this.props.context;\n    helmetInstances.remove(this);\n    this.emitChange();\n  }\n  emitChange() {\n    const { helmetInstances, setHelmet } = this.props.context;\n    let serverState = null;\n    const state = reducePropsToState(\n      helmetInstances.get().map((instance) => {\n        const props = { ...instance.props };\n        delete props.context;\n        return props;\n      })\n    );\n    if (HelmetProvider.canUseDOM) {\n      client_default(state);\n    } else if (server_default) {\n      serverState = server_default(state);\n    }\n    setHelmet(serverState);\n  }\n  // componentWillMount will be deprecated\n  // for SSR, initialize on first render\n  // constructor is also unsafe in StrictMode\n  init() {\n    if (this.rendered) {\n      return;\n    }\n    this.rendered = true;\n    const { helmetInstances } = this.props.context;\n    helmetInstances.add(this);\n    this.emitChange();\n  }\n  render() {\n    this.init();\n    return null;\n  }\n};\n\n// src/index.tsx\nvar Helmet = class extends Component3 {\n  static defaultProps = {\n    defer: true,\n    encodeSpecialCharacters: true,\n    prioritizeSeoTags: false\n  };\n  shouldComponentUpdate(nextProps) {\n    return !fastCompare(without(this.props, \"helmetData\"), without(nextProps, \"helmetData\"));\n  }\n  mapNestedChildrenToProps(child, nestedChildren) {\n    if (!nestedChildren) {\n      return null;\n    }\n    switch (child.type) {\n      case \"script\" /* SCRIPT */:\n      case \"noscript\" /* NOSCRIPT */:\n        return {\n          innerHTML: nestedChildren\n        };\n      case \"style\" /* STYLE */:\n        return {\n          cssText: nestedChildren\n        };\n      default:\n        throw new Error(\n          `<${child.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`\n        );\n    }\n  }\n  flattenArrayTypeChildren(child, arrayTypeChildren, newChildProps, nestedChildren) {\n    return {\n      ...arrayTypeChildren,\n      [child.type]: [\n        ...arrayTypeChildren[child.type] || [],\n        {\n          ...newChildProps,\n          ...this.mapNestedChildrenToProps(child, nestedChildren)\n        }\n      ]\n    };\n  }\n  mapObjectTypeChildren(child, newProps, newChildProps, nestedChildren) {\n    switch (child.type) {\n      case \"title\" /* TITLE */:\n        return {\n          ...newProps,\n          [child.type]: nestedChildren,\n          titleAttributes: { ...newChildProps }\n        };\n      case \"body\" /* BODY */:\n        return {\n          ...newProps,\n          bodyAttributes: { ...newChildProps }\n        };\n      case \"html\" /* HTML */:\n        return {\n          ...newProps,\n          htmlAttributes: { ...newChildProps }\n        };\n      default:\n        return {\n          ...newProps,\n          [child.type]: { ...newChildProps }\n        };\n    }\n  }\n  mapArrayTypeChildrenToProps(arrayTypeChildren, newProps) {\n    let newFlattenedProps = { ...newProps };\n    Object.keys(arrayTypeChildren).forEach((arrayChildName) => {\n      newFlattenedProps = {\n        ...newFlattenedProps,\n        [arrayChildName]: arrayTypeChildren[arrayChildName]\n      };\n    });\n    return newFlattenedProps;\n  }\n  warnOnInvalidChildren(child, nestedChildren) {\n    invariant(\n      VALID_TAG_NAMES.some((name) => child.type === name),\n      typeof child.type === \"function\" ? `You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.` : `Only elements types ${VALID_TAG_NAMES.join(\n        \", \"\n      )} are allowed. Helmet does not support rendering <${child.type}> elements. Refer to our API for more information.`\n    );\n    invariant(\n      !nestedChildren || typeof nestedChildren === \"string\" || Array.isArray(nestedChildren) && !nestedChildren.some((nestedChild) => typeof nestedChild !== \"string\"),\n      `Helmet expects a string as a child of <${child.type}>. Did you forget to wrap your children in braces? ( <${child.type}>{\\`\\`}</${child.type}> ) Refer to our API for more information.`\n    );\n    return true;\n  }\n  mapChildrenToProps(children, newProps) {\n    let arrayTypeChildren = {};\n    React3.Children.forEach(children, (child) => {\n      if (!child || !child.props) {\n        return;\n      }\n      const { children: nestedChildren, ...childProps } = child.props;\n      const newChildProps = Object.keys(childProps).reduce((obj, key) => {\n        obj[HTML_TAG_MAP[key] || key] = childProps[key];\n        return obj;\n      }, {});\n      let { type } = child;\n      if (typeof type === \"symbol\") {\n        type = type.toString();\n      } else {\n        this.warnOnInvalidChildren(child, nestedChildren);\n      }\n      switch (type) {\n        case \"Symbol(react.fragment)\" /* FRAGMENT */:\n          newProps = this.mapChildrenToProps(nestedChildren, newProps);\n          break;\n        case \"link\" /* LINK */:\n        case \"meta\" /* META */:\n        case \"noscript\" /* NOSCRIPT */:\n        case \"script\" /* SCRIPT */:\n        case \"style\" /* STYLE */:\n          arrayTypeChildren = this.flattenArrayTypeChildren(\n            child,\n            arrayTypeChildren,\n            newChildProps,\n            nestedChildren\n          );\n          break;\n        default:\n          newProps = this.mapObjectTypeChildren(child, newProps, newChildProps, nestedChildren);\n          break;\n      }\n    });\n    return this.mapArrayTypeChildrenToProps(arrayTypeChildren, newProps);\n  }\n  render() {\n    const { children, ...props } = this.props;\n    let newProps = { ...props };\n    let { helmetData } = props;\n    if (children) {\n      newProps = this.mapChildrenToProps(children, newProps);\n    }\n    if (helmetData && !(helmetData instanceof HelmetData)) {\n      const data = helmetData;\n      helmetData = new HelmetData(data.context, true);\n      delete newProps.helmetData;\n    }\n    return helmetData ? /* @__PURE__ */ React3.createElement(HelmetDispatcher, { ...newProps, context: helmetData.value }) : /* @__PURE__ */ React3.createElement(Context.Consumer, null, (context) => /* @__PURE__ */ React3.createElement(HelmetDispatcher, { ...newProps, context }));\n  }\n};\nexport {\n  Helmet,\n  HelmetData,\n  HelmetProvider\n};\n", "import React from 'react';\r\n\r\nconst LoadingSpinner: React.FC = () => {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-12\">\r\n      <div className=\"relative\">\r\n        <div className=\"w-8 h-8 border-4 border-[var(--color-border)] border-t-[var(--color-primary)] rounded-full animate-spin\"></div>\r\n        <span className=\"sr-only\">Carregando...</span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoadingSpinner;\r\n", "import i18n from 'i18next';\r\nimport { initReactI18next } from 'react-i18next';\r\nimport LanguageDetector from 'i18next-browser-languagedetector';\r\n\r\n// Importar traduções modulares organizadas\r\nimport ptBR from './locales/pt-BR';\r\nimport enUS from './locales/en-US';\r\nimport esES from './locales/es-ES';\r\n\r\n// Recursos organizados e modulares\r\nconst resources = {\r\n  'pt-BR': {\r\n    translation: ptBR\r\n  },\r\n  'en-US': {\r\n    translation: enUS\r\n  },\r\n  'es-ES': {\r\n    translation: esES\r\n  }\r\n};\r\n\r\n// Configuração robusta e simplificada do i18n\r\ni18n\r\n  .use(LanguageDetector)\r\n  .use(initReactI18next)\r\n  .init({\r\n    resources,\r\n    lng: 'pt-BR',\r\n    fallbackLng: 'pt-BR',\r\n\r\n    // DEBUG apenas em desenvolvimento\r\n    debug: import.meta.env.DEV,\r\n\r\n    // Configurações React\r\n    react: {\r\n      useSuspense: false\r\n    },\r\n\r\n    // Configurações de interpolação\r\n    interpolation: {\r\n      escapeValue: false\r\n    },\r\n\r\n    // Configurações básicas\r\n    defaultNS: 'translation',\r\n\r\n    // Configurações de detecção de idioma\r\n    detection: {\r\n      order: ['localStorage', 'navigator'],\r\n      caches: ['localStorage']\r\n    }\r\n  })\r\n  .then(() => {\r\n    // i18n initialized successfully - logs apenas em desenvolvimento\r\n    if (import.meta.env.DEV) {\r\n      console.log('✅ i18n initialized successfully');\r\n      console.log('🌐 Current language:', i18n.language);\r\n      console.log('📊 Available languages:', Object.keys(resources));\r\n    }\r\n  })\r\n  .catch((error) => {\r\n    console.error('❌ i18n initialization failed:', error);\r\n  });\r\n\r\nexport default i18n;\r\n", "// Português Brasileiro - Arquivo principal de traduções\nimport navigation from './navigation';\nimport profile from './profile';\nimport projects from './projects';\nimport backlog from './backlog';\nimport contact from './contact';\nimport accessibility from './accessibility';\nimport sound from './sound';\nimport tooltips from './tooltips';\nimport toasts from './toasts';\nimport seo from './seo';\nimport schema from './schema';\nimport alts from './alts';\nimport language from './language';\nimport theme from './theme';\nimport footer from './footer';\nimport feedback from './feedback';\nimport cookies from './cookies';\nimport common from './common';\n\nexport default {\n  navigation,\n  profile,\n  projects,\n  backlog,\n  contact,\n  accessibility,\n  sound,\n  tooltips,\n  toasts,\n  seo,\n  schema,\n  alts,\n  language,\n  theme,\n  footer,\n  feedback,\n  cookies,\n  common\n};\n", "export default {\n  profile: \"Perfil\",\n  projects: \"Projetos\",\n  backlog: \"Backlog\",\n  contact: \"<PERSON>tat<PERSON>\",\n  home: \"<PERSON><PERSON><PERSON>\",\n  goToProfile: \"Ir para sessão Perfil\",\n  goToProjetos: \"Ir para sessão Projetos\",\n  goToBacklog: \"Ir para sessão Backlog\",\n  goToContato: \"Ir para sessão Contato\",\n  menu: {\n    open: \"Abrir menu de navegação\",\n    close: \"Fechar menu de navegação\",\n    toggle: \"Alternar menu de navegação\"\n  },\n  settings: {\n    title: \"Configurações\",\n    open: \"Abrir configurações\",\n    close: \"<PERSON><PERSON>r configurações\",\n    description: \"Personalize sua experiência\",\n    theme: {\n      toggle: \"Alternar tema\",\n      lightMode: \"Modo claro ativado\",\n      darkMode: \"Modo escuro ativado\"\n    },\n    language: {\n      select: \"Selecionar idioma\"\n    },\n    sound: {\n      toggle: \"Alternar som\",\n      enabled: \"Som ativado\",\n      disabled: \"Som desativado\"\n    },\n    accessibility: {\n      menu: \"Menu de acessibilidade\",\n      description: \"Recursos de acessibilidade\"\n    },\n    feedback: {\n      open: \"Abrir feedback\",\n      description: \"Compartilhe sua experiência e sugestões\"\n    }\n  }\n};\n", "export default {\n  title: \"UX/Product Designer com foco em estratégia, impacto e experiência\",\n  bio: \"Sou UX/Product Designer com forte atuação no design de produtos digitais focados em experiência do usuário, conversão e impacto de negócio. Com background em Marketing Digital, SEO e IA, integro estratégia, design e usabilidade em processos contínuos de melhoria e inovação.\",\n  exploreProjects: \"Explore projetos\",\n  letsChat: \"Vamos Conversar\",\n  downloadCV: \"Download CV\",\n  linkedin: \"LinkedIn\",\n  name: \"Tarcisio Bispo de Araujo\",\n  ixdf: \"IxDF | Interaction Design Foundation\",\n  hero: {\n    greeting: \"Ol<PERSON>, eu sou\",\n    roles: {\n      uxDesigner: \"UX Designer\",\n      productDesigner: \"Product Designer\",\n      designStrategist: \"Design Strategist\",\n      interactionDesigner: \"Interaction Designer\"\n    }\n  }\n};\n", "export default {\r\n  title: \"Projetos\",\r\n  description: \"Casos reais de UX/Product Design com foco em estratégia, impacto e resultados mensuráveis para negócios e usuários.\",\r\n  overview: \"Visão Geral\",\r\n  discovery: \"Descoberta\",\r\n  solution: \"Solução\",\r\n  iteration: \"Iteração\",\r\n  outcomes: \"Resultados\",\r\n  insights: \"Insights\",\r\n  seeMore: \"Ver detalhes\",\r\n  seeLess: \"Ocultar detalhes\",\r\n  projectImage: \"Imagem do projeto\",\r\n  badges: {\r\n    usability: \"Usabilidade\",\r\n    informationArchitecture: \"Arquitetura da Informação\",\r\n    userTesting: \"Testes de Usuário\",\r\n    uxResearch: \"UX Research\",\r\n    journeyMapping: \"Mapa de Jornada\",\r\n    stakeholderManagement: \"Stakeholder Management\",\r\n    productStrategy: \"Product Strategy\",\r\n    seo: \"SEO\",\r\n    productValidation: \"Validação de Produto\",\r\n    visualDesign: \"Design Visual\",\r\n    communication: \"Comunicação\",\r\n    engagement: \"Engajamento\"\r\n  },\r\n  fgvLaw: {\r\n    title: \"FGV LAW\",\r\n    category: \"Navegação e Usabilidade\",\r\n    overview: \"Reestruturação da área de cursos jurídicos da Direito GV com foco em usabilidade e organização da informação para melhorar a experiência dos usuários.\",\r\n    discovery: \"Identifiquei que os usuários enfrentavam dificuldade para localizar e comparar cursos na plataforma da Direito GV.\",\r\n    solution: \"Projetei um novo painel com sistema de abas e filtros temáticos específicos para o contexto jurídico.\",\r\n    iteration: \"Após testes com usuários, simplificamos a terminologia dos filtros e ajustamos a hierarquia de informações.\",\r\n    outcomes: [\r\n      \"Melhora de 25% na visibilidade dos cursos e 35% mais interações com páginas específicas em 3 meses\",\r\n      \"Aumento de 18% na taxa de conversão de acessos em inscrições, passando de aproximadamente 8% para 10%\",\r\n      \"Redução de 30% no tempo médio de navegação, de cerca de 4 minutos para 3 minutos até a escolha do curso\"\r\n    ],\r\n    insights: \"A estrutura de navegação precisa guiar, não apenas mostrar. Clareza e agrupamento relevante influenciam diretamente a percepção de valor de um curso.\"\r\n  },\r\n  direitoGV: {\r\n    title: \"Pesquisa Direito FGV\",\r\n    category: \"Mapas, Fluxos e Pesquisa\",\r\n    overview: \"Reorganização da área de pesquisa para melhorar visibilidade dos projetos acadêmicos e facilitar acesso a pesquisadores.\",\r\n    discovery: \"A área de pesquisa estava fragmentada e pouco acessível. Pesquisadores tinham dificuldade para divulgar seus trabalhos e usuários externos não conseguiam encontrar informações relevantes sobre projetos em andamento.\",\r\n    solution: \"Desenvolvi uma nova arquitetura de informação com categorização por áreas temáticas, perfis de pesquisadores e linha do tempo de projetos. Criei também um sistema de busca avançada.\",\r\n    iteration: \"Realizamos testes com alunos, professores e pesquisadores. A navegação foi ajustada com base em feedback sobre nomenclatura e ordem de prioridades. Validei cada alteração com os stakeholders envolvidos.\",\r\n    outcomes: [\r\n      \"Redução de 65% no tempo de navegação para encontrar projetos específicos, de aproximadamente 6 minutos para 2 minutos\",\r\n      \"Aumento de 85% nas visitas às páginas de pesquisadores, passando de cerca de 150 para mais de 280 acessos mensais\",\r\n      \"Crescimento de 40% na consulta de publicações acadêmicas e 25% mais solicitações de parcerias em 5 meses\"\r\n    ],\r\n    insights: \"Áreas institucionais ganham relevância quando são navegáveis, atualizadas e refletidas de forma estratégica na arquitetura da informação.\"\r\n  },\r\n  taliparts: {\r\n    title: \"Taliparts\",\r\n    category: \"UX Estratégico + B2B\",\r\n    overview: \"Estruturação e validação digital da Taliparts para publicação de peças automotivas no Mercado Livre com foco em aprendizado rápido.\",\r\n    discovery: \"Conduzi benchmark detalhado com concorrentes do setor automotivo. Entrevistei mecânicos e lojistas, modelei personas e apliquei a Matriz CSD para identificar certezas, suposições e dúvidas no catálogo físico.\",\r\n    solution: \"Criei uma estratégia de validação com SEO para Mercado Livre, padronização visual de anúncios, categorização centrada no vocabulário do comprador e histórico de buscas. Também organizei KPIs e defini plano de priorização de produtos.\",\r\n    iteration: \"Testei produtos por blocos temáticos, monitorando cliques, perguntas e taxa de conversão. Refinei descrições, títulos e até a seleção de itens com base em performance real.\",\r\n    outcomes: [\r\n      \"Crescimento de 45% nas vendas dos produtos priorizados, gerando aproximadamente R$ 6.500 em receita adicional em 4 meses\",\r\n      \"Redução de 40% nas dúvidas dos compradores, diminuindo de cerca de 20 para 12 perguntas por produto publicado\",\r\n      \"Criação de processo que aumentou a eficiência de publicação em 50%, permitindo análise de mais de 80 produtos em 2 meses\"\r\n    ],\r\n    insights: \"Validar digitalmente com baixo custo é possível — e necessário. A lógica de produto precisa considerar contexto físico, vocabulário técnico e diferenciais percebidos pelo cliente.\"\r\n  },\r\n  tvInstitucional: {\r\n    title: \"FGV TV Institucional\",\r\n    category: \"Engajamento e Comunicação Visual\",\r\n    overview: \"Sistema visual para TVs no hall da FGV para comunicar eventos e atualizações institucionais de forma atrativa e dinâmica.\",\r\n    discovery: \"Alunos ignoravam murais físicos e e-mails institucionais. Identifiquei que a linguagem dos canais era desatualizada e pouco integrada com a rotina visual dos espaços.\",\r\n    solution: \"Implementei um painel digital com curadoria de conteúdo semanal, foco em ritmo visual e clareza imediata das mensagens. A plataforma foi pensada para ser automatizada, com flexibilidade de atualização remota.\",\r\n    iteration: \"Testamos tipos de animações, tempo de exibição e contraste. Ajustamos o calendário visual e otimizamos o layout com base em feedback de alunos e coordenação.\",\r\n    outcomes: [\r\n      \"Aumento de 35% na visibilidade de eventos institucionais, melhorando o conhecimento dos alunos sobre atividades do campus\",\r\n      \"Crescimento de 20% na participação em eventos, com maior engajamento da comunidade acadêmica\",\r\n      \"Melhora de 40% na retenção de informações institucionais comparado aos métodos anteriores de comunicação\"\r\n    ],\r\n    insights: \"Ambientes físicos também são interfaces. Quando bem projetados, informam, engajam e conectam — sem precisar de login.\"\r\n  }\r\n};\r\n", "export default {\r\n  title: \"Ciclo de Backlogs Estratégicos\",\r\n  description: \"Demonstração prática de como transformo desafios de negócio em soluções de UX mensuráveis. Cada caso apresenta metodologia aplicada, resultados alcançados e insights estratégicos que geram impacto real para stakeholders e usuários.\",\r\n  solution: \"Solução\",\r\n  result: \"Resultado\",\r\n  note: \"Nota\",\r\n  noItems: \"Nenhum item nesta página.\",\r\n  previous: \"Anterior\",\r\n  next: \"Próxima\",\r\n  items: [\r\n    {\r\n      challenge: \"Usuários abandonavam formulários longos sem completar o cadastro\",\r\n      solution: \"Implementei formulário em etapas com barra de progresso e validação em tempo real\",\r\n      result: \"Aumento de 40% na taxa de conclusão de cadastros em 2 semanas\",\r\n      note: \"Dividir tarefas complexas em etapas menores reduz a ansiedade e melhora a experiência.\"\r\n    },\r\n    {\r\n      challenge: \"Baixa adesão a recursos premium devido à falta de clareza sobre benefícios\",\r\n      solution: \"Criei onboarding interativo com demonstrações práticas dos recursos premium\",\r\n      result: \"Crescimento de 60% nas conversões para planos pagos em 1 mês\",\r\n      note: \"Mostrar valor através de experiência prática é mais eficaz que apenas listar funcionalidades.\"\r\n    },\r\n    {\r\n      challenge: \"Usuários não encontravam facilmente o suporte quando precisavam de ajuda\",\r\n      solution: \"Redesenhei sistema de ajuda contextual com chatbot inteligente e FAQ dinâmico\",\r\n      result: \"Redução de 50% nos tickets de suporte e aumento de 35% na satisfação\",\r\n      note: \"Ajuda contextual no momento certo previne frustrações e melhora a autonomia do usuário.\"\r\n    },\r\n    {\r\n      challenge: \"Interface complexa causava confusão em usuários iniciantes\",\r\n      solution: \"Desenvolvi modo simplificado com tutorial progressivo e tooltips adaptativos\",\r\n      result: \"Diminuição de 45% na taxa de abandono de novos usuários\",\r\n      note: \"Adaptar a complexidade ao nível de experiência do usuário melhora significativamente a adoção.\"\r\n    },\r\n    {\r\n      challenge: \"Processo de checkout tinha alta taxa de abandono no último passo\",\r\n      solution: \"Simplifiquei fluxo removendo campos desnecessários e adicionando opções de pagamento express\",\r\n      result: \"Aumento de 30% na conclusão de compras e redução de 25% no tempo de checkout\",\r\n      note: \"Cada campo extra no checkout é uma barreira potencial. Simplicidade gera conversão.\"\r\n    },\r\n    {\r\n      challenge: \"Usuários não percebiam atualizações importantes do produto\",\r\n      solution: \"Implementei sistema de notificações in-app com design não intrusivo e personalização\",\r\n      result: \"Melhoria de 55% no engajamento com novas funcionalidades\",\r\n      note: \"Comunicação eficaz sobre mudanças mantém usuários informados sem interromper o fluxo.\"\r\n    },\r\n    {\r\n      challenge: \"Dificuldade para encontrar conteúdo relevante em base de conhecimento extensa\",\r\n      solution: \"Criei sistema de busca inteligente com filtros contextuais e sugestões automáticas\",\r\n      result: \"Aumento de 70% na utilização da base de conhecimento e redução de 40% em consultas repetitivas\",\r\n      note: \"Busca eficiente transforma informação abundante em conhecimento acessível.\"\r\n    },\r\n    {\r\n      challenge: \"Baixo engajamento em funcionalidades colaborativas da plataforma\",\r\n      solution: \"Redesenhei interface de colaboração com indicadores visuais de atividade e gamificação sutil\",\r\n      result: \"Crescimento de 80% na colaboração entre usuários em 6 semanas\",\r\n      note: \"Tornar a colaboração visível e recompensadora incentiva naturalmente a participação.\"\r\n    },\r\n    {\r\n      challenge: \"Usuários perdiam progresso ao navegar entre seções do aplicativo\",\r\n      solution: \"Implementei sistema de auto-save com indicadores visuais de status de salvamento\",\r\n      result: \"Eliminação de 95% das reclamações sobre perda de dados\",\r\n      note: \"Confiança na tecnologia cresce quando o usuário vê que seu trabalho está sempre protegido.\"\r\n    },\r\n    {\r\n      challenge: \"Interface não responsiva causava frustração em dispositivos móveis\",\r\n      solution: \"Desenvolvi versão mobile-first com gestos intuitivos e navegação otimizada para toque\",\r\n      result: \"Aumento de 120% no uso mobile e melhoria de 4.2 para 4.7 na avaliação da app store\",\r\n      note: \"Design mobile-first garante experiência consistente independente do dispositivo usado.\"\r\n    },\r\n    {\r\n      challenge: \"Usuários com deficiência visual enfrentavam barreiras de acessibilidade\",\r\n      solution: \"Implementei padrões WCAG 2.1 com navegação por teclado e compatibilidade com leitores de tela\",\r\n      result: \"Aumento de 200% no uso por pessoas com deficiência e reconhecimento em prêmio de acessibilidade\",\r\n      note: \"Acessibilidade não é apenas compliance — é design inclusivo que beneficia todos os usuários.\"\r\n    },\r\n    {\r\n      challenge: \"Textos técnicos confundiam usuários não especializados\",\r\n      solution: \"Reescrevi microcopy com linguagem clara e adicionei explicações contextuais quando necessário\",\r\n      result: \"Redução de 60% em dúvidas de usuários e melhoria na percepção de facilidade de uso\",\r\n      note: \"Pequenas decisões no texto têm grande impacto na experiência de leitura e compreensão.\"\r\n    }\r\n  ]\r\n};\r\n", "export default {\n  title: \"Vamos conversar?\",\n  description: \"Estou sempre aberto a novas oportunidades e colaborações. Entre em contato para discutir projetos, parcerias ou apenas trocar ideias sobre UX e design de produtos.\",\n  form: {\n    name: \"Nome\",\n    namePlaceholder: \"Digite seu nome completo\",\n    email: \"E-mail\",\n    emailPlaceholder: \"Digite seu melhor e-mail\",\n    subject: \"Assunto\",\n    subjectPlaceholder: \"Sobre o que você gostaria de conversar?\",\n    message: \"Mensagem\",\n    messagePlaceholder: \"Conte-me sobre seu projeto, oportunidade ou como posso ajudar...\",\n    send: \"Enviar mensagem\",\n    sending: \"Enviando...\",\n    success: \"✅ Mensagem enviada com sucesso! Eu retorno em breve.\",\n    error: \"❌ Ops! Não conseguimos enviar sua mensagem. Tente novamente ou entre em contato diretamente.\",\n    nameRequired: \"Nome é obrigatório\",\n    emailRequired: \"E-mail é obrigatório\",\n    emailInvalid: \"E-mail inválido\",\n    subjectRequired: \"Assunto é obrigatório\",\n    messageRequired: \"Mensagem é obrigatória\",\n    messageMinLength: \"Mensagem deve ter pelo menos 10 caracteres\"\n  },\n  info: {\n    email: \"<EMAIL>\",\n    location: \"São Paulo, Brasil\",\n    availability: \"Disponível para projetos freelance e oportunidades full-time\"\n  },\n  social: {\n    linkedin: \"LinkedIn\",\n    whatsapp: \"WhatsApp\",\n    email: \"E-mail\"\n  }\n};\n", "export default {\n  title: \"Acessibilidade\",\n  subtitle: \"Personalize sua experiência de navegação\",\n  description: \"Configure opções de acessibilidade para melhorar sua experiência.\",\n  instructions: \"Use as opções abaixo para personalizar a interface.\",\n  close: \"<PERSON><PERSON><PERSON>\",\n  open: \"Abrir\",\n  menuLabel: \"Menu de Acessibilidade\",\n  menuTooltip: \"Configurações de acessibilidade (Shift + A)\",\n  fontSize: {\n    label: \"Tamanho da fonte\",\n    increase: \"Aumentar\",\n    decrease: \"Diminuir\",\n    reset: \"Resetar\",\n    increaseLabel: \"Aumentar tamanho da fonte\",\n    decreaseLabel: \"Diminuir tamanho da fonte\",\n    resetLabel: \"Resetar tamanho da fonte\"\n  },\n  contrast: {\n    label: \"Alto contraste\",\n    enable: \"Ativar alto contraste\",\n    disable: \"Desativar alto contraste\",\n    enabled: \"Alto contraste ativado\",\n    disabled: \"Alto contraste desativado\"\n  },\n  readingMode: {\n    label: \"Modo leitura\",\n    enable: \"Ativar modo leitura\",\n    disable: \"Desativar modo leitura\",\n    enabled: \"Modo leitura ativado\",\n    disabled: \"Modo leitura desativado\"\n  },\n  screenReader: {\n    label: \"Leitor de tela\",\n    enable: \"Ativar leitor de tela\",\n    disable: \"Desativar leitor de tela\",\n    enabled: \"Leitor de tela ativado\",\n    disabled: \"Leitor de tela desativado\"\n  },\n  reset: {\n    label: \"Resetar configurações\",\n    action: \"Configurações de acessibilidade resetadas\",\n    success: \"Configurações resetadas com sucesso\"\n  },\n  features: {\n    fontSize: \"Tamanho da fonte\",\n    fontSizeIncrease: \"Aumentar fonte\",\n    fontSizeDecrease: \"Diminuir fonte\",\n    fontSizeReset: \"Resetar fonte\",\n    contrast: \"Alto contraste\",\n    contrastEnable: \"Ativar contraste\",\n    contrastDisable: \"Desativar contraste\",\n    readingMode: \"Modo leitura\",\n    readingModeEnable: \"Ativar leitura\",\n    readingModeDisable: \"Desativar leitura\"\n  },\n  skipLinks: {\n    skipToContent: \"Pular para conteúdo principal\",\n    skipToNavigation: \"Pular para navegação\"\n  },\n  status: {\n    enabled: \"Ativado\",\n    disabled: \"Desativado\",\n    fontIncreased: \"Fonte aumentada\",\n    fontDecreased: \"Fonte diminuída\",\n    fontReset: \"Fonte resetada\",\n    contrastEnabled: \"Alto contraste ativado\",\n    contrastDisabled: \"Alto contraste desativado\",\n    readingEnabled: \"Modo leitura ativado\",\n    readingDisabled: \"Modo leitura desativado\"\n  }\n};\n", "export default {\n  enabled: \"Som ativado\",\n  disabled: \"Som desativado\",\n  enabledDesc: \"Efeitos sonoros ativados\",\n  disabledDesc: \"Efeitos sonoros desativados\",\n  enable: \"Ativar som\",\n  disable: \"Desativar som\",\n  toggle: \"Alternar som\",\n  changed: \"Som alterado para\",\n  volume: \"Volume\",\n  volumeControl: \"Ajustar volume do som\"\n};\n", "export default {\n  theme: {\n    light: \"Alternar para modo claro\",\n    dark: \"Alternar para modo escuro\",\n    system: \"Usar preferência do sistema\"\n  },\n  language: {\n    switch: \"Trocar idioma\",\n    current: \"Idioma atual\",\n    available: \"Idiomas disponíveis\"\n  },\n  navigation: {\n    home: \"Ir para início\",\n    profile: \"Ir para perfil\",\n    projects: \"Ver projetos\",\n    backlog: \"Ver backlog estratégico\",\n    contact: \"Entrar em contato\"\n  },\n  social: {\n    linkedin: \"Perfil no LinkedIn\",\n    email: \"Enviar e-mail\",\n    whatsapp: \"Conversar no WhatsApp\"\n  },\n  sound: {\n    enable: \"Ativar efeitos sonoros\",\n    disable: \"Desativar efeitos sonoros\"\n  },\n  actions: {\n    expand: \"Expandir detalhes\",\n    collapse: \"Recolher detalhes\",\n    download: \"Baixar arquivo\",\n    share: \"Compartilhar\",\n    copy: \"Copiar link\",\n    print: \"Imprimir página\",\n    backToTop: \"Voltar ao topo\"\n  }\n};\n", "export default {\n  success: {\n    title: \"Sucesso!\",\n    messageSent: \"Mensagem enviada com sucesso!\",\n    settingsSaved: \"Configurações salvas\",\n    linkCopied: \"Link copiado para área de transferência\",\n    themeChanged: \"Tema alterado\",\n    languageChanged: \"Idioma alterado\"\n  },\n  error: {\n    title: \"<PERSON>rro\",\n    messageNotSent: \"Erro ao enviar mensagem\",\n    networkError: \"Erro de conexão\",\n    genericError: \"Algo deu errado\",\n    tryAgain: \"Tente novamente\"\n  },\n  info: {\n    title: \"Informação\",\n    loading: \"Carregando...\",\n    processing: \"Processando...\",\n    saving: \"Salvando...\"\n  },\n  warning: {\n    title: \"Aviso\",\n    unsavedChanges: \"Você tem alterações não salvas\",\n    confirmAction: \"Tem certeza que deseja continuar?\"\n  }\n};\n", "export default {\n  title: \"Tarcisi<PERSON> Bispo - UX/Product Designer | Portfolio\",\n  description: \"UX/Product Designer especializado em estratégia, impacto e experiência do usuário. Veja meus projetos de design de produtos digitais e soluções UX.\",\n  keywords: \"UX Designer, Product Designer, Design de Produto, Experiência do Usuário, UI/UX, Portfolio, São Paulo\",\n  author: \"Tarcisio Bispo de Araujo\",\n  pages: {\n    home: {\n      title: \"Tarcisio Bispo - UX/Product Designer | Portfolio\",\n      description: \"UX/Product Designer focado em estratégia, impacto e experiência do usuário. Especialista em design de produtos digitais, conversão e impacto de negócio.\"\n    },\n    projects: {\n      title: \"Projetos - Tarcisi<PERSON> Bispo | UX Designer\",\n      description: \"Veja meus principais projetos de UX/Product Design: FGV LAW, Direito GV, Taliparts e FGV TV Institucional. Casos reais com resultados mensuráveis.\"\n    },\n    backlog: {\n      title: \"Backlog Estratégico - Tarc<PERSON><PERSON> | UX Designer\",\n      description: \"Demonstração prática de como transformo desafios de negócio em soluções UX mensuráveis. Metodologia aplicada, resultados alcançados e insights estratégicos.\"\n    },\n    contact: {\n      title: \"Contato - Tarcisio Bispo | UX Designer\",\n      description: \"Entre em contato para discutir projetos, parcerias ou oportunidades. Disponível para projetos freelance e oportunidades full-time.\"\n    }\n  }\n};\n", "export default {\n  person: {\n    name: \"<PERSON><PERSON><PERSON><PERSON> Bispo <PERSON>jo\",\n    jobTitle: \"UX/Product Designer\",\n    description: \"UX/Product Designer especializado em estratégia, impacto e experiência do usuário\",\n    location: \"São Paulo, Brasil\",\n    email: \"<EMAIL>\",\n    skills: [\"UX Design\", \"Product Design\", \"Estratégia de Design\", \"Pesquisa de Usuário\", \"Prototipagem\", \"Testes de Usabilidade\"]\n  },\n  organization: {\n    name: \"Tarcisio Bispo Portfolio\",\n    description: \"Portfolio profissional de UX/Product Design\",\n    location: \"São Paulo, Brasil\"\n  }\n};\n", "export default {\n  profile: {\n    photo: \"Foto de perfil de Tarcisio Bispo\",\n    ixdfLogo: \"Logo da Interaction Design Foundation\",\n    ixdfSeal: \"Selo de certificação IxDF\"\n  },\n  projects: {\n    fgvLaw: \"Screenshot do projeto FGV LAW - designers trabalhando em equipe no escritório\",\n    direitoGV: \"Screenshot do projeto Pesquisa Direito FGV - mesa de madeira com livro em frente a estante\",\n    taliparts: \"Screenshot do projeto Taliparts - e-commerce de peças automotivas\",\n    tvInstitucional: \"Screenshot do projeto FGV TV Institucional - placa branca pendurada na lateral de um edifício\"\n  },\n  icons: {\n    menu: \"Ícone de menu\",\n    close: \"Ícone de fechar\",\n    expand: \"Ícone de expandir\",\n    collapse: \"Ícone de recolher\",\n    external: \"Ícone de link externo\",\n    download: \"Ícone de download\",\n    email: \"Ícone de e-mail\",\n    phone: \"Ícone de telefone\",\n    linkedin: \"Ícone do LinkedIn\",\n    whatsapp: \"Ícone do WhatsApp\",\n    github: \"Ícone do GitHub\",\n    sun: \"Ícone de sol\",\n    moon: \"Ícone de lua\",\n    globe: \"Ícone de globo\",\n    volume: \"Ícone de volume\",\n    success: \"Ícone de sucesso\",\n    error: \"Ícone de erro\",\n    warning: \"Ícone de aviso\",\n    info: \"Ícone de informação\"\n  },\n  decorative: {\n    gradient: \"Gradiente decorativo\",\n    pattern: \"Padrão decorativo\",\n    divider: \"Divisor visual\",\n    background: \"Imagem de fundo decorativa\"\n  }\n};\n", "export default {\n  changed: \"Idioma alterado com sucesso\",\n  current: \"Idioma atual\",\n  available: \"Idiomas disponíveis\",\n  portuguese: \"Português\",\n  english: \"English\",\n  spanish: \"Español\",\n  select: \"Selecionar idioma\"\n};\n", "export default {\n  toggle: \"Alternar tema\",\n  changed: \"Tema alterado com sucesso\",\n  light: \"Modo claro ativado\",\n  dark: \"Modo escuro ativado\",\n  system: \"Usando preferência do sistema\"\n};\n", "export default {\n  copyright: \"© 2024 Tarcisio Bispo. Todos os direitos reservados.\",\n  title: \"UX/Product Designer\"\n};\n", "export default {\n  title: \"Feedback\",\n  subtitle: \"Sua opinião importa\",\n  description: \"Compartilhe sua experiência e sugestões\",\n  typeQuestion: \"Que tipo de feedback você gostaria de compartilhar?\",\n  close: \"<PERSON><PERSON><PERSON>\",\n  back: \"Voltar\",\n  send: \"Enviar feedback\",\n  sending: \"Enviando...\",\n  includeEmail: \"Incluir meu e-mail para resposta\",\n  privacyPolicy: \"Política de Privacidade\",\n\n  // Tipos de feedback\n  problem: \"Reportar Problema\",\n  idea: \"Compartilhar Ideia\",\n  praise: \"Dar Elogio\",\n\n  // Títulos específicos por tipo\n  problemTitle: \"Reportar Problema\",\n  ideaTitle: \"Compartilhar Ideia\",\n  praiseTitle: \"Dar Elogio\",\n  defaultTitle: \"Enviar Feedback\",\n\n  // Instruções específicas por tipo\n  problemInstruction: \"Descreva o problema que você encontrou em detalhes\",\n  ideaInstruction: \"Compartilhe sua ideia ou sugestão de melhoria\",\n  praiseInstruction: \"Conte-nos o que você gostou\",\n  defaultInstruction: \"Compartilhe seu feedback conosco\",\n\n  // Placeholders específicos por tipo\n  problemPlaceholder: \"Descreva o problema que você encontrou...\",\n  ideaPlaceholder: \"Compartilhe sua ideia ou sugestão...\",\n  praisePlaceholder: \"Conte-nos o que você gostou...\",\n  defaultPlaceholder: \"Compartilhe seu feedback...\",\n\n  // Validação\n  validation: {\n    messageRequired: \"Mensagem é obrigatória\",\n    messageMinLength: \"Mínimo 5 caracteres\",\n    emailInvalid: \"E-mail inválido\"\n  },\n\n  // Formulário\n  form: {\n    type: \"Tipo de feedback\",\n    message: \"Sua mensagem\",\n    email: \"Seu e-mail (opcional)\",\n    send: \"Enviar feedback\",\n    sending: \"Enviando...\",\n    success: \"✅ Obrigado pelo seu feedback! Sua opinião é muito importante para nós.\",\n    error: \"❌ Ops! Não conseguimos enviar seu feedback. Tente novamente ou entre em contato diretamente.\",\n    messageRequired: \"A mensagem é obrigatória\"\n  },\n\n  // Status messages\n  status: {\n    success: \"Obrigado pelo seu feedback!\",\n    error: \"Erro ao enviar feedback. Tente novamente.\",\n    sending: \"Enviando feedback...\"\n  },\n\n  // Tipos legados (manter compatibilidade)\n  types: {\n    bug: \"Reportar bug\",\n    suggestion: \"Sugestão\",\n    compliment: \"Elogio\",\n    other: \"Outro\"\n  }\n};\n", "export default {\n  title: \"Este site usa cookies\",\n  description: \"Usamos cookies para melhorar sua experiência, analisar o tráfego do site e personalizar conteúdo.\",\n  learnMore: \"Saiba mais\",\n  acceptAll: \"Aceitar todos\",\n  rejectAll: \"Rejeitar todos\",\n  managePreferences: \"Gerenciar preferências\",\n  savePreferences: \"Salvar preferências\",\n  required: \"Obrigatório\",\n  preferences: {\n    title: \"Preferências de Cookies\"\n  },\n  types: {\n    necessary: {\n      title: \"Cookies Necessários\",\n      description: \"Estes cookies são essenciais para o funcionamento do site e não podem ser desativados.\"\n    },\n    analytics: {\n      title: \"Cookies de Análise\",\n      description: \"Nos ajudam a entender como os visitantes interagem com o site coletando informações de forma anônima.\",\n      providers: \"Provedores\"\n    },\n    marketing: {\n      title: \"Cookies de Marketing\",\n      description: \"São usados para rastrear visitantes em sites para exibir anúncios relevantes e atraentes.\"\n    }\n  }\n};\n", "export default {\n  close: \"<PERSON><PERSON><PERSON>\",\n  open: \"<PERSON><PERSON><PERSON>\",\n  save: \"<PERSON><PERSON>\",\n  cancel: \"<PERSON><PERSON><PERSON>\",\n  confirm: \"Confirmar\",\n  yes: \"Sim\",\n  no: \"<PERSON><PERSON>\",\n  loading: \"Carregando...\",\n  error: \"<PERSON>rro\",\n  success: \"Sucesso\",\n  warning: \"Aviso\",\n  info: \"Informação\"\n};\n", "// English - Main translations file\nimport navigation from './navigation';\nimport profile from './profile';\nimport projects from './projects';\nimport backlog from './backlog';\nimport contact from './contact';\nimport accessibility from './accessibility';\nimport sound from './sound';\nimport tooltips from './tooltips';\nimport toasts from './toasts';\nimport seo from './seo';\nimport schema from './schema';\nimport alts from './alts';\nimport language from './language';\nimport theme from './theme';\nimport footer from './footer';\nimport feedback from './feedback';\nimport cookies from './cookies';\nimport common from './common';\n\nexport default {\n  navigation,\n  profile,\n  projects,\n  backlog,\n  contact,\n  accessibility,\n  sound,\n  tooltips,\n  toasts,\n  seo,\n  schema,\n  alts,\n  language,\n  theme,\n  footer,\n  feedback,\n  cookies,\n  common\n};\n", "export default {\n  profile: \"Profile\",\n  projects: \"Projects\",\n  backlog: \"Backlog\",\n  contact: \"Contact\",\n  home: \"Home\",\n  goToProfile: \"Go to Profile section\",\n  goToProjetos: \"Go to Projects section\",\n  goToBacklog: \"Go to Backlog section\",\n  goToContato: \"Go to Contact section\",\n  menu: {\n    open: \"Open navigation menu\",\n    close: \"Close navigation menu\",\n    toggle: \"Toggle navigation menu\"\n  },\n  settings: {\n    title: \"Settings\",\n    open: \"Open settings\",\n    close: \"Close settings\",\n    description: \"Customize your experience\",\n    theme: {\n      toggle: \"Toggle theme\",\n      lightMode: \"Light mode enabled\",\n      darkMode: \"Dark mode enabled\"\n    },\n    language: {\n      select: \"Select language\"\n    },\n    sound: {\n      toggle: \"Toggle sound\",\n      enabled: \"Sound enabled\",\n      disabled: \"Sound disabled\"\n    },\n    accessibility: {\n      menu: \"Accessibility menu\",\n      description: \"Accessibility features\"\n    },\n    feedback: {\n      open: \"Open feedback\",\n      description: \"Share your experience and suggestions\"\n    }\n  }\n};\n", "export default {\n  title: \"UX/Product Designer focused on strategy, impact and experience\",\n  bio: \"I'm a UX/Product Designer with strong expertise in designing digital products focused on user experience, conversion and business impact. With a background in Digital Marketing, SEO and AI, I integrate strategy, design and usability in continuous improvement and innovation processes.\",\n  exploreProjects: \"Explore projects\",\n  letsChat: \"Let's Chat\",\n  downloadCV: \"Download CV\",\n  linkedin: \"LinkedIn\",\n  name: \"<PERSON><PERSON><PERSON><PERSON> Bispo de Araujo\",\n  ixdf: \"IxDF | Interaction Design Foundation\",\n  hero: {\n    greeting: \"Hello, I'm\",\n    roles: {\n      uxDesigner: \"UX Designer\",\n      productDesigner: \"Product Designer\",\n      designStrategist: \"Design Strategist\",\n      interactionDesigner: \"Interaction Designer\"\n    }\n  }\n};\n", "export default {\r\n  title: \"Projects\",\r\n  description: \"Real UX/Product Design cases focused on strategy, impact and measurable results for businesses and users.\",\r\n  overview: \"Overview\",\r\n  discovery: \"Discovery\",\r\n  solution: \"Solution\",\r\n  iteration: \"Iteration\",\r\n  outcomes: \"Outcomes\",\r\n  insights: \"Insights\",\r\n  seeMore: \"See details\",\r\n  seeLess: \"Hide details\",\r\n  projectImage: \"Project image\",\r\n  badges: {\r\n    usability: \"Usability\",\r\n    informationArchitecture: \"Information Architecture\",\r\n    userTesting: \"User Testing\",\r\n    uxResearch: \"UX Research\",\r\n    journeyMapping: \"Journey Mapping\",\r\n    stakeholderManagement: \"Stakeholder Management\",\r\n    productStrategy: \"Product Strategy\",\r\n    seo: \"SEO\",\r\n    productValidation: \"Product Validation\",\r\n    visualDesign: \"Visual Design\",\r\n    communication: \"Communication\",\r\n    engagement: \"Engagement\"\r\n  },\r\n  fgvLaw: {\r\n    title: \"FGV LAW\",\r\n    category: \"Navigation and Usability\",\r\n    overview: \"Restructuring of the legal courses area at Direito GV focusing on usability and information organization to improve user experience.\",\r\n    discovery: \"I identified that users faced difficulty locating and comparing courses on the Direito GV platform.\",\r\n    solution: \"I designed a new panel with tab system and thematic filters specific to the legal context.\",\r\n    iteration: \"After user testing, we simplified filter terminology and adjusted information hierarchy.\",\r\n    outcomes: [\r\n      \"25% improvement in course visibility and 35% more interactions with specific pages in 3 months\",\r\n      \"18% increase in conversion rate from visits to enrollments, rising from approximately 8% to 10%\",\r\n      \"30% reduction in average navigation time, from about 4 minutes to 3 minutes until course selection\"\r\n    ],\r\n    insights: \"Navigation structure needs to guide, not just show. Clarity and relevant grouping directly influence the perception of a course's value.\"\r\n  },\r\n  direitoGV: {\r\n    title: \"FGV Law Research\",\r\n    category: \"Maps, Flows and Research\",\r\n    overview: \"Reorganization of the research area to improve visibility of academic projects and facilitate access to researchers.\",\r\n    discovery: \"The research area was fragmented and poorly accessible. Researchers had difficulty promoting their work and external users couldn't find relevant information about ongoing projects.\",\r\n    solution: \"I developed a new information architecture with categorization by thematic areas, researcher profiles and project timeline. I also created an advanced search system.\",\r\n    iteration: \"We conducted tests with students, professors and researchers. Navigation was adjusted based on feedback about nomenclature and priority order. I validated each change with involved stakeholders.\",\r\n    outcomes: [\r\n      \"65% reduction in navigation time to find specific projects, from approximately 6 minutes to 2 minutes\",\r\n      \"85% increase in visits to researcher pages, rising from about 150 to over 280 monthly visits\",\r\n      \"40% growth in academic publication consultation and 25% more partnership requests in 5 months\"\r\n    ],\r\n    insights: \"Institutional areas gain relevance when they are navigable, updated and strategically reflected in information architecture.\"\r\n  },\r\n  taliparts: {\r\n    title: \"Taliparts\",\r\n    category: \"Strategic UX + B2B\",\r\n    overview: \"Structuring and digital validation of Taliparts for publishing automotive parts on Mercado Livre with focus on rapid learning.\",\r\n    discovery: \"I conducted detailed benchmark with automotive sector competitors. I interviewed mechanics and store owners, modeled personas and applied CSD Matrix to identify certainties, assumptions and doubts in the physical catalog.\",\r\n    solution: \"I created a validation strategy with SEO for Mercado Livre, visual standardization of ads, categorization centered on buyer vocabulary and search history. I also organized KPIs and defined product prioritization plan.\",\r\n    iteration: \"I tested products by thematic blocks, monitoring clicks, questions and conversion rate. I refined descriptions, titles and even item selection based on real performance.\",\r\n    outcomes: [\r\n      \"45% growth in sales of prioritized products, generating approximately R$ 6,500 in additional revenue in 4 months\",\r\n      \"40% reduction in buyer doubts, decreasing from about 20 to 12 questions per published product\",\r\n      \"Creation of process that increased publication efficiency by 50%, allowing analysis of over 80 products in 2 months\"\r\n    ],\r\n    insights: \"Digital validation at low cost is possible — and necessary. Product logic needs to consider physical context, technical vocabulary and differentials perceived by the customer.\"\r\n  },\r\n  tvInstitucional: {\r\n    title: \"FGV Institutional TV\",\r\n    category: \"Engagement and Visual Communication\",\r\n    overview: \"Visual system for TVs in FGV hall to communicate events and institutional updates in an attractive and dynamic way.\",\r\n    discovery: \"Students ignored physical bulletin boards and institutional emails. I identified that the language of channels was outdated and poorly integrated with the visual routine of spaces.\",\r\n    solution: \"I implemented a digital panel with weekly content curation, focus on visual rhythm and immediate clarity of messages. The platform was designed to be automated, with remote update flexibility.\",\r\n    iteration: \"We tested types of animations, display time and contrast. We adjusted the visual calendar and optimized layout based on feedback from students and coordination.\",\r\n    outcomes: [\r\n      \"35% increase in institutional event visibility, improving student knowledge about campus activities\",\r\n      \"20% growth in event participation, with greater engagement from the academic community\",\r\n      \"40% improvement in institutional information retention compared to previous communication methods\"\r\n    ],\r\n    insights: \"Physical environments are also interfaces. When well designed, they inform, engage and connect — without needing login.\"\r\n  }\r\n};\r\n", "export default {\r\n  title: \"Strategic Backlog Cycle\",\r\n  description: \"Practical demonstration of how I transform business challenges into measurable UX solutions. Each case presents applied methodology, achieved results and strategic insights that generate real impact for stakeholders and users.\",\r\n  solution: \"Solution\",\r\n  result: \"Result\",\r\n  note: \"Note\",\r\n  noItems: \"No items on this page.\",\r\n  previous: \"Previous\",\r\n  next: \"Next\",\r\n  items: [\r\n    {\r\n      challenge: \"Users abandoned long forms without completing registration\",\r\n      solution: \"Implemented step-by-step form with progress bar and real-time validation\",\r\n      result: \"40% increase in registration completion rate in 2 weeks\",\r\n      note: \"Breaking complex tasks into smaller steps reduces anxiety and improves experience.\"\r\n    },\r\n    {\r\n      challenge: \"Low adoption of premium features due to lack of clarity about benefits\",\r\n      solution: \"Created interactive onboarding with practical demonstrations of premium features\",\r\n      result: \"60% growth in conversions to paid plans in 1 month\",\r\n      note: \"Showing value through practical experience is more effective than just listing features.\"\r\n    },\r\n    {\r\n      challenge: \"Users couldn't easily find support when they needed help\",\r\n      solution: \"Redesigned contextual help system with intelligent chatbot and dynamic FAQ\",\r\n      result: \"50% reduction in support tickets and 35% increase in satisfaction\",\r\n      note: \"Contextual help at the right moment prevents frustrations and improves user autonomy.\"\r\n    },\r\n    {\r\n      challenge: \"Complex interface caused confusion in beginner users\",\r\n      solution: \"Developed simplified mode with progressive tutorial and adaptive tooltips\",\r\n      result: \"45% decrease in new user abandonment rate\",\r\n      note: \"Adapting complexity to user experience level significantly improves adoption.\"\r\n    },\r\n    {\r\n      challenge: \"Checkout process had high abandonment rate at the last step\",\r\n      solution: \"Simplified flow by removing unnecessary fields and adding express payment options\",\r\n      result: \"30% increase in purchase completion and 25% reduction in checkout time\",\r\n      note: \"Each extra field in checkout is a potential barrier. Simplicity generates conversion.\"\r\n    },\r\n    {\r\n      challenge: \"Users didn't notice important product updates\",\r\n      solution: \"Implemented in-app notification system with non-intrusive design and personalization\",\r\n      result: \"55% improvement in engagement with new features\",\r\n      note: \"Effective communication about changes keeps users informed without interrupting flow.\"\r\n    },\r\n    {\r\n      challenge: \"Difficulty finding relevant content in extensive knowledge base\",\r\n      solution: \"Created intelligent search system with contextual filters and automatic suggestions\",\r\n      result: \"70% increase in knowledge base usage and 40% reduction in repetitive queries\",\r\n      note: \"Efficient search transforms abundant information into accessible knowledge.\"\r\n    },\r\n    {\r\n      challenge: \"Low engagement in platform's collaborative features\",\r\n      solution: \"Redesigned collaboration interface with visual activity indicators and subtle gamification\",\r\n      result: \"80% growth in user collaboration in 6 weeks\",\r\n      note: \"Making collaboration visible and rewarding naturally encourages participation.\"\r\n    },\r\n    {\r\n      challenge: \"Users lost progress when navigating between app sections\",\r\n      solution: \"Implemented auto-save system with visual indicators of save status\",\r\n      result: \"Elimination of 95% of complaints about data loss\",\r\n      note: \"Trust in technology grows when users see their work is always protected.\"\r\n    },\r\n    {\r\n      challenge: \"Non-responsive interface caused frustration on mobile devices\",\r\n      solution: \"Developed mobile-first version with intuitive gestures and touch-optimized navigation\",\r\n      result: \"120% increase in mobile usage and improvement from 4.2 to 4.7 in app store rating\",\r\n      note: \"Mobile-first design ensures consistent experience regardless of device used.\"\r\n    },\r\n    {\r\n      challenge: \"Users with visual impairments faced accessibility barriers\",\r\n      solution: \"Implemented WCAG 2.1 standards with keyboard navigation and screen reader compatibility\",\r\n      result: \"200% increase in usage by people with disabilities and recognition in accessibility award\",\r\n      note: \"Accessibility is not just compliance — it's inclusive design that benefits all users.\"\r\n    },\r\n    {\r\n      challenge: \"Technical texts confused non-specialized users\",\r\n      solution: \"Rewrote microcopy with clear language and added contextual explanations when necessary\",\r\n      result: \"60% reduction in user doubts and improvement in perceived ease of use\",\r\n      note: \"Small decisions in text have great impact on reading experience and comprehension.\"\r\n    }\r\n  ]\r\n};\r\n", "export default {\n  title: \"Let's talk?\",\n  description: \"I'm always open to new opportunities and collaborations. Get in touch to discuss projects, partnerships or just exchange ideas about UX and product design.\",\n  form: {\n    name: \"Name\",\n    namePlaceholder: \"Enter your full name\",\n    email: \"<PERSON><PERSON>\",\n    emailPlaceholder: \"Enter your best email\",\n    subject: \"Subject\",\n    subjectPlaceholder: \"What would you like to talk about?\",\n    message: \"Message\",\n    messagePlaceholder: \"Tell me about your project, opportunity or how I can help...\",\n    send: \"Send message\",\n    sending: \"Sending...\",\n    success: \"✅ Message sent successfully! I'll get back to you soon.\",\n    error: \"❌ Oops! We couldn't send your message. Try again or contact directly.\",\n    nameRequired: \"Name is required\",\n    emailRequired: \"Email is required\",\n    emailInvalid: \"Invalid email\",\n    subjectRequired: \"Subject is required\",\n    messageRequired: \"Message is required\",\n    messageMinLength: \"Message must be at least 10 characters\"\n  },\n  info: {\n    email: \"<EMAIL>\",\n    location: \"São Paulo, Brazil\",\n    availability: \"Available for freelance projects and full-time opportunities\"\n  },\n  social: {\n    linkedin: \"LinkedIn\",\n    whatsapp: \"WhatsApp\",\n    email: \"Email\"\n  }\n};\n", "export default {\n  title: \"Accessibility\",\n  subtitle: \"Customize your browsing experience\",\n  description: \"Configure accessibility options to improve your experience.\",\n  instructions: \"Use the options below to customize the interface.\",\n  close: \"Close\",\n  open: \"Open\",\n  menuLabel: \"Accessibility Menu\",\n  menuTooltip: \"Accessibility settings (Shift + A)\",\n  fontSize: {\n    label: \"Font size\",\n    increase: \"Increase\",\n    decrease: \"Decrease\",\n    reset: \"Reset\",\n    increaseLabel: \"Increase font size\",\n    decreaseLabel: \"Decrease font size\",\n    resetLabel: \"Reset font size\"\n  },\n  contrast: {\n    label: \"High contrast\",\n    enable: \"Enable high contrast\",\n    disable: \"Disable high contrast\",\n    enabled: \"High contrast enabled\",\n    disabled: \"High contrast disabled\"\n  },\n  readingMode: {\n    label: \"Reading mode\",\n    enable: \"Enable reading mode\",\n    disable: \"Disable reading mode\",\n    enabled: \"Reading mode enabled\",\n    disabled: \"Reading mode disabled\"\n  },\n  screenReader: {\n    label: \"Screen reader\",\n    enable: \"Enable screen reader\",\n    disable: \"Disable screen reader\",\n    enabled: \"Screen reader enabled\",\n    disabled: \"Screen reader disabled\"\n  },\n  reset: {\n    label: \"Reset settings\",\n    action: \"Accessibility settings reset\",\n    success: \"Settings reset successfully\"\n  },\n  features: {\n    fontSize: \"Font size\",\n    fontSizeIncrease: \"Increase font\",\n    fontSizeDecrease: \"Decrease font\",\n    fontSizeReset: \"Reset font\",\n    contrast: \"High contrast\",\n    contrastEnable: \"Enable contrast\",\n    contrastDisable: \"Disable contrast\",\n    readingMode: \"Reading mode\",\n    readingModeEnable: \"Enable reading\",\n    readingModeDisable: \"Disable reading\"\n  },\n  skipLinks: {\n    skipToContent: \"Skip to main content\",\n    skipToNavigation: \"Skip to navigation\"\n  },\n  status: {\n    enabled: \"Enabled\",\n    disabled: \"Disabled\",\n    fontIncreased: \"Font increased\",\n    fontDecreased: \"Font decreased\",\n    fontReset: \"Font reset\",\n    contrastEnabled: \"High contrast enabled\",\n    contrastDisabled: \"High contrast disabled\",\n    readingEnabled: \"Reading mode enabled\",\n    readingDisabled: \"Reading mode disabled\"\n  }\n};\n", "export default {\n  enabled: \"Sound enabled\",\n  disabled: \"Sound disabled\",\n  enabledDesc: \"Sound effects enabled\",\n  disabledDesc: \"Sound effects disabled\",\n  enable: \"Enable sound\",\n  disable: \"Disable sound\",\n  toggle: \"Toggle sound\",\n  changed: \"Sound changed to\",\n  volume: \"Volume\",\n  volumeControl: \"Adjust sound volume\"\n};\n", "export default {\n  theme: {\n    light: \"Switch to light mode\",\n    dark: \"Switch to dark mode\",\n    system: \"Use system preference\"\n  },\n  language: {\n    switch: \"Switch language\",\n    current: \"Current language\",\n    available: \"Available languages\"\n  },\n  navigation: {\n    home: \"Go to home\",\n    profile: \"Go to profile\",\n    projects: \"View projects\",\n    backlog: \"View strategic backlog\",\n    contact: \"Get in touch\"\n  },\n  social: {\n    linkedin: \"LinkedIn profile\",\n    email: \"Send email\",\n    whatsapp: \"Chat on WhatsApp\"\n  },\n  sound: {\n    enable: \"Enable sound effects\",\n    disable: \"Disable sound effects\"\n  },\n  actions: {\n    expand: \"Expand details\",\n    collapse: \"Collapse details\",\n    download: \"Download file\",\n    share: \"Share\",\n    copy: \"Copy link\",\n    print: \"Print page\",\n    backToTop: \"Back to top\"\n  }\n};\n", "export default {\n  success: {\n    title: \"Success!\",\n    messageSent: \"Message sent successfully!\",\n    settingsSaved: \"Settings saved\",\n    linkCopied: \"Link copied to clipboard\",\n    themeChanged: \"Theme changed\",\n    languageChanged: \"Language changed\"\n  },\n  error: {\n    title: \"Error\",\n    messageNotSent: \"Error sending message\",\n    networkError: \"Connection error\",\n    genericError: \"Something went wrong\",\n    tryAgain: \"Try again\"\n  },\n  info: {\n    title: \"Information\",\n    loading: \"Loading...\",\n    processing: \"Processing...\",\n    saving: \"Saving...\"\n  },\n  warning: {\n    title: \"Warning\",\n    unsavedChanges: \"You have unsaved changes\",\n    confirmAction: \"Are you sure you want to continue?\"\n  }\n};\n", "export default {\n  title: \"Tarcisio Bispo - UX/Product Designer | Portfolio\",\n  description: \"UX/Product Designer specialized in strategy, impact and user experience. See my digital product design projects and UX solutions.\",\n  keywords: \"UX Designer, Product Designer, Product Design, User Experience, UI/UX, Portfolio, São Paulo\",\n  author: \"Tarcisio Bispo de Araujo\",\n  pages: {\n    home: {\n      title: \"Tarcisio Bispo - UX/Product Designer | Portfolio\",\n      description: \"UX/Product Designer focused on strategy, impact and user experience. Expert in digital product design, conversion and business impact.\"\n    },\n    projects: {\n      title: \"Projects - Tarcisio Bispo | UX Designer\",\n      description: \"See my main UX/Product Design projects: FGV LAW, Direito GV, Taliparts and FGV Institutional TV. Real cases with measurable results.\"\n    },\n    backlog: {\n      title: \"Strategic Backlog - Tarcisio Bispo | UX Designer\",\n      description: \"Practical demonstration of how I transform business challenges into measurable UX solutions. Applied methodology, achieved results and strategic insights.\"\n    },\n    contact: {\n      title: \"Contact - Tarcisio Bispo | UX Designer\",\n      description: \"Get in touch to discuss projects, partnerships or opportunities. Available for freelance projects and full-time opportunities.\"\n    }\n  }\n};\n", "export default {\n  person: {\n    name: \"<PERSON><PERSON>isi<PERSON> Bispo <PERSON>\",\n    jobTitle: \"UX/Product Designer\",\n    description: \"UX/Product Designer specialized in strategy, impact and user experience\",\n    location: \"São Paulo, Brazil\",\n    email: \"<EMAIL>\",\n    skills: [\"UX Design\", \"Product Design\", \"Design Strategy\", \"User Research\", \"Prototyping\", \"Usability Testing\"]\n  },\n  organization: {\n    name: \"Tarcisio Bispo Portfolio\",\n    description: \"Professional UX/Product Design portfolio\",\n    location: \"São Paulo, Brazil\"\n  }\n};\n", "export default {\n  profile: {\n    photo: \"Tarcisio Bispo profile photo\",\n    ixdfLogo: \"Interaction Design Foundation logo\",\n    ixdfSeal: \"IxDF certification seal\"\n  },\n  projects: {\n    fgvLaw: \"FGV LAW project screenshot - designers working as a team in the office\",\n    direitoGV: \"FGV Law Research project screenshot - wooden table with book in front of bookshelf\",\n    taliparts: \"Taliparts project screenshot - automotive parts e-commerce\",\n    tvInstitucional: \"FGV Institutional TV project screenshot - white sign hanging on the side of a building\"\n  },\n  icons: {\n    menu: \"Menu icon\",\n    close: \"Close icon\",\n    expand: \"Expand icon\",\n    collapse: \"Collapse icon\",\n    external: \"External link icon\",\n    download: \"Download icon\",\n    email: \"Email icon\",\n    phone: \"Phone icon\",\n    linkedin: \"LinkedIn icon\",\n    whatsapp: \"WhatsApp icon\",\n    github: \"GitHub icon\",\n    sun: \"Sun icon\",\n    moon: \"Moon icon\",\n    globe: \"Globe icon\",\n    volume: \"Volume icon\",\n    success: \"Success icon\",\n    error: \"Error icon\",\n    warning: \"Warning icon\",\n    info: \"Information icon\"\n  },\n  decorative: {\n    gradient: \"Decorative gradient\",\n    pattern: \"Decorative pattern\",\n    divider: \"Visual divider\",\n    background: \"Decorative background image\"\n  }\n};\n", "export default {\n  changed: \"Language changed successfully\",\n  current: \"Current language\",\n  available: \"Available languages\",\n  portuguese: \"Português\",\n  english: \"English\",\n  spanish: \"Español\",\n  select: \"Select language\"\n};\n", "export default {\n  toggle: \"Toggle theme\",\n  changed: \"Theme changed successfully\",\n  light: \"Light mode enabled\",\n  dark: \"Dark mode enabled\",\n  system: \"Using system preference\"\n};\n", "export default {\n  copyright: \"© 2024 Tarcisio Bispo. All rights reserved.\",\n  title: \"UX/Product Designer\"\n};\n", "export default {\n  title: \"Feedback\",\n  subtitle: \"Your opinion matters\",\n  description: \"Share your experience and suggestions\",\n  typeQuestion: \"What type of feedback would you like to share?\",\n  close: \"Close\",\n  back: \"Back\",\n  send: \"Send feedback\",\n  sending: \"Sending...\",\n  includeEmail: \"Include my email for response\",\n  privacyPolicy: \"Privacy Policy\",\n\n  // Feedback types\n  problem: \"Report Problem\",\n  idea: \"Share Idea\",\n  praise: \"Give Praise\",\n\n  // Specific titles by type\n  problemTitle: \"Report Problem\",\n  ideaTitle: \"Share Idea\",\n  praiseTitle: \"Give Praise\",\n  defaultTitle: \"Send Feedback\",\n\n  // Specific instructions by type\n  problemInstruction: \"Describe the problem you found in detail\",\n  ideaInstruction: \"Share your idea or improvement suggestion\",\n  praiseInstruction: \"Tell us what you liked\",\n  defaultInstruction: \"Share your feedback with us\",\n\n  // Specific placeholders by type\n  problemPlaceholder: \"Describe the problem you found...\",\n  ideaPlaceholder: \"Share your idea or suggestion...\",\n  praisePlaceholder: \"Tell us what you liked...\",\n  defaultPlaceholder: \"Share your feedback...\",\n\n  // Validation\n  validation: {\n    messageRequired: \"Message is required\",\n    messageMinLength: \"Minimum 5 characters\",\n    emailInvalid: \"Invalid email\"\n  },\n\n  // Form\n  form: {\n    type: \"Feedback type\",\n    message: \"Your message\",\n    email: \"Your email (optional)\",\n    send: \"Send feedback\",\n    sending: \"Sending...\",\n    success: \"✅ Thank you for your feedback! Your opinion is very important to us.\",\n    error: \"❌ Oops! We couldn't send your feedback. Try again or contact directly.\",\n    messageRequired: \"Message is required\"\n  },\n\n  // Status messages\n  status: {\n    success: \"Thank you for your feedback!\",\n    error: \"Error sending feedback. Please try again.\",\n    sending: \"Sending feedback...\"\n  },\n\n  // Legacy types (maintain compatibility)\n  types: {\n    bug: \"Report bug\",\n    suggestion: \"Suggestion\",\n    compliment: \"Compliment\",\n    other: \"Other\"\n  }\n};\n", "export default {\n  title: \"This site uses cookies\",\n  description: \"We use cookies to improve your experience, analyze site traffic and personalize content.\",\n  learnMore: \"Learn more\",\n  acceptAll: \"Accept all\",\n  rejectAll: \"Reject all\",\n  managePreferences: \"Manage preferences\",\n  savePreferences: \"Save preferences\",\n  required: \"Required\",\n  preferences: {\n    title: \"Cookie Preferences\"\n  },\n  types: {\n    necessary: {\n      title: \"Necessary Cookies\",\n      description: \"These cookies are essential for the website to function and cannot be disabled.\"\n    },\n    analytics: {\n      title: \"Analytics Cookies\",\n      description: \"Help us understand how visitors interact with the site by collecting information anonymously.\",\n      providers: \"Providers\"\n    },\n    marketing: {\n      title: \"Marketing Cookies\",\n      description: \"Used to track visitors across websites to display relevant and engaging ads.\"\n    }\n  }\n};\n", "export default {\n  close: \"Close\",\n  open: \"Open\",\n  save: \"Save\",\n  cancel: \"Cancel\",\n  confirm: \"Confirm\",\n  yes: \"Yes\",\n  no: \"No\",\n  loading: \"Loading...\",\n  error: \"Error\",\n  success: \"Success\",\n  warning: \"Warning\",\n  info: \"Information\"\n};\n", "// Español - Archivo principal de traducciones\nimport navigation from './navigation';\nimport profile from './profile';\nimport projects from './projects';\nimport backlog from './backlog';\nimport contact from './contact';\nimport accessibility from './accessibility';\nimport sound from './sound';\nimport tooltips from './tooltips';\nimport toasts from './toasts';\nimport seo from './seo';\nimport schema from './schema';\nimport alts from './alts';\nimport language from './language';\nimport theme from './theme';\nimport footer from './footer';\nimport feedback from './feedback';\nimport cookies from './cookies';\nimport common from './common';\n\nexport default {\n  navigation,\n  profile,\n  projects,\n  backlog,\n  contact,\n  accessibility,\n  sound,\n  tooltips,\n  toasts,\n  seo,\n  schema,\n  alts,\n  language,\n  theme,\n  footer,\n  feedback,\n  cookies,\n  common\n};\n", "export default {\n  profile: \"Perfil\",\n  projects: \"Proyectos\",\n  backlog: \"Backlog\",\n  contact: \"Contacto\",\n  home: \"Inicio\",\n  goToProfile: \"Ir a sección Perfil\",\n  goToProjetos: \"Ir a sección Proyectos\",\n  goToBacklog: \"Ir a sección Backlog\",\n  goToContato: \"Ir a sección Contacto\",\n  menu: {\n    open: \"Abrir menú de navegación\",\n    close: \"Cerrar menú de navegación\",\n    toggle: \"Alternar menú de navegación\"\n  },\n  settings: {\n    title: \"Configuraciones\",\n    open: \"Abrir configuraciones\",\n    close: \"Cerrar configuraciones\",\n    description: \"Personaliza tu experiencia\",\n    theme: {\n      toggle: \"Alternar tema\",\n      lightMode: \"Modo claro activado\",\n      darkMode: \"Modo oscuro activado\"\n    },\n    language: {\n      select: \"Seleccionar idioma\"\n    },\n    sound: {\n      toggle: \"Alternar sonido\",\n      enabled: \"Sonido activado\",\n      disabled: \"Sonido desactivado\"\n    },\n    accessibility: {\n      menu: \"Menú de accesibilidad\",\n      description: \"Funciones de accesibilidad\"\n    },\n    feedback: {\n      open: \"Abrir feedback\",\n      description: \"Comparte tu experiencia y sugerencias\"\n    }\n  }\n};\n", "export default {\n  title: \"UX/Product Designer enfocado en estrategia, impacto y experiencia\",\n  bio: \"Soy UX/Product Designer con fuerte experiencia en el diseño de productos digitales enfocados en experiencia del usuario, conversión e impacto empresarial. Con experiencia en Marketing Digital, SEO e IA, integro estrategia, diseño y usabilidad en procesos continuos de mejora e innovación.\",\n  exploreProjects: \"Explorar proyectos\",\n  letsChat: \"Conversemos\",\n  downloadCV: \"Descargar CV\",\n  linkedin: \"LinkedIn\",\n  name: \"<PERSON>rcisio Bispo de Araujo\",\n  ixdf: \"IxDF | Interaction Design Foundation\",\n  hero: {\n    greeting: \"<PERSON><PERSON>, soy\",\n    roles: {\n      uxDesigner: \"UX Designer\",\n      productDesigner: \"Product Designer\",\n      designStrategist: \"Design Strategist\",\n      interactionDesigner: \"Interaction Designer\"\n    }\n  }\n};\n", "export default {\r\n  title: \"Proyectos\",\r\n  description: \"Casos reales de UX/Product Design enfocados en estrategia, impacto y resultados medibles para empresas y usuarios.\",\r\n  overview: \"Resumen\",\r\n  discovery: \"Descubrimiento\",\r\n  solution: \"Solución\",\r\n  iteration: \"Iteración\",\r\n  outcomes: \"Resultados\",\r\n  insights: \"Insights\",\r\n  seeMore: \"Ver detalles\",\r\n  seeLess: \"Ocultar detalles\",\r\n  projectImage: \"Imagen del proyecto\",\r\n  badges: {\r\n    usability: \"Usabilidad\",\r\n    informationArchitecture: \"Arquitectura de la Información\",\r\n    userTesting: \"Pruebas de Usuario\",\r\n    uxResearch: \"UX Research\",\r\n    journeyMapping: \"Mapeo de Jornada\",\r\n    stakeholderManagement: \"Gestión de Stakeholders\",\r\n    productStrategy: \"Estrategia de Producto\",\r\n    seo: \"SEO\",\r\n    productValidation: \"Validación de Producto\",\r\n    visualDesign: \"Diseño Visual\",\r\n    communication: \"Comunicación\",\r\n    engagement: \"Engagement\"\r\n  },\r\n  fgvLaw: {\r\n    title: \"FGV LAW\",\r\n    category: \"Navegación y Usabilidad\",\r\n    overview: \"Reestructuración del área de cursos jurídicos de Direito GV enfocada en usabilidad y organización de la información para mejorar la experiencia del usuario.\",\r\n    discovery: \"Identifiqué que los usuarios tenían dificultades para localizar y comparar cursos en la plataforma de Direito GV.\",\r\n    solution: \"Diseñé un nuevo panel con sistema de pestañas y filtros temáticos específicos para el contexto jurídico.\",\r\n    iteration: \"Después de las pruebas con usuarios, simplificamos la terminología de los filtros y ajustamos la jerarquía de información.\",\r\n    outcomes: [\r\n      \"Mejora del 25% en la visibilidad de cursos y 35% más interacciones con páginas específicas en 3 meses\",\r\n      \"Aumento del 18% en la tasa de conversión de visitas a inscripciones, pasando de aproximadamente 8% a 10%\",\r\n      \"Reducción del 30% en el tiempo promedio de navegación, de cerca de 4 minutos a 3 minutos hasta la selección del curso\"\r\n    ],\r\n    insights: \"La estructura de navegación necesita guiar, no solo mostrar. La claridad y agrupación relevante influyen directamente en la percepción de valor de un curso.\"\r\n  },\r\n  direitoGV: {\r\n    title: \"Investigación Derecho FGV\",\r\n    category: \"Mapas, Flujos e Investigación\",\r\n    overview: \"Reorganización del área de investigación para mejorar la visibilidad de proyectos académicos y facilitar el acceso a investigadores.\",\r\n    discovery: \"El área de investigación estaba fragmentada y era poco accesible. Los investigadores tenían dificultades para promocionar su trabajo y los usuarios externos no podían encontrar información relevante sobre proyectos en curso.\",\r\n    solution: \"Desarrollé una nueva arquitectura de información con categorización por áreas temáticas, perfiles de investigadores y línea de tiempo de proyectos. También creé un sistema de búsqueda avanzada.\",\r\n    iteration: \"Realizamos pruebas con estudiantes, profesores e investigadores. La navegación se ajustó basándose en comentarios sobre nomenclatura y orden de prioridades. Validé cada cambio con los stakeholders involucrados.\",\r\n    outcomes: [\r\n      \"Reducción del 65% en el tiempo de navegación para encontrar proyectos específicos, de aproximadamente 6 minutos a 2 minutos\",\r\n      \"Aumento del 85% en visitas a páginas de investigadores, pasando de cerca de 150 a más de 280 visitas mensuales\",\r\n      \"Crecimiento del 40% en consulta de publicaciones académicas y 25% más solicitudes de asociaciones en 5 meses\"\r\n    ],\r\n    insights: \"Las áreas institucionales ganan relevancia cuando son navegables, actualizadas y reflejadas estratégicamente en la arquitectura de información.\"\r\n  },\r\n  taliparts: {\r\n    title: \"Taliparts\",\r\n    category: \"UX Estratégico + B2B\",\r\n    overview: \"Estructuración y validación digital de Taliparts para publicación de autopartes en Mercado Libre con enfoque en aprendizaje rápido.\",\r\n    discovery: \"Realicé benchmark detallado con competidores del sector automotriz. Entrevisté mecánicos y propietarios de tiendas, modelé personas y apliqué la Matriz CSD para identificar certezas, suposiciones y dudas en el catálogo físico.\",\r\n    solution: \"Creé una estrategia de validación con SEO para Mercado Libre, estandarización visual de anuncios, categorización centrada en el vocabulario del comprador e historial de búsquedas. También organicé KPIs y definí plan de priorización de productos.\",\r\n    iteration: \"Probé productos por bloques temáticos, monitoreando clics, preguntas y tasa de conversión. Refiné descripciones, títulos e incluso selección de artículos basándome en rendimiento real.\",\r\n    outcomes: [\r\n      \"Crecimiento del 45% en ventas de productos priorizados, generando aproximadamente R$ 6,500 en ingresos adicionales en 4 meses\",\r\n      \"Reducción del 40% en dudas de compradores, disminuyendo de cerca de 20 a 12 preguntas por producto publicado\",\r\n      \"Creación de proceso que aumentó la eficiencia de publicación en 50%, permitiendo análisis de más de 80 productos en 2 meses\"\r\n    ],\r\n    insights: \"La validación digital a bajo costo es posible — y necesaria. La lógica del producto necesita considerar contexto físico, vocabulario técnico y diferenciales percibidos por el cliente.\"\r\n  },\r\n  tvInstitucional: {\r\n    title: \"FGV TV Institucional\",\r\n    category: \"Engagement y Comunicación Visual\",\r\n    overview: \"Sistema visual para TVs en el hall de FGV para comunicar eventos y actualizaciones institucionales de manera atractiva y dinámica.\",\r\n    discovery: \"Los estudiantes ignoraban carteleras físicas y emails institucionales. Identifiqué que el lenguaje de los canales estaba desactualizado y mal integrado con la rutina visual de los espacios.\",\r\n    solution: \"Implementé un panel digital con curaduría de contenido semanal, enfoque en ritmo visual y claridad inmediata de mensajes. La plataforma fue diseñada para ser automatizada, con flexibilidad de actualización remota.\",\r\n    iteration: \"Probamos tipos de animaciones, tiempo de visualización y contraste. Ajustamos el calendario visual y optimizamos el layout basándose en comentarios de estudiantes y coordinación.\",\r\n    outcomes: [\r\n      \"Aumento del 35% en visibilidad de eventos institucionales, mejorando el conocimiento de estudiantes sobre actividades del campus\",\r\n      \"Crecimiento del 20% en participación en eventos, con mayor engagement de la comunidad académica\",\r\n      \"Mejora del 40% en retención de información institucional comparado con métodos anteriores de comunicación\"\r\n    ],\r\n    insights: \"Los ambientes físicos también son interfaces. Cuando están bien diseñados, informan, involucran y conectan — sin necesidad de login.\"\r\n  }\r\n};\r\n", "export default {\r\n  title: \"Ciclo de Backlog Estratégico\",\r\n  description: \"Demostración práctica de cómo transformo desafíos empresariales en soluciones UX medibles. Cada caso presenta metodología aplicada, resultados alcanzados e insights estratégicos que generan impacto real para stakeholders y usuarios.\",\r\n  solution: \"Solución\",\r\n  result: \"Resultado\",\r\n  note: \"Nota\",\r\n  noItems: \"No hay elementos en esta página.\",\r\n  previous: \"Anterior\",\r\n  next: \"Siguiente\",\r\n  items: [\r\n    {\r\n      challenge: \"Los usuarios abandonaban formularios largos sin completar el registro\",\r\n      solution: \"Implementé formulario paso a paso con barra de progreso y validación en tiempo real\",\r\n      result: \"Aumento del 40% en la tasa de finalización de registros en 2 semanas\",\r\n      note: \"Dividir tareas complejas en pasos más pequeños reduce la ansiedad y mejora la experiencia.\"\r\n    },\r\n    {\r\n      challenge: \"Baja adopción de funciones premium debido a falta de claridad sobre beneficios\",\r\n      solution: \"Creé onboarding interactivo con demostraciones prácticas de funciones premium\",\r\n      result: \"Crecimiento del 60% en conversiones a planes pagos en 1 mes\",\r\n      note: \"Mostrar valor a través de experiencia práctica es más efectivo que solo listar funcionalidades.\"\r\n    },\r\n    {\r\n      challenge: \"Los usuarios no encontraban fácilmente soporte cuando necesitaban ayuda\",\r\n      solution: \"Rediseñé sistema de ayuda contextual con chatbot inteligente y FAQ dinámico\",\r\n      result: \"Reducción del 50% en tickets de soporte y aumento del 35% en satisfacción\",\r\n      note: \"Ayuda contextual en el momento correcto previene frustraciones y mejora la autonomía del usuario.\"\r\n    },\r\n    {\r\n      challenge: \"Interfaz compleja causaba confusión en usuarios principiantes\",\r\n      solution: \"Desarrollé modo simplificado con tutorial progresivo y tooltips adaptativos\",\r\n      result: \"Disminución del 45% en tasa de abandono de nuevos usuarios\",\r\n      note: \"Adaptar la complejidad al nivel de experiencia del usuario mejora significativamente la adopción.\"\r\n    },\r\n    {\r\n      challenge: \"Proceso de checkout tenía alta tasa de abandono en el último paso\",\r\n      solution: \"Simplifiqué flujo eliminando campos innecesarios y agregando opciones de pago express\",\r\n      result: \"Aumento del 30% en finalización de compras y reducción del 25% en tiempo de checkout\",\r\n      note: \"Cada campo extra en checkout es una barrera potencial. La simplicidad genera conversión.\"\r\n    },\r\n    {\r\n      challenge: \"Los usuarios no percibían actualizaciones importantes del producto\",\r\n      solution: \"Implementé sistema de notificaciones in-app con diseño no intrusivo y personalización\",\r\n      result: \"Mejora del 55% en engagement con nuevas funcionalidades\",\r\n      note: \"Comunicación efectiva sobre cambios mantiene a usuarios informados sin interrumpir el flujo.\"\r\n    },\r\n    {\r\n      challenge: \"Dificultad para encontrar contenido relevante en base de conocimiento extensa\",\r\n      solution: \"Creé sistema de búsqueda inteligente con filtros contextuales y sugerencias automáticas\",\r\n      result: \"Aumento del 70% en utilización de base de conocimiento y reducción del 40% en consultas repetitivas\",\r\n      note: \"Búsqueda eficiente transforma información abundante en conocimiento accesible.\"\r\n    },\r\n    {\r\n      challenge: \"Bajo engagement en funcionalidades colaborativas de la plataforma\",\r\n      solution: \"Rediseñé interfaz de colaboración con indicadores visuales de actividad y gamificación sutil\",\r\n      result: \"Crecimiento del 80% en colaboración entre usuarios en 6 semanas\",\r\n      note: \"Hacer la colaboración visible y gratificante incentiva naturalmente la participación.\"\r\n    },\r\n    {\r\n      challenge: \"Los usuarios perdían progreso al navegar entre secciones de la app\",\r\n      solution: \"Implementé sistema de auto-guardado con indicadores visuales de estado de guardado\",\r\n      result: \"Eliminación del 95% de quejas sobre pérdida de datos\",\r\n      note: \"La confianza en la tecnología crece cuando los usuarios ven que su trabajo está siempre protegido.\"\r\n    },\r\n    {\r\n      challenge: \"Interfaz no responsiva causaba frustración en dispositivos móviles\",\r\n      solution: \"Desarrollé versión mobile-first con gestos intuitivos y navegación optimizada para toque\",\r\n      result: \"Aumento del 120% en uso móvil y mejora de 4.2 a 4.7 en calificación de app store\",\r\n      note: \"Diseño mobile-first garantiza experiencia consistente independientemente del dispositivo usado.\"\r\n    },\r\n    {\r\n      challenge: \"Usuarios con discapacidad visual enfrentaban barreras de accesibilidad\",\r\n      solution: \"Implementé estándares WCAG 2.1 con navegación por teclado y compatibilidad con lectores de pantalla\",\r\n      result: \"Aumento del 200% en uso por personas con discapacidad y reconocimiento en premio de accesibilidad\",\r\n      note: \"Accesibilidad no es solo cumplimiento — es diseño inclusivo que beneficia a todos los usuarios.\"\r\n    },\r\n    {\r\n      challenge: \"Textos técnicos confundían a usuarios no especializados\",\r\n      solution: \"Reescribí microcopy con lenguaje claro y agregué explicaciones contextuales cuando era necesario\",\r\n      result: \"Reducción del 60% en dudas de usuarios y mejora en percepción de facilidad de uso\",\r\n      note: \"Pequeñas decisiones en el texto tienen gran impacto en la experiencia de lectura y comprensión.\"\r\n    }\r\n  ]\r\n};\r\n", "export default {\n  title: \"¿Conversamos?\",\n  description: \"Siempre estoy abierto a nuevas oportunidades y colaboraciones. Ponte en contacto para discutir proyectos, asociaciones o simplemente intercambiar ideas sobre UX y diseño de productos.\",\n  form: {\n    name: \"Nombre\",\n    namePlaceholder: \"Ingresa tu nombre completo\",\n    email: \"Email\",\n    emailPlaceholder: \"Ingresa tu mejor email\",\n    subject: \"Asunto\",\n    subjectPlaceholder: \"¿De qué te gustaría hablar?\",\n    message: \"Mensaje\",\n    messagePlaceholder: \"Cuéntame sobre tu proyecto, oportunidad o cómo puedo ayudar...\",\n    send: \"Enviar mensaje\",\n    sending: \"Enviando...\",\n    success: \"✅ ¡Mensaje enviado con éxito! Te responderé pronto.\",\n    error: \"❌ ¡Ups! No pudimos enviar tu mensaje. Intenta de nuevo o contacta directamente.\",\n    nameRequired: \"El nombre es obligatorio\",\n    emailRequired: \"El email es obligatorio\",\n    emailInvalid: \"Email inválido\",\n    subjectRequired: \"El asunto es obligatorio\",\n    messageRequired: \"El mensaje es obligatorio\",\n    messageMinLength: \"El mensaje debe tener al menos 10 caracteres\"\n  },\n  info: {\n    email: \"<EMAIL>\",\n    location: \"São Paulo, Brasil\",\n    availability: \"Disponible para proyectos freelance y oportunidades full-time\"\n  },\n  social: {\n    linkedin: \"LinkedIn\",\n    whatsapp: \"WhatsApp\",\n    email: \"Email\"\n  }\n};\n", "export default {\n  title: \"Accesibilidad\",\n  subtitle: \"Personaliza tu experiencia de navegación\",\n  description: \"Configura opciones de accesibilidad para mejorar tu experiencia.\",\n  instructions: \"Usa las opciones a continuación para personalizar la interfaz.\",\n  close: \"Cerrar\",\n  open: \"Abrir\",\n  menuLabel: \"Menú de Accesibilidad\",\n  menuTooltip: \"Configuraciones de accesibilidad (Shift + A)\",\n  fontSize: {\n    label: \"Tamaño de fuente\",\n    increase: \"Aumentar\",\n    decrease: \"Disminuir\",\n    reset: \"Resetear\",\n    increaseLabel: \"Aumentar tamaño de fuente\",\n    decreaseLabel: \"Disminuir tamaño de fuente\",\n    resetLabel: \"Resetear tamaño de fuente\"\n  },\n  contrast: {\n    label: \"Alto contraste\",\n    enable: \"Activar alto contraste\",\n    disable: \"Desactivar alto contraste\",\n    enabled: \"Alto contraste activado\",\n    disabled: \"Alto contraste desactivado\"\n  },\n  readingMode: {\n    label: \"Modo lectura\",\n    enable: \"Activar modo lectura\",\n    disable: \"Desactivar modo lectura\",\n    enabled: \"Modo lectura activado\",\n    disabled: \"Modo lectura desactivado\"\n  },\n  screenReader: {\n    label: \"Lector de pantalla\",\n    enable: \"Activar lector de pantalla\",\n    disable: \"Desactivar lector de pantalla\",\n    enabled: \"Lector de pantalla activado\",\n    disabled: \"Lector de pantalla desactivado\"\n  },\n  reset: {\n    label: \"Resetear configuraciones\",\n    action: \"Configuraciones de accesibilidad reseteadas\",\n    success: \"Configuraciones reseteadas con éxito\"\n  },\n  features: {\n    fontSize: \"Tamaño de fuente\",\n    fontSizeIncrease: \"Aumentar fuente\",\n    fontSizeDecrease: \"Disminuir fuente\",\n    fontSizeReset: \"Resetear fuente\",\n    contrast: \"Alto contraste\",\n    contrastEnable: \"Activar contraste\",\n    contrastDisable: \"Desactivar contraste\",\n    readingMode: \"Modo lectura\",\n    readingModeEnable: \"Activar lectura\",\n    readingModeDisable: \"Desactivar lectura\"\n  },\n  skipLinks: {\n    skipToContent: \"Saltar al contenido principal\",\n    skipToNavigation: \"Saltar a navegación\"\n  },\n  status: {\n    enabled: \"Activado\",\n    disabled: \"Desactivado\",\n    fontIncreased: \"Fuente aumentada\",\n    fontDecreased: \"Fuente disminuida\",\n    fontReset: \"Fuente reseteada\",\n    contrastEnabled: \"Alto contraste activado\",\n    contrastDisabled: \"Alto contraste desactivado\",\n    readingEnabled: \"Modo lectura activado\",\n    readingDisabled: \"Modo lectura desactivado\"\n  }\n};\n", "export default {\n  enabled: \"Sonido activado\",\n  disabled: \"Sonido desactivado\",\n  enabledDesc: \"Efectos de sonido activados\",\n  disabledDesc: \"Efectos de sonido desactivados\",\n  enable: \"Activar sonido\",\n  disable: \"Desactivar sonido\",\n  toggle: \"Alternar sonido\",\n  changed: \"Sonido cambiado a\",\n  volume: \"Volumen\",\n  volumeControl: \"Ajustar volumen del sonido\"\n};\n", "export default {\n  theme: {\n    light: \"Cambiar a modo claro\",\n    dark: \"Cambiar a modo oscuro\",\n    system: \"Usar preferencia del sistema\"\n  },\n  language: {\n    switch: \"Cambiar idioma\",\n    current: \"Idioma actual\",\n    available: \"Idiomas disponibles\"\n  },\n  navigation: {\n    home: \"Ir al inicio\",\n    profile: \"Ir al perfil\",\n    projects: \"Ver proyectos\",\n    backlog: \"Ver backlog estratégico\",\n    contact: \"Ponerse en contacto\"\n  },\n  social: {\n    linkedin: \"Perfil de LinkedIn\",\n    email: \"Enviar email\",\n    whatsapp: \"Chatear en WhatsApp\"\n  },\n  sound: {\n    enable: \"Activar efectos de sonido\",\n    disable: \"Desactivar efectos de sonido\"\n  },\n  actions: {\n    expand: \"Expandir detalles\",\n    collapse: \"Contraer detalles\",\n    download: \"Descargar archivo\",\n    share: \"Compartir\",\n    copy: \"Copiar enlace\",\n    print: \"Imprimir página\",\n    backToTop: \"Volver arriba\"\n  }\n};\n", "export default {\n  success: {\n    title: \"¡Éxito!\",\n    messageSent: \"¡Mensaje enviado con éxito!\",\n    settingsSaved: \"Configuraciones guardadas\",\n    linkCopied: \"Enlace copiado al portapapeles\",\n    themeChanged: \"Tema cambiado\",\n    languageChanged: \"Idioma cambiado\"\n  },\n  error: {\n    title: \"Error\",\n    messageNotSent: \"Error al enviar mensaje\",\n    networkError: \"Error de conexión\",\n    genericError: \"Algo salió mal\",\n    tryAgain: \"Intenta de nuevo\"\n  },\n  info: {\n    title: \"Información\",\n    loading: \"Cargando...\",\n    processing: \"Procesando...\",\n    saving: \"Guardando...\"\n  },\n  warning: {\n    title: \"Advertencia\",\n    unsavedChanges: \"Tienes cambios no guardados\",\n    confirmAction: \"¿Estás seguro de que quieres continuar?\"\n  }\n};\n", "export default {\n  title: \"Tarcisi<PERSON> Bispo - UX/Product Designer | Portfolio\",\n  description: \"UX/Product Designer especializado en estrategia, impacto y experiencia del usuario. Ve mis proyectos de diseño de productos digitales y soluciones UX.\",\n  keywords: \"UX Designer, Product Designer, Diseño de Producto, Experiencia del Usuario, UI/UX, Portfolio, São Paulo\",\n  author: \"Tarcisio Bispo de Araujo\",\n  pages: {\n    home: {\n      title: \"Tarcisio Bispo - UX/Product Designer | Portfolio\",\n      description: \"UX/Product Designer enfocado en estrategia, impacto y experiencia del usuario. Experto en diseño de productos digitales, conversión e impacto empresarial.\"\n    },\n    projects: {\n      title: \"Proyectos - Tarcisio Bispo | UX Designer\",\n      description: \"Ve mis principales proyectos de UX/Product Design: FGV LAW, Direito GV, Taliparts y FGV TV Institucional. Casos reales con resultados medibles.\"\n    },\n    backlog: {\n      title: \"Backlog Estratégico - <PERSON><PERSON><PERSON><PERSON> | UX Designer\",\n      description: \"Demostración práctica de cómo transformo desafíos empresariales en soluciones UX medibles. Metodología aplicada, resultados alcanzados e insights estratégicos.\"\n    },\n    contact: {\n      title: \"Contacto - Tarcisio Bispo | UX Designer\",\n      description: \"Ponte en contacto para discutir proyectos, asociaciones u oportunidades. Disponible para proyectos freelance y oportunidades full-time.\"\n    }\n  }\n};\n", "export default {\n  person: {\n    name: \"<PERSON><PERSON><PERSON><PERSON>\",\n    jobTitle: \"UX/Product Designer\",\n    description: \"UX/Product Designer especializado en estrategia, impacto y experiencia del usuario\",\n    location: \"São Paulo, Brasil\",\n    email: \"<EMAIL>\",\n    skills: [\"UX Design\", \"Product Design\", \"Estrategia de Diseño\", \"Investigación de Usuario\", \"Prototipado\", \"Pruebas de Usabilidad\"]\n  },\n  organization: {\n    name: \"Tarcisio Bispo Portfolio\",\n    description: \"Portfolio profesional de UX/Product Design\",\n    location: \"São Paulo, Brasil\"\n  }\n};\n", "export default {\n  profile: {\n    photo: \"Foto de perfil de Tarcisio Bispo\",\n    ixdfLogo: \"Logo de Interaction Design Foundation\",\n    ixdfSeal: \"Sello de certificación IxDF\"\n  },\n  projects: {\n    fgvLaw: \"Captura de pantalla del proyecto FGV LAW - diseñadores trabajando en equipo en la oficina\",\n    direitoGV: \"Captura de pantalla del proyecto Investigación Derecho FGV - mesa de madera con libro frente a estantería\",\n    taliparts: \"Captura de pantalla del proyecto Taliparts - e-commerce de autopartes\",\n    tvInstitucional: \"Captura de pantalla del proyecto FGV TV Institucional - cartel blanco colgado en el lateral de un edificio\"\n  },\n  icons: {\n    menu: \"Ícono de menú\",\n    close: \"Ícono de cerrar\",\n    expand: \"Ícono de expandir\",\n    collapse: \"Ícono de contraer\",\n    external: \"Ícono de enlace externo\",\n    download: \"Ícono de descarga\",\n    email: \"Ícono de email\",\n    phone: \"Ícono de teléfono\",\n    linkedin: \"Ícono de LinkedIn\",\n    whatsapp: \"Ícono de WhatsApp\",\n    github: \"Ícono de GitHub\",\n    sun: \"Ícono de sol\",\n    moon: \"Ícono de luna\",\n    globe: \"Ícono de globo\",\n    volume: \"Ícono de volumen\",\n    success: \"Ícono de éxito\",\n    error: \"Ícono de error\",\n    warning: \"Ícono de advertencia\",\n    info: \"Ícono de información\"\n  },\n  decorative: {\n    gradient: \"Gradiente decorativo\",\n    pattern: \"Patrón decorativo\",\n    divider: \"Divisor visual\",\n    background: \"Imagen de fondo decorativa\"\n  }\n};\n", "export default {\n  changed: \"Idioma cambiado con éxito\",\n  current: \"Idioma actual\",\n  available: \"Idiomas disponibles\",\n  portuguese: \"Português\",\n  english: \"English\",\n  spanish: \"Español\",\n  select: \"Seleccionar idioma\"\n};\n", "export default {\n  toggle: \"Alternar tema\",\n  changed: \"Tema cambiado con éxito\",\n  light: \"Modo claro activado\",\n  dark: \"Modo oscuro activado\",\n  system: \"Usando preferencia del sistema\"\n};\n", "export default {\n  copyright: \"© 2024 Tarcisio Bispo. Todos los derechos reservados.\",\n  title: \"UX/Product Designer\"\n};\n", "export default {\n  title: \"Feedback\",\n  subtitle: \"Tu opinión importa\",\n  description: \"Comparte tu experiencia y sugerencias\",\n  typeQuestion: \"¿Qué tipo de feedback te gustaría compartir?\",\n  close: \"Cerra<PERSON>\",\n  back: \"Volver\",\n  send: \"Enviar feedback\",\n  sending: \"Enviando...\",\n  includeEmail: \"Incluir mi email para respuesta\",\n  privacyPolicy: \"Política de Privacidad\",\n\n  // Tipos de feedback\n  problem: \"Reportar Problema\",\n  idea: \"Compartir Idea\",\n  praise: \"Dar Elogio\",\n\n  // Títulos específicos por tipo\n  problemTitle: \"Reportar Problema\",\n  ideaTitle: \"Compartir Idea\",\n  praiseTitle: \"Dar Elogio\",\n  defaultTitle: \"Enviar Feedback\",\n\n  // Instrucciones específicas por tipo\n  problemInstruction: \"Describe el problema que encontraste en detalle\",\n  ideaInstruction: \"Comparte tu idea o sugerencia de mejora\",\n  praiseInstruction: \"Cuéntanos qué te gustó\",\n  defaultInstruction: \"Comparte tu feedback con nosotros\",\n\n  // Placeholders específicos por tipo\n  problemPlaceholder: \"Describe el problema que encontraste...\",\n  ideaPlaceholder: \"Comparte tu idea o sugerencia...\",\n  praisePlaceholder: \"Cuéntanos qué te gustó...\",\n  defaultPlaceholder: \"Comparte tu feedback...\",\n\n  // Validación\n  validation: {\n    messageRequired: \"El mensaje es obligatorio\",\n    messageMinLength: \"Mínimo 5 caracteres\",\n    emailInvalid: \"Email inválido\"\n  },\n\n  // Formulario\n  form: {\n    type: \"Tipo de feedback\",\n    message: \"Tu mensaje\",\n    email: \"Tu email (opcional)\",\n    send: \"Enviar feedback\",\n    sending: \"Enviando...\",\n    success: \"✅ ¡Gracias por tu feedback! Tu opinión es muy importante para nosotros.\",\n    error: \"❌ ¡Ups! No pudimos enviar tu feedback. Intenta de nuevo o contacta directamente.\",\n    messageRequired: \"El mensaje es obligatorio\"\n  },\n\n  // Mensajes de estado\n  status: {\n    success: \"¡Gracias por tu feedback!\",\n    error: \"Error al enviar feedback. Inténtalo de nuevo.\",\n    sending: \"Enviando feedback...\"\n  },\n\n  // Tipos legados (mantener compatibilidad)\n  types: {\n    bug: \"Reportar bug\",\n    suggestion: \"Sugerencia\",\n    compliment: \"Elogio\",\n    other: \"Otro\"\n  }\n};\n", "export default {\n  title: \"Este sitio usa cookies\",\n  description: \"Usamos cookies para mejorar tu experiencia, analizar el tráfico del sitio y personalizar contenido.\",\n  learnMore: \"Saber más\",\n  acceptAll: \"<PERSON>ptar todas\",\n  rejectAll: \"<PERSON><PERSON><PERSON> todas\",\n  managePreferences: \"Gestionar preferencias\",\n  savePreferences: \"Guardar preferencias\",\n  required: \"Obligatorio\",\n  preferences: {\n    title: \"Preferencias de Cookies\"\n  },\n  types: {\n    necessary: {\n      title: \"Cookies Necesarias\",\n      description: \"Estas cookies son esenciales para el funcionamiento del sitio web y no se pueden desactivar.\"\n    },\n    analytics: {\n      title: \"Cookies de Análisis\",\n      description: \"Nos ayudan a entender cómo los visitantes interactúan con el sitio recopilando información de forma anónima.\",\n      providers: \"Proveedores\"\n    },\n    marketing: {\n      title: \"Cookies de Marketing\",\n      description: \"Se utilizan para rastrear visitantes en sitios web para mostrar anuncios relevantes y atractivos.\"\n    }\n  }\n};\n", "export default {\n  close: \"<PERSON><PERSON><PERSON>\",\n  open: \"<PERSON><PERSON><PERSON>\",\n  save: \"<PERSON><PERSON>\",\n  cancel: \"<PERSON><PERSON>ar\",\n  confirm: \"Confirmar\",\n  yes: \"S<PERSON>\",\n  no: \"No\",\n  loading: \"Cargando...\",\n  error: \"Error\",\n  success: \"Éxito\",\n  warning: \"Advertencia\",\n  info: \"Información\"\n};\n", "import React, { useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport LoadingSpinner from './LoadingSpinner';\r\nimport i18n from '../i18n/config';\r\n\r\ninterface I18nProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nconst I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {\r\n  const [isReady, setIsReady] = useState(false);\r\n  const { t } = useTranslation();\r\n\r\n  useEffect(() => {\r\n    const checkI18nReady = () => {\r\n      // Verificar se o i18n está inicializado e se as traduções estão carregadas\r\n      if (i18n.isInitialized && i18n.hasResourceBundle('pt-BR', 'translation')) {\r\n        console.log('🎯 I18n is ready!');\r\n        console.log('🌐 Current language:', i18n.language);\r\n        console.log('📊 Test translation:', t('profile.title'));\r\n        setIsReady(true);\r\n      } else {\r\n        console.log('⏳ Waiting for i18n...');\r\n        // Tentar novamente em 100ms\r\n        setTimeout(checkI18nReady, 100);\r\n      }\r\n    };\r\n\r\n    // Verificar imediatamente\r\n    checkI18nReady();\r\n\r\n    // Listener para mudanças no i18n\r\n    const handleLanguageChanged = () => {\r\n      console.log('🔄 Language changed to:', i18n.language);\r\n      setIsReady(true);\r\n    };\r\n\r\n    i18n.on('languageChanged', handleLanguageChanged);\r\n    i18n.on('initialized', () => {\r\n      console.log('✅ I18n initialized event fired');\r\n      checkI18nReady();\r\n    });\r\n\r\n    return () => {\r\n      i18n.off('languageChanged', handleLanguageChanged);\r\n      i18n.off('initialized', checkI18nReady);\r\n    };\r\n  }, [t]);\r\n\r\n  if (!isReady) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-white dark:bg-gray-900\">\r\n        <div className=\"text-center\">\r\n          <LoadingSpinner />\r\n          <p className=\"mt-4 text-gray-600 dark:text-gray-400\">\r\n            Carregando traduções...\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return <>{children}</>;\r\n};\r\n\r\nexport default I18nProvider;\r\n", "import { useEffect, useState, useCallback } from 'react';\r\nimport { User, Folder, Repeat, Mail, MessageCircle, Menu, X } from 'lucide-react';\r\n\r\nconst navItems = [\r\n  { href: '#perfil', icon: User, sectionId: 'perfil', i18nKey: 'navigation.profile' },\r\n  { href: '#projetos', icon: Folder, sectionId: 'projetos', i18nKey: 'navigation.projects' },\r\n  { href: '#backlog', icon: Repeat, sectionId: 'backlog', i18nKey: 'navigation.backlog' },\r\n  { href: '#contato', icon: Mail, sectionId: 'contato', i18nKey: 'navigation.contact' },\r\n];\r\n\r\n// Componente mobile funcional\r\nconst MobileNavigationMenu = ({ isOpen, onClose, activeSection, onNavigate, items }: any) => {\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 md:hidden\">\r\n      <div className=\"fixed inset-0 bg-black/50\" onClick={onClose}></div>\r\n      <div className=\"fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-800 shadow-xl\">\r\n        <div className=\"p-6\">\r\n          <div className=\"flex justify-between items-center mb-6\">\r\n            <h2 className=\"text-lg font-semibold\">Menu</h2>\r\n            <button onClick={onClose} className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded\">\r\n              <X className=\"w-5 h-5\" />\r\n            </button>\r\n          </div>\r\n          <nav className=\"space-y-4\">\r\n            {items.map((item: any) => (\r\n              <a\r\n                key={item.id}\r\n                href={item.href}\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  onNavigate(item.sectionId);\r\n                  onClose();\r\n                }}\r\n                className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${\r\n                  activeSection === item.sectionId\r\n                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'\r\n                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'\r\n                }`}\r\n              >\r\n                {item.icon}\r\n                <span>{item.text}</span>\r\n              </a>\r\n            ))}\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default function Header() {\r\n  const [scrolled, setScrolled] = useState(false);\r\n  const [activeSection, setActiveSection] = useState('perfil');\r\n  const [feedbackOpen, setFeedbackOpen] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const onScroll = () => {\r\n      setScrolled(window.scrollY > 10);\r\n      // Detecta a seção ativa com base no scroll\r\n      let found = 'perfil';\r\n      for (const item of navItems) {\r\n        const el = document.getElementById(item.sectionId);\r\n        if (el) {\r\n          const rect = el.getBoundingClientRect();\r\n          if (rect.top <= 80 && rect.bottom > 80) {\r\n            found = item.sectionId;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n      setActiveSection(found);\r\n    };\r\n    window.addEventListener('scroll', onScroll, { passive: true });\r\n    onScroll();\r\n    return () => window.removeEventListener('scroll', onScroll);\r\n  }, []);\r\n\r\n  const handleNavClick = useCallback((e: React.MouseEvent, sectionId: string) => {\r\n    e.preventDefault();\r\n    const el = document.getElementById(sectionId);\r\n    if (el) {\r\n      window.scrollTo({\r\n        top: el.offsetTop - 70,\r\n        behavior: 'smooth',\r\n      });\r\n      setActiveSection(sectionId);\r\n    }\r\n  }, []);\r\n\r\n  const handleMobileNavigation = useCallback((sectionId: string) => {\r\n    const el = document.getElementById(sectionId);\r\n    if (el) {\r\n      window.scrollTo({\r\n        top: el.offsetTop - 70,\r\n        behavior: 'smooth',\r\n      });\r\n      setActiveSection(sectionId);\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <header\r\n      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 border-b border-gray-200 dark:border-gray-700\r\n        ${scrolled ? 'bg-white dark:bg-gray-900 shadow-md' : 'bg-white/80 dark:bg-gray-900/80 backdrop-blur'}\r\n      `}\r\n    >\r\n      <nav className=\"max-w-7xl mx-auto flex items-center justify-between px-6 py-2\">\r\n        {/* Logo */}\r\n        <a href=\"#perfil\" className=\"hidden md:flex items-center gap-2 text-2xl font-bold tracking-tight text-blue-600\">\r\n          <span className=\"rounded bg-blue-600 text-white px-2 py-1 text-lg font-black shadow-sm\">TBA</span>\r\n        </a>\r\n\r\n        {/* Navegação Desktop */}\r\n        <ul className=\"hidden md:flex gap-8 items-end\">\r\n          {navItems.map(item => {\r\n            const Icon = item.icon;\r\n            const isActive = activeSection === item.sectionId;\r\n            return (\r\n              <li key={item.href}>\r\n                <a\r\n                  href={item.href}\r\n                  onClick={e => handleNavClick(e, item.sectionId)}\r\n                  className={`flex flex-col items-center px-3 py-2 transition-colors ${\r\n                    isActive ? 'text-blue-600' : 'text-gray-700 dark:text-gray-300 hover:text-blue-600'\r\n                  }`}\r\n                >\r\n                  <Icon className=\"mb-1 w-5 h-5\" />\r\n                  <span className=\"text-sm font-medium\">\r\n                    {item.sectionId === 'perfil' && 'Perfil'}\r\n                    {item.sectionId === 'projetos' && 'Projetos'}\r\n                    {item.sectionId === 'backlog' && 'Backlog'}\r\n                    {item.sectionId === 'contato' && 'Contato'}\r\n                  </span>\r\n                  {isActive && (\r\n                    <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full h-0.5 bg-blue-600\"></div>\r\n                  )}\r\n                </a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n\r\n        {/* Ações */}\r\n        <div className=\"flex gap-2 items-center\">\r\n          {/* Mobile menu button */}\r\n          <div className=\"md:hidden\">\r\n            <button\r\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\r\n              className=\"flex items-center justify-center w-11 h-11 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 shadow hover:shadow-lg transition-all\"\r\n            >\r\n              {mobileMenuOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\r\n            </button>\r\n          </div>\r\n\r\n          {/* Desktop actions */}\r\n          <div className=\"hidden md:flex gap-2 items-center\">\r\n            <button\r\n              onClick={() => setFeedbackOpen(true)}\r\n              className=\"flex items-center justify-center w-10 h-10 rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 shadow hover:shadow-lg transition-all text-blue-600\"\r\n            >\r\n              <MessageCircle className=\"w-5 h-5\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Mobile Navigation Menu */}\r\n      <MobileNavigationMenu\r\n        isOpen={mobileMenuOpen}\r\n        onClose={() => setMobileMenuOpen(false)}\r\n        activeSection={activeSection}\r\n        onNavigate={handleMobileNavigation}\r\n        items={[\r\n          {\r\n            id: 'perfil',\r\n            sectionId: 'perfil',\r\n            text: 'Perfil',\r\n            href: '#perfil',\r\n            icon: <User className=\"w-5 h-5\" />\r\n          },\r\n          {\r\n            id: 'projetos',\r\n            sectionId: 'projetos',\r\n            text: 'Projetos',\r\n            href: '#projetos',\r\n            icon: <Folder className=\"w-5 h-5\" />\r\n          },\r\n          {\r\n            id: 'backlog',\r\n            sectionId: 'backlog',\r\n            text: 'Backlog',\r\n            href: '#backlog',\r\n            icon: <Repeat className=\"w-5 h-5\" />\r\n          },\r\n          {\r\n            id: 'contato',\r\n            sectionId: 'contato',\r\n            text: 'Contato',\r\n            href: '#contato',\r\n            icon: <Mail className=\"w-5 h-5\" />\r\n          }\r\n        ]}\r\n      />\r\n\r\n      {/* Feedback Modal Placeholder */}\r\n      {feedbackOpen && (\r\n        <div className=\"fixed inset-0 z-50 bg-black/50 flex items-center justify-center\" onClick={() => setFeedbackOpen(false)}>\r\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full mx-4\">\r\n            <h3 className=\"text-lg font-semibold mb-4\">Feedback</h3>\r\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">Modal de feedback funcionando!</p>\r\n            <button\r\n              onClick={() => setFeedbackOpen(false)}\r\n              className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\r\n            >\r\n              Fechar\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </header>\r\n  );\r\n}\r\n", "import * as React from 'react';\r\nimport { Suspense } from 'react';\r\nimport { Toaster } from \"@/components/ui/toaster\";\r\nimport { Toaster as Sonner } from \"@/components/ui/sonner\";\r\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\r\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport { HelmetProvider } from 'react-helmet-async';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport LoadingSpinner from \"@/components/LoadingSpinner\";\r\nimport I18nProvider from \"@/components/I18nProvider\";\r\n\r\n// Critical components loaded immediately\r\nimport Header from \"@/components/Header\";\r\n\r\n// Lazy loading dos componentes de página para code splitting\r\nconst Index = React.lazy(() => import(\"./pages/Index\"));\r\nconst NotFound = React.lazy(() => import(\"./pages/NotFound\"));\r\nconst PrivacyPolicy = React.lazy(() => import(\"./pages/PrivacyPolicy\"));\r\n\r\n// Lazy loading de componentes pesados para reduzir bundle inicial\r\nconst AnalyticsProvider = React.lazy(() => import(\"@/components/analytics/AnalyticsProvider\"));\r\nconst BackToTop = React.lazy(() => import(\"@/components/ui/BackToTop\"));\r\nconst FluidGradientBackground = React.lazy(() => import(\"@/components/FluidGradientBackground\").then(module => ({ default: module.default })));\r\nconst GradientSectionIndicator = React.lazy(() => import(\"@/components/FluidGradientBackground\").then(module => ({ default: module.GradientSectionIndicator })));\r\nconst FluidGradientDemo = React.lazy(() => import(\"@/components/examples/FluidGradientDemo\"));\r\n\r\nconst SoundDemo = React.lazy(() => import(\"@/components/ui/SoundDemo\"));\r\nconst LazyScripts = React.lazy(() => import(\"@/components/LazyScripts\"));\r\nconst CookieConsent = React.lazy(() => import(\"@/components/CookieConsent\"));\r\nconst TranslationDebug = React.lazy(() => import(\"@/components/debug/TranslationDebug\"));\r\n\r\nconst queryClient = new QueryClient();\r\n\r\nconst App = () => {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <HelmetProvider>\r\n      <QueryClientProvider client={queryClient}>\r\n        <I18nProvider>\r\n          <TooltipProvider>\r\n          <Suspense fallback={<div className=\"min-h-screen flex items-center justify-center\"><LoadingSpinner /></div>}>\r\n            <AnalyticsProvider>\r\n\r\n            {/* Skip Links para Navegação por Teclado */}\r\n            <a href=\"#main-content\" className=\"skip-link\">\r\n              {t('accessibility.features.skipToContent')}\r\n            </a>\r\n            <a href=\"#navigation\" className=\"skip-link\">\r\n              {t('accessibility.features.skipToNavigation')}\r\n            </a>\r\n\r\n            <Toaster />\r\n            <Sonner />\r\n\r\n\r\n\r\n            {/* Sistema de Gradientes Fluidos */}\r\n            <Suspense fallback={null}>\r\n              <FluidGradientBackground />\r\n            </Suspense>\r\n\r\n            {/* Indicador de Seção (apenas em desenvolvimento) */}\r\n            {import.meta.env.DEV && (\r\n              <Suspense fallback={null}>\r\n                <GradientSectionIndicator />\r\n              </Suspense>\r\n            )}\r\n            {import.meta.env.DEV && (\r\n              <Suspense fallback={null}>\r\n                <FluidGradientDemo />\r\n              </Suspense>\r\n            )}\r\n\r\n            {/* Sound Demo - apenas em desenvolvimento */}\r\n            {import.meta.env.DEV && (\r\n              <div className=\"fixed bottom-4 right-4 z-50\">\r\n                <Suspense fallback={null}>\r\n                  <SoundDemo />\r\n                </Suspense>\r\n              </div>\r\n            )}\r\n\r\n            {/* UX Premium Components */}\r\n            <Suspense fallback={null}>\r\n              <BackToTop />\r\n            </Suspense>\r\n\r\n            {/* Lazy load third-party scripts */}\r\n            <Suspense fallback={null}>\r\n              <LazyScripts delay={2000} />\r\n            </Suspense>\r\n\r\n            {/* Cookie Consent Banner */}\r\n            <Suspense fallback={null}>\r\n              <CookieConsent />\r\n            </Suspense>\r\n\r\n            {/* Translation Debug - apenas em desenvolvimento */}\r\n            {import.meta.env.DEV && (\r\n              <Suspense fallback={null}>\r\n                <TranslationDebug />\r\n              </Suspense>\r\n            )}\r\n\r\n          <Header />\r\n          <BrowserRouter\r\n            basename=\"/portfolio\"\r\n            future={{\r\n              v7_startTransition: true,\r\n              v7_relativeSplatPath: true\r\n            }}\r\n          >\r\n            <Suspense fallback={\r\n              <div className=\"min-h-screen flex items-center justify-center\">\r\n                <LoadingSpinner />\r\n              </div>\r\n            }>\r\n              <Routes>\r\n                <Route path=\"/\" element={<Index />} />\r\n                <Route path=\"/privacy-policy\" element={<PrivacyPolicy />} />\r\n                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL \"*\" ROUTE */}\r\n                <Route path=\"*\" element={<NotFound />} />\r\n              </Routes>\r\n            </Suspense>\r\n          </BrowserRouter>\r\n          </AnalyticsProvider>\r\n          </Suspense>\r\n        </TooltipProvider>\r\n        </I18nProvider>\r\n      </QueryClientProvider>\r\n    </HelmetProvider>\r\n  );\r\n};\r\n\r\nexport default App;\r\n", "/**\n * Cache Optimization Utilities\n * Implements efficient caching strategies for static resources\n */\n\ninterface CacheConfig {\n  maxAge: number;\n  staleWhileRevalidate?: number;\n  cacheFirst?: boolean;\n}\n\nconst CACHE_CONFIGS: Record<string, CacheConfig> = {\n  // Images - Long cache with stale-while-revalidate\n  images: {\n    maxAge: 31536000, // 1 year\n    staleWhileRevalidate: 86400, // 1 day\n    cacheFirst: true\n  },\n  // JavaScript bundles - Long cache\n  scripts: {\n    maxAge: 31536000, // 1 year\n    cacheFirst: true\n  },\n  // CSS files - Long cache\n  styles: {\n    maxAge: 31536000, // 1 year\n    cacheFirst: true\n  },\n  // Fonts - Very long cache\n  fonts: {\n    maxAge: 31536000, // 1 year\n    cacheFirst: true\n  },\n  // API responses - Short cache with revalidation\n  api: {\n    maxAge: 300, // 5 minutes\n    staleWhileRevalidate: 60 // 1 minute\n  }\n};\n\n/**\n * Generate cache headers for different resource types\n */\nexport const getCacheHeaders = (resourceType: keyof typeof CACHE_CONFIGS): Record<string, string> => {\n  const config = CACHE_CONFIGS[resourceType];\n\n  if (!config) {\n    return {};\n  }\n\n  const headers: Record<string, string> = {\n    'Cache-Control': `public, max-age=${config.maxAge}`\n  };\n\n  if (config.staleWhileRevalidate) {\n    headers['Cache-Control'] += `, stale-while-revalidate=${config.staleWhileRevalidate}`;\n  }\n\n  if (config.cacheFirst) {\n    headers['Cache-Control'] += ', immutable';\n  }\n\n  return headers;\n};\n\n/**\n * Preload critical resources with proper cache headers\n */\nexport const preloadCriticalResources = () => {\n  const baseUrl = import.meta.env.BASE_URL;\n  const criticalResources = [\n    {\n      href: `${baseUrl}images/tarcisio_bispo.webp`,\n      as: 'image',\n      type: 'image/webp',\n      fetchpriority: 'high'\n    },\n    {\n      href: `${baseUrl}images/tarcisio_bispo.png`,\n      as: 'image',\n      type: 'image/png',\n      fetchpriority: 'high'\n    }\n    // Font CSS removed to avoid unused preload warnings\n    // Fonts will be loaded naturally by the browser when needed\n  ];\n\n  criticalResources.forEach(resource => {\n    const link = document.createElement('link');\n    link.rel = 'preload';\n    link.href = resource.href;\n    link.as = resource.as;\n\n    if (resource.type) {\n      link.type = resource.type;\n    }\n\n    if (resource.fetchpriority) {\n      link.setAttribute('fetchpriority', resource.fetchpriority);\n    }\n\n    if (resource.crossorigin && resource.crossorigin !== '') {\n      link.setAttribute('crossorigin', resource.crossorigin);\n    }\n\n    // Add error handling for missing resources\n    link.onerror = () => {\n      console.warn(`Failed to preload resource: ${resource.href}`);\n    };\n\n    document.head.appendChild(link);\n  });\n};\n\n/**\n * Implement service worker for advanced caching\n */\nexport const registerServiceWorker = async () => {\n  if ('serviceWorker' in navigator) {\n    try {\n      // Use correct path for GitHub Pages\n      const swPath = import.meta.env.BASE_URL + 'sw.js';\n      const registration = await navigator.serviceWorker.register(swPath);\n      console.log('Service Worker registered successfully:', registration);\n      return registration;\n    } catch (error) {\n      console.warn('Service Worker registration failed:', error);\n      return null;\n    }\n  }\n  return null;\n};\n\n/**\n * Optimize image loading with intersection observer\n */\nexport const createImageObserver = () => {\n  if ('IntersectionObserver' in window) {\n    const imageObserver = new IntersectionObserver((entries) => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          const img = entry.target as HTMLImageElement;\n\n          // Load the image\n          if (img.dataset.src) {\n            img.src = img.dataset.src;\n            img.removeAttribute('data-src');\n          }\n\n          // Load srcset if available\n          if (img.dataset.srcset) {\n            img.srcset = img.dataset.srcset;\n            img.removeAttribute('data-srcset');\n          }\n\n          // Remove loading class\n          img.classList.remove('lazy-loading');\n          img.classList.add('lazy-loaded');\n\n          // Stop observing this image\n          imageObserver.unobserve(img);\n        }\n      });\n    }, {\n      rootMargin: '50px 0px', // Start loading 50px before the image enters viewport\n      threshold: 0.01\n    });\n\n    return imageObserver;\n  }\n\n  return null;\n};\n\n/**\n * Prefetch next page resources\n */\nexport const prefetchNextPageResources = (nextPageUrls: string[]) => {\n  if ('requestIdleCallback' in window) {\n    requestIdleCallback(() => {\n      nextPageUrls.forEach(url => {\n        const link = document.createElement('link');\n        link.rel = 'prefetch';\n        link.href = url;\n        document.head.appendChild(link);\n      });\n    });\n  } else {\n    // Fallback for browsers without requestIdleCallback\n    setTimeout(() => {\n      nextPageUrls.forEach(url => {\n        const link = document.createElement('link');\n        link.rel = 'prefetch';\n        link.href = url;\n        document.head.appendChild(link);\n      });\n    }, 2000);\n  }\n};\n\n/**\n * Optimize font loading\n */\nexport const optimizeFontLoading = () => {\n  // Add font-display: swap to existing font links\n  const fontLinks = document.querySelectorAll('link[href*=\"fonts.googleapis.com\"]');\n  fontLinks.forEach(link => {\n    const href = link.getAttribute('href');\n    if (href && !href.includes('display=swap')) {\n      const url = new URL(href);\n      url.searchParams.set('display', 'swap');\n      link.setAttribute('href', url.toString());\n    }\n  });\n\n  // Skip font preloading to avoid unused preload warnings\n  // Fonts will be loaded when needed by the CSS\n};\n\n/**\n * Initialize all cache optimizations\n */\nexport const initializeCacheOptimizations = () => {\n  // Skip preload in development to avoid warnings\n  if (import.meta.env.PROD) {\n    // Preload critical resources only in production\n    preloadCriticalResources();\n  }\n\n  // Optimize font loading\n  optimizeFontLoading();\n\n  // Register service worker only in production\n  if (import.meta.env.PROD) {\n    registerServiceWorker();\n  }\n\n  // Create image observer for lazy loading\n  const imageObserver = createImageObserver();\n\n  if (imageObserver) {\n    // Observe all images with data-src attribute\n    const lazyImages = document.querySelectorAll('img[data-src]');\n    lazyImages.forEach(img => imageObserver.observe(img));\n  }\n\n  // Skip prefetch in development\n  if (import.meta.env.PROD) {\n    // Only prefetch static resources that actually exist\n    // SPA routes are handled by React Router and don't need prefetching\n    const baseUrl = import.meta.env.BASE_URL;\n    const nextPageUrls: string[] = [\n      // Add only static files that exist in dist folder\n      // SPA routes like /privacy-policy are handled by React Router\n    ];\n\n    if (nextPageUrls.length > 0) {\n      prefetchNextPageResources(nextPageUrls);\n    }\n  }\n};\n\nexport default {\n  getCacheHeaders,\n  preloadCriticalResources,\n  registerServiceWorker,\n  createImageObserver,\n  prefetchNextPageResources,\n  optimizeFontLoading,\n  initializeCacheOptimizations\n};\n", "/**\n * Image Optimization Utilities\n * Handles image loading, fallbacks, and error handling\n */\n\ninterface ImageLoadOptions {\n  src: string;\n  fallback?: string;\n  placeholder?: string;\n  retryAttempts?: number;\n  retryDelay?: number;\n}\n\n/**\n * Preload critical images to improve LCP\n */\nexport const preloadCriticalImages = () => {\n  const baseUrl = import.meta.env.BASE_URL;\n  const criticalImages = [\n    `${baseUrl}images/tarcisio_bispo.webp`,\n    `${baseUrl}images/tarcisio_bispo.png`,\n    `${baseUrl}images/ixdf-symbol-dark.png`,\n    `${baseUrl}images/ixdf-symbol-white.png`\n  ];\n\n  criticalImages.forEach(src => {\n    const link = document.createElement('link');\n    link.rel = 'preload';\n    link.as = 'image';\n    link.href = src;\n\n    // Add error handling for missing images\n    link.onerror = () => {\n      console.warn(`Failed to preload image: ${src}`);\n    };\n\n    document.head.appendChild(link);\n  });\n};\n\n/**\n * Load image with retry logic and fallback\n */\nexport const loadImageWithFallback = async (options: ImageLoadOptions): Promise<string> => {\n  const { src, fallback, retryAttempts = 3, retryDelay = 1000 } = options;\n\n  for (let attempt = 0; attempt < retryAttempts; attempt++) {\n    try {\n      await loadImage(src);\n      return src;\n    } catch (error) {\n      console.warn(`Image load attempt ${attempt + 1} failed for: ${src}`);\n\n      if (attempt < retryAttempts - 1) {\n        await delay(retryDelay);\n      }\n    }\n  }\n\n  // If all attempts failed, try fallback\n  if (fallback) {\n    try {\n      await loadImage(fallback);\n      return fallback;\n    } catch (error) {\n      console.warn(`Fallback image also failed: ${fallback}`);\n    }\n  }\n\n  // Return placeholder or empty string\n  return options.placeholder || '';\n};\n\n/**\n * Load a single image\n */\nconst loadImage = (src: string): Promise<void> => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onload = () => resolve();\n    img.onerror = () => reject(new Error(`Failed to load image: ${src}`));\n    img.src = src;\n  });\n};\n\n/**\n * Delay utility\n */\nconst delay = (ms: number): Promise<void> => {\n  return new Promise(resolve => setTimeout(resolve, ms));\n};\n\n/**\n * Generate responsive image URLs\n */\nexport const generateResponsiveImageUrls = (baseSrc: string, sizes: number[]): string => {\n  const baseUrl = baseSrc.split('.').slice(0, -1).join('.');\n  const extension = baseSrc.split('.').pop();\n\n  return sizes\n    .map(size => `${baseUrl}_${size}w.${extension} ${size}w`)\n    .join(', ');\n};\n\n/**\n * Check if image exists\n */\nexport const imageExists = async (src: string): Promise<boolean> => {\n  try {\n    await loadImage(src);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\n/**\n * Get optimized image path with fallbacks\n */\nexport const getOptimizedImagePath = (imageName: string): {\n  webp: string;\n  png: string;\n  fallback: string;\n} => {\n  const baseUrl = import.meta.env.BASE_URL;\n  const basePath = `${baseUrl}images/`;\n  const name = imageName.replace(/\\.(webp|png|jpg|jpeg)$/i, '');\n\n  return {\n    webp: `${basePath}${name}.webp`,\n    png: `${basePath}${name}.png`,\n    fallback: `${basePath}placeholder.png`\n  };\n};\n\n/**\n * Create placeholder image data URL\n */\nexport const createPlaceholderDataUrl = (width: number, height: number, color = '#f3f4f6'): string => {\n  const svg = `\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <rect width=\"100%\" height=\"100%\" fill=\"${color}\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\".3em\" fill=\"#9ca3af\" font-family=\"Arial, sans-serif\" font-size=\"14\">\n        Loading...\n      </text>\n    </svg>\n  `;\n\n  return `data:image/svg+xml;base64,${btoa(svg)}`;\n};\n\n/**\n * Optimize image loading with intersection observer\n */\nexport const createImageIntersectionObserver = (callback: (entries: IntersectionObserverEntry[]) => void) => {\n  if (!('IntersectionObserver' in window)) {\n    // Fallback for browsers without IntersectionObserver\n    return null;\n  }\n\n  return new IntersectionObserver(callback, {\n    rootMargin: '50px 0px',\n    threshold: 0.01\n  });\n};\n\n/**\n * Batch preload images\n */\nexport const batchPreloadImages = async (imageSrcs: string[], batchSize = 3): Promise<void> => {\n  for (let i = 0; i < imageSrcs.length; i += batchSize) {\n    const batch = imageSrcs.slice(i, i + batchSize);\n\n    await Promise.allSettled(\n      batch.map(src => loadImageWithFallback({ src }))\n    );\n\n    // Small delay between batches to avoid overwhelming the network\n    if (i + batchSize < imageSrcs.length) {\n      await delay(100);\n    }\n  }\n};\n\n/**\n * Get image dimensions\n */\nexport const getImageDimensions = (src: string): Promise<{ width: number; height: number }> => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onload = () => {\n      resolve({\n        width: img.naturalWidth,\n        height: img.naturalHeight\n      });\n    };\n    img.onerror = () => reject(new Error(`Failed to load image: ${src}`));\n    img.src = src;\n  });\n};\n\n/**\n * Initialize image optimizations\n */\nexport const initializeImageOptimizations = () => {\n  // Preload critical images\n  preloadCriticalImages();\n\n  // Set up intersection observer for lazy loading\n  const observer = createImageIntersectionObserver((entries) => {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        const img = entry.target as HTMLImageElement;\n        const src = img.dataset.src;\n\n        if (src) {\n          loadImageWithFallback({ src })\n            .then(loadedSrc => {\n              img.src = loadedSrc;\n              img.removeAttribute('data-src');\n              img.classList.remove('lazy-loading');\n              img.classList.add('lazy-loaded');\n            })\n            .catch(error => {\n              console.warn('Failed to load lazy image:', error);\n              img.classList.add('lazy-error');\n            });\n        }\n\n        observer?.unobserve(img);\n      }\n    });\n  });\n\n  // Observe all images with data-src attribute\n  if (observer) {\n    const lazyImages = document.querySelectorAll('img[data-src]');\n    lazyImages.forEach(img => observer.observe(img));\n  }\n};\n\nexport default {\n  preloadCriticalImages,\n  loadImageWithFallback,\n  generateResponsiveImageUrls,\n  imageExists,\n  getOptimizedImagePath,\n  createPlaceholderDataUrl,\n  createImageIntersectionObserver,\n  batchPreloadImages,\n  getImageDimensions,\n  initializeImageOptimizations\n};\n", "/**\n * Content Security Policy (CSP) Security Utilities\n * Prevents eval() usage and implements secure alternatives\n */\n\n/**\n * Secure alternatives to eval() and Function constructor\n */\nexport class CSPSecurity {\n  /**\n   * Secure JSON parsing without eval()\n   */\n  static parseJSON(jsonString: string): any {\n    try {\n      return JSON.parse(jsonString);\n    } catch (error) {\n      console.error('CSPSecurity: Invalid JSON string:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Secure template string replacement without eval()\n   */\n  static interpolateTemplate(template: string, variables: Record<string, any>): string {\n    return template.replace(/\\{\\{(\\w+)\\}\\}/g, (match, key) => {\n      return variables[key] !== undefined ? String(variables[key]) : match;\n    });\n  }\n\n  /**\n   * Secure dynamic property access without eval()\n   */\n  static getNestedProperty(obj: any, path: string): any {\n    return path.split('.').reduce((current, key) => {\n      return current && current[key] !== undefined ? current[key] : undefined;\n    }, obj);\n  }\n\n  /**\n   * Secure dynamic property setting without eval()\n   */\n  static setNestedProperty(obj: any, path: string, value: any): void {\n    const keys = path.split('.');\n    const lastKey = keys.pop();\n\n    if (!lastKey) return;\n\n    const target = keys.reduce((current, key) => {\n      if (!current[key] || typeof current[key] !== 'object') {\n        current[key] = {};\n      }\n      return current[key];\n    }, obj);\n\n    target[lastKey] = value;\n  }\n\n  /**\n   * Secure script execution without eval()\n   * Only allows predefined safe operations\n   */\n  static executeSafeOperation(operation: string, context: Record<string, any>): any {\n    const safeOperations: Record<string, Function> = {\n      'add': (a: number, b: number) => a + b,\n      'subtract': (a: number, b: number) => a - b,\n      'multiply': (a: number, b: number) => a * b,\n      'divide': (a: number, b: number) => b !== 0 ? a / b : 0,\n      'concat': (a: string, b: string) => a + b,\n      'uppercase': (str: string) => str.toUpperCase(),\n      'lowercase': (str: string) => str.toLowerCase(),\n      'length': (arr: any[]) => arr.length,\n      'reverse': (arr: any[]) => [...arr].reverse(),\n      'sort': (arr: any[]) => [...arr].sort()\n    };\n\n    if (safeOperations[operation]) {\n      return safeOperations[operation];\n    }\n\n    console.warn('CSPSecurity: Unsafe operation blocked:', operation);\n    return null;\n  }\n\n  /**\n   * Secure timeout/interval without string evaluation\n   */\n  static secureSetTimeout(callback: Function, delay: number): number {\n    if (typeof callback !== 'function') {\n      console.error('CSPSecurity: setTimeout callback must be a function');\n      return 0;\n    }\n    return window.setTimeout(callback, delay);\n  }\n\n  static secureSetInterval(callback: Function, delay: number): number {\n    if (typeof callback !== 'function') {\n      console.error('CSPSecurity: setInterval callback must be a function');\n      return 0;\n    }\n    return window.setInterval(callback, delay);\n  }\n\n  /**\n   * Secure dynamic import without eval()\n   */\n  static async secureImport(modulePath: string): Promise<any> {\n    // Validate module path to prevent injection\n    if (!this.isValidModulePath(modulePath)) {\n      throw new Error('CSPSecurity: Invalid module path');\n    }\n\n    try {\n      return await import(/* @vite-ignore */ modulePath);\n    } catch (error) {\n      console.error('CSPSecurity: Failed to import module:', modulePath, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Validate module path for security\n   */\n  private static isValidModulePath(path: string): boolean {\n    // Allow only relative paths and npm packages\n    const validPatterns = [\n      /^\\.\\//, // Relative paths starting with ./\n      /^\\.\\.\\//, // Relative paths starting with ../\n      /^@?[a-zA-Z0-9_-]+\\/[a-zA-Z0-9_/-]+$/, // npm packages\n      /^[a-zA-Z0-9_-]+$/ // Simple package names\n    ];\n\n    return validPatterns.some(pattern => pattern.test(path));\n  }\n\n  /**\n   * Secure HTML sanitization without eval()\n   */\n  static sanitizeHTML(html: string): string {\n    const div = document.createElement('div');\n    div.textContent = html;\n    return div.innerHTML;\n  }\n\n  /**\n   * Secure URL construction without eval()\n   */\n  static buildSecureURL(base: string, params: Record<string, string>): string {\n    try {\n      const url = new URL(base);\n\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.set(key, value);\n      });\n\n      return url.toString();\n    } catch (error) {\n      console.error('CSPSecurity: Invalid URL construction:', error);\n      return base;\n    }\n  }\n\n  /**\n   * Install CSP security interceptors\n   */\n  static installCSPInterceptors(): void {\n    // Intercept and warn about eval() usage (non-blocking in development)\n    if (typeof window !== 'undefined') {\n      const originalEval = window.eval;\n\n      window.eval = function(code: string) {\n        if (import.meta.env.DEV) {\n          console.warn('CSPSecurity: eval() usage detected. Consider using secure alternatives.');\n          return originalEval.call(this, code);\n        } else {\n          console.error('CSPSecurity: eval() usage blocked for security. Use secure alternatives.');\n          throw new Error('eval() is not allowed due to Content Security Policy');\n        }\n      };\n\n      // Intercept Function constructor (warn in dev, block in prod)\n      const originalFunction = window.Function;\n\n      window.Function = function(...args: any[]) {\n        if (import.meta.env.DEV) {\n          console.warn('CSPSecurity: Function constructor usage detected. Consider using secure alternatives.');\n          return originalFunction.apply(this, args);\n        } else {\n          console.error('CSPSecurity: Function constructor blocked for security.');\n          throw new Error('Function constructor is not allowed due to Content Security Policy');\n        }\n      } as any;\n\n      // Intercept setTimeout/setInterval with string arguments\n      const originalSetTimeout = window.setTimeout;\n      const originalSetInterval = window.setInterval;\n\n      window.setTimeout = function(handler: any, timeout?: number, ...args: any[]): number {\n        if (typeof handler === 'string') {\n          if (import.meta.env.DEV) {\n            console.warn('CSPSecurity: setTimeout with string detected. Consider using function instead.');\n            return originalSetTimeout.call(this, handler, timeout, ...args);\n          } else {\n            console.error('CSPSecurity: setTimeout with string blocked. Use function instead.');\n            throw new Error('setTimeout with string is not allowed due to Content Security Policy');\n          }\n        }\n        return originalSetTimeout.call(this, handler, timeout, ...args);\n      };\n\n      window.setInterval = function(handler: any, timeout?: number, ...args: any[]): number {\n        if (typeof handler === 'string') {\n          if (import.meta.env.DEV) {\n            console.warn('CSPSecurity: setInterval with string detected. Consider using function instead.');\n            return originalSetInterval.call(this, handler, timeout, ...args);\n          } else {\n            console.error('CSPSecurity: setInterval with string blocked. Use function instead.');\n            throw new Error('setInterval with string is not allowed due to Content Security Policy');\n          }\n        }\n        return originalSetInterval.call(this, handler, timeout, ...args);\n      };\n\n      console.log('CSPSecurity: Security interceptors installed successfully');\n    }\n  }\n\n  /**\n   * Generate CSP nonce for inline scripts\n   */\n  static generateNonce(): string {\n    const array = new Uint8Array(16);\n    crypto.getRandomValues(array);\n    return btoa(String.fromCharCode(...array));\n  }\n\n  /**\n   * Validate CSP compliance\n   */\n  static validateCSPCompliance(): boolean {\n    const violations: string[] = [];\n\n    // Check if our interceptors are in place\n    if (typeof window !== 'undefined') {\n      // Check if eval is properly intercepted\n      const evalString = window.eval.toString();\n      if (!evalString.includes('eval() usage blocked')) {\n        violations.push('eval() interceptor not installed');\n      }\n\n      // Check if Function constructor is properly intercepted\n      const functionString = window.Function.toString();\n      if (!functionString.includes('Function constructor blocked')) {\n        violations.push('Function constructor interceptor not installed');\n      }\n    }\n\n    if (violations.length > 0) {\n      console.warn('CSPSecurity: CSP violations detected:', violations);\n      return false;\n    }\n\n    console.log('CSPSecurity: CSP compliance validated successfully');\n    return true;\n  }\n}\n\n/**\n * Initialize CSP security on application start\n */\nexport const initializeCSPSecurity = (): void => {\n  if (typeof window !== 'undefined') {\n    // Only install interceptors in production to avoid development issues\n    if (import.meta.env.PROD) {\n      CSPSecurity.installCSPInterceptors();\n\n      // Validate compliance after installation\n      setTimeout(() => {\n        CSPSecurity.validateCSPCompliance();\n      }, 100);\n    } else {\n      console.log('CSPSecurity: Skipped in development mode');\n    }\n  }\n};\n\nexport default CSPSecurity;\n", "import './react-fix'\r\nimport * as React from 'react'\r\nimport * as ReactDOM from 'react-dom/client'\r\nimport App from './App.tsx'\r\n// ===== CSS MODULAR UNIFICADO - IMPORTAÇÃO ÚNICA =====\r\nimport './styles/index.css' // Sistema CSS modular completo\r\nimport './components/ui/buttons/styles/index.css' // CSS modular dos botões\r\nimport './components/ui/modals/styles/index.css' // CSS modular dos modais\r\nimport './components/mobile/styles/index.css' // CSS mobile unificado\r\nimport { ThemeProvider } from './components/providers/ThemeProvider'\r\nimport { initializeCacheOptimizations } from './utils/cacheOptimization'\r\nimport { initializeImageOptimizations } from './utils/imageOptimization'\r\nimport { initializeCSPSecurity } from './utils/cspSecurity'\r\n\r\n// Import i18n synchronously to avoid translation errors\r\nimport './i18n/config';\r\n\r\n// Garantir que React está disponível globalmente\r\nif (typeof window !== 'undefined') {\r\n  (window as any).React = React;\r\n}\r\n\r\nconst rootElement = document.getElementById('root');\r\nif (!rootElement) {\r\n  throw new Error('Root element not found');\r\n}\r\n\r\n// Renderizar app imediatamente para melhor LCP\r\nconst root = ReactDOM.createRoot(rootElement);\r\n\r\n// Initialize optimizations after DOM is ready\r\nif (typeof window !== 'undefined') {\r\n  // Initialize immediately for critical resources\r\n  initializeCacheOptimizations();\r\n  initializeImageOptimizations();\r\n\r\n  // Initialize security after React is loaded\r\n  setTimeout(() => {\r\n    initializeCSPSecurity();\r\n  }, 1000);\r\n}\r\n\r\n// Render immediately with critical content\r\nroot.render(\r\n  <React.StrictMode>\r\n    <ThemeProvider defaultTheme=\"system\" storageKey=\"portfolio-theme\">\r\n      <App />\r\n    </ThemeProvider>\r\n  </React.StrictMode>\r\n);\r\n"], "file": "js/index-Dn5J_98a.js"}