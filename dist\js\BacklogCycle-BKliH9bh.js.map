{"version": 3, "file": "BacklogCycle-BKliH9bh.js", "sources": ["../../node_modules/@radix-ui/react-collapsible/dist/index.mjs", "../../node_modules/@radix-ui/react-direction/dist/index.mjs", "../../node_modules/@radix-ui/react-accordion/dist/index.mjs", "../../src/components/ui/accordion.tsx", "../../src/components/BacklogCycle.tsx"], "sourcesContent": ["\"use client\";\n\n// src/collapsible.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { jsx } from \"react/jsx-runtime\";\nvar COLLAPSIBLE_NAME = \"Collapsible\";\nvar [createCollapsibleContext, createCollapsibleScope] = createContextScope(COLLAPSIBLE_NAME);\nvar [CollapsibleProvider, useCollapsibleContext] = createCollapsibleContext(COLLAPSIBLE_NAME);\nvar Collapsible = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeCollapsible,\n      open: openProp,\n      defaultOpen,\n      disabled,\n      onOpenChange,\n      ...collapsibleProps\n    } = props;\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? false,\n      onChange: onOpenChange,\n      caller: COLLAPSIBLE_NAME\n    });\n    return /* @__PURE__ */ jsx(\n      CollapsibleProvider,\n      {\n        scope: __scopeCollapsible,\n        disabled,\n        contentId: useId(),\n        open,\n        onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            \"data-state\": getState(open),\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...collapsibleProps,\n            ref: forwardedRef\n          }\n        )\n      }\n    );\n  }\n);\nCollapsible.displayName = COLLAPSIBLE_NAME;\nvar TRIGGER_NAME = \"CollapsibleTrigger\";\nvar CollapsibleTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open || false,\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        disabled: context.disabled,\n        ...triggerProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, context.onOpenToggle)\n      }\n    );\n  }\n);\nCollapsibleTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"CollapsibleContent\";\nvar CollapsibleContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: ({ present }) => /* @__PURE__ */ jsx(CollapsibleContentImpl, { ...contentProps, ref: forwardedRef, present }) });\n  }\n);\nCollapsibleContent.displayName = CONTENT_NAME;\nvar CollapsibleContentImpl = React.forwardRef((props, forwardedRef) => {\n  const { __scopeCollapsible, present, children, ...contentProps } = props;\n  const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n  const [isPresent, setIsPresent] = React.useState(present);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const heightRef = React.useRef(0);\n  const height = heightRef.current;\n  const widthRef = React.useRef(0);\n  const width = widthRef.current;\n  const isOpen = context.open || isPresent;\n  const isMountAnimationPreventedRef = React.useRef(isOpen);\n  const originalStylesRef = React.useRef(void 0);\n  React.useEffect(() => {\n    const rAF = requestAnimationFrame(() => isMountAnimationPreventedRef.current = false);\n    return () => cancelAnimationFrame(rAF);\n  }, []);\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node) {\n      originalStylesRef.current = originalStylesRef.current || {\n        transitionDuration: node.style.transitionDuration,\n        animationName: node.style.animationName\n      };\n      node.style.transitionDuration = \"0s\";\n      node.style.animationName = \"none\";\n      const rect = node.getBoundingClientRect();\n      heightRef.current = rect.height;\n      widthRef.current = rect.width;\n      if (!isMountAnimationPreventedRef.current) {\n        node.style.transitionDuration = originalStylesRef.current.transitionDuration;\n        node.style.animationName = originalStylesRef.current.animationName;\n      }\n      setIsPresent(present);\n    }\n  }, [context.open, present]);\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-state\": getState(context.open),\n      \"data-disabled\": context.disabled ? \"\" : void 0,\n      id: context.contentId,\n      hidden: !isOpen,\n      ...contentProps,\n      ref: composedRefs,\n      style: {\n        [`--radix-collapsible-content-height`]: height ? `${height}px` : void 0,\n        [`--radix-collapsible-content-width`]: width ? `${width}px` : void 0,\n        ...props.style\n      },\n      children: isOpen && children\n    }\n  );\n});\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar Root = Collapsible;\nvar Trigger = CollapsibleTrigger;\nvar Content = CollapsibleContent;\nexport {\n  Collapsible,\n  CollapsibleContent,\n  CollapsibleTrigger,\n  Content,\n  Root,\n  Trigger,\n  createCollapsibleScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/accordion.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\";\nimport { createCollapsibleScope } from \"@radix-ui/react-collapsible\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ACCORDION_NAME = \"Accordion\";\nvar ACCORDION_KEYS = [\"Home\", \"End\", \"ArrowDown\", \"ArrowUp\", \"ArrowLeft\", \"ArrowRight\"];\nvar [Collection, useCollection, createCollectionScope] = createCollection(ACCORDION_NAME);\nvar [createAccordionContext, createAccordionScope] = createContextScope(ACCORDION_NAME, [\n  createCollectionScope,\n  createCollapsibleScope\n]);\nvar useCollapsibleScope = createCollapsibleScope();\nvar Accordion = React.forwardRef(\n  (props, forwardedRef) => {\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps;\n    const multipleProps = accordionProps;\n    return /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeAccordion, children: type === \"multiple\" ? /* @__PURE__ */ jsx(AccordionImplMultiple, { ...multipleProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(AccordionImplSingle, { ...singleProps, ref: forwardedRef }) });\n  }\n);\nAccordion.displayName = ACCORDION_NAME;\nvar [AccordionValueProvider, useAccordionValueContext] = createAccordionContext(ACCORDION_NAME);\nvar [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(\n  ACCORDION_NAME,\n  { collapsible: false }\n);\nvar AccordionImplSingle = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      value: valueProp,\n      defaultValue,\n      onValueChange = () => {\n      },\n      collapsible = false,\n      ...accordionSingleProps\n    } = props;\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? \"\",\n      onChange: onValueChange,\n      caller: ACCORDION_NAME\n    });\n    return /* @__PURE__ */ jsx(\n      AccordionValueProvider,\n      {\n        scope: props.__scopeAccordion,\n        value: React.useMemo(() => value ? [value] : [], [value]),\n        onItemOpen: setValue,\n        onItemClose: React.useCallback(() => collapsible && setValue(\"\"), [collapsible, setValue]),\n        children: /* @__PURE__ */ jsx(AccordionCollapsibleProvider, { scope: props.__scopeAccordion, collapsible, children: /* @__PURE__ */ jsx(AccordionImpl, { ...accordionSingleProps, ref: forwardedRef }) })\n      }\n    );\n  }\n);\nvar AccordionImplMultiple = React.forwardRef((props, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {\n    },\n    ...accordionMultipleProps\n  } = props;\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: ACCORDION_NAME\n  });\n  const handleItemOpen = React.useCallback(\n    (itemValue) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n  const handleItemClose = React.useCallback(\n    (itemValue) => setValue((prevValue = []) => prevValue.filter((value2) => value2 !== itemValue)),\n    [setValue]\n  );\n  return /* @__PURE__ */ jsx(\n    AccordionValueProvider,\n    {\n      scope: props.__scopeAccordion,\n      value,\n      onItemOpen: handleItemOpen,\n      onItemClose: handleItemClose,\n      children: /* @__PURE__ */ jsx(AccordionCollapsibleProvider, { scope: props.__scopeAccordion, collapsible: true, children: /* @__PURE__ */ jsx(AccordionImpl, { ...accordionMultipleProps, ref: forwardedRef }) })\n    }\n  );\n});\nvar [AccordionImplProvider, useAccordionContext] = createAccordionContext(ACCORDION_NAME);\nvar AccordionImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, disabled, dir, orientation = \"vertical\", ...accordionProps } = props;\n    const accordionRef = React.useRef(null);\n    const composedRefs = useComposedRefs(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const handleKeyDown = composeEventHandlers(props.onKeyDown, (event) => {\n      if (!ACCORDION_KEYS.includes(event.key)) return;\n      const target = event.target;\n      const triggerCollection = getItems().filter((item) => !item.ref.current?.disabled);\n      const triggerIndex = triggerCollection.findIndex((item) => item.ref.current === target);\n      const triggerCount = triggerCollection.length;\n      if (triggerIndex === -1) return;\n      event.preventDefault();\n      let nextIndex = triggerIndex;\n      const homeIndex = 0;\n      const endIndex = triggerCount - 1;\n      const moveNext = () => {\n        nextIndex = triggerIndex + 1;\n        if (nextIndex > endIndex) {\n          nextIndex = homeIndex;\n        }\n      };\n      const movePrev = () => {\n        nextIndex = triggerIndex - 1;\n        if (nextIndex < homeIndex) {\n          nextIndex = endIndex;\n        }\n      };\n      switch (event.key) {\n        case \"Home\":\n          nextIndex = homeIndex;\n          break;\n        case \"End\":\n          nextIndex = endIndex;\n          break;\n        case \"ArrowRight\":\n          if (orientation === \"horizontal\") {\n            if (isDirectionLTR) {\n              moveNext();\n            } else {\n              movePrev();\n            }\n          }\n          break;\n        case \"ArrowDown\":\n          if (orientation === \"vertical\") {\n            moveNext();\n          }\n          break;\n        case \"ArrowLeft\":\n          if (orientation === \"horizontal\") {\n            if (isDirectionLTR) {\n              movePrev();\n            } else {\n              moveNext();\n            }\n          }\n          break;\n        case \"ArrowUp\":\n          if (orientation === \"vertical\") {\n            movePrev();\n          }\n          break;\n      }\n      const clampedIndex = nextIndex % triggerCount;\n      triggerCollection[clampedIndex].ref.current?.focus();\n    });\n    return /* @__PURE__ */ jsx(\n      AccordionImplProvider,\n      {\n        scope: __scopeAccordion,\n        disabled,\n        direction: dir,\n        orientation,\n        children: /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeAccordion, children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            ...accordionProps,\n            \"data-orientation\": orientation,\n            ref: composedRefs,\n            onKeyDown: disabled ? void 0 : handleKeyDown\n          }\n        ) })\n      }\n    );\n  }\n);\nvar ITEM_NAME = \"AccordionItem\";\nvar [AccordionItemProvider, useAccordionItemContext] = createAccordionContext(ITEM_NAME);\nvar AccordionItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = useId();\n    const open = value && valueContext.value.includes(value) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n    return /* @__PURE__ */ jsx(\n      AccordionItemProvider,\n      {\n        scope: __scopeAccordion,\n        open,\n        disabled,\n        triggerId,\n        children: /* @__PURE__ */ jsx(\n          CollapsiblePrimitive.Root,\n          {\n            \"data-orientation\": accordionContext.orientation,\n            \"data-state\": getState(open),\n            ...collapsibleScope,\n            ...accordionItemProps,\n            ref: forwardedRef,\n            disabled,\n            open,\n            onOpenChange: (open2) => {\n              if (open2) {\n                valueContext.onItemOpen(value);\n              } else {\n                valueContext.onItemClose(value);\n              }\n            }\n          }\n        )\n      }\n    );\n  }\n);\nAccordionItem.displayName = ITEM_NAME;\nvar HEADER_NAME = \"AccordionHeader\";\nvar AccordionHeader = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return /* @__PURE__ */ jsx(\n      Primitive.h3,\n      {\n        \"data-orientation\": accordionContext.orientation,\n        \"data-state\": getState(itemContext.open),\n        \"data-disabled\": itemContext.disabled ? \"\" : void 0,\n        ...headerProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nAccordionHeader.displayName = HEADER_NAME;\nvar TRIGGER_NAME = \"AccordionTrigger\";\nvar AccordionTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: __scopeAccordion, children: /* @__PURE__ */ jsx(\n      CollapsiblePrimitive.Trigger,\n      {\n        \"aria-disabled\": itemContext.open && !collapsibleContext.collapsible || void 0,\n        \"data-orientation\": accordionContext.orientation,\n        id: itemContext.triggerId,\n        ...collapsibleScope,\n        ...triggerProps,\n        ref: forwardedRef\n      }\n    ) });\n  }\n);\nAccordionTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"AccordionContent\";\nvar AccordionContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ jsx(\n      CollapsiblePrimitive.Content,\n      {\n        role: \"region\",\n        \"aria-labelledby\": itemContext.triggerId,\n        \"data-orientation\": accordionContext.orientation,\n        ...collapsibleScope,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n          [\"--radix-accordion-content-height\"]: \"var(--radix-collapsible-content-height)\",\n          [\"--radix-accordion-content-width\"]: \"var(--radix-collapsible-content-width)\",\n          ...props.style\n        }\n      }\n    );\n  }\n);\nAccordionContent.displayName = CONTENT_NAME;\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar Root2 = Accordion;\nvar Item = AccordionItem;\nvar Header = AccordionHeader;\nvar Trigger2 = AccordionTrigger;\nvar Content2 = AccordionContent;\nexport {\n  Accordion,\n  AccordionContent,\n  AccordionHeader,\n  AccordionItem,\n  AccordionTrigger,\n  Content2 as Content,\n  Header,\n  Item,\n  Root2 as Root,\n  Trigger2 as Trigger,\n  createAccordionScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDown } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Accordion = AccordionPrimitive.Root\r\n\r\nconst AccordionItem = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\r\n>(({ className, ...props }, ref) => (\r\n  <AccordionPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\"border-b\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAccordionItem.displayName = \"AccordionItem\"\r\n\r\nconst AccordionTrigger = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Header asChild>\r\n    <div className=\"flex\">\r\n      <AccordionPrimitive.Trigger\r\n        ref={ref}\r\n        className={cn(\r\n          \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\r\n      </AccordionPrimitive.Trigger>\r\n    </div>\r\n  </AccordionPrimitive.Header>\r\n))\r\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\r\n\r\nconst AccordionContent = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Content\r\n    ref={ref}\r\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\r\n    {...props}\r\n  >\r\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\r\n  </AccordionPrimitive.Content>\r\n))\r\n\r\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n", "import React, { useState, memo, useCallback, useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger\r\n} from '@/components/ui/accordion';\r\nimport { ChevronLeft, ChevronRight, CheckCircle2, Lightbulb, Target, TrendingUp } from 'lucide-react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { CTAButton } from '@/components/ui/buttons';\r\nimport { useTranslationArray } from '@/utils/translationHelpers';\r\nimport { useProjectSounds, useNavigationSounds } from '@/hooks/useSound';\r\n\r\ninterface BacklogItem {\r\n  id: string;\r\n  challenge: string;\r\n  solution: string;\r\n  result: string;\r\n  note: string;\r\n}\r\n\r\nconst ITEMS_PER_PAGE = 4;\r\n\r\nconst BacklogCycle: React.FC = () => {\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const { t } = useTranslation();\r\n  const { playExpand, playCollapse } = useProjectSounds();\r\n  const { playButtonClick, playButtonHover } = useNavigationSounds();\r\n\r\n  // Agora os itens vêm das traduções usando função utilitária segura\r\n  const backlogItems = useTranslationArray('backlog.items', t) as Array<{\r\n    challenge: string;\r\n    solution: string;\r\n    result: string;\r\n    note: string;\r\n  }>;\r\n\r\n  // Adicionar IDs aos itens para o Accordion\r\n  const backlogItemsWithIds = backlogItems.map((item, index) => ({\r\n    ...item,\r\n    id: `backlog-${index + 1}`\r\n  }));\r\n\r\n  const totalPages = Math.ceil(backlogItemsWithIds.length / ITEMS_PER_PAGE);\r\n  const startIdx = (currentPage - 1) * ITEMS_PER_PAGE;\r\n  const endIdx = startIdx + ITEMS_PER_PAGE;\r\n  const paginatedItems = backlogItemsWithIds.slice(startIdx, endIdx);\r\n\r\n\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full\">\r\n      {/* Header Section - Alinhado com Cards */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 30 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n        className=\"mb-16\"\r\n      >\r\n        <div className=\"max-w-4xl mx-auto text-left px-4 sm:px-6 lg:px-8\">\r\n          <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-text)] mb-4\">\r\n            {t('backlog.title')}\r\n          </h1>\r\n          <p className=\"text-[var(--color-muted)] text-lg mb-4\">\r\n            {t('backlog.description')}\r\n          </p>\r\n          {/* Linha Azul Animada - Similar ao Hero */}\r\n          <motion.div\r\n            initial={{ width: 0 }}\r\n            animate={{ width: \"120px\" }}\r\n            transition={{ duration: 0.8, delay: 0.6 }}\r\n            className=\"h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full\"\r\n          ></motion.div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Content Section - Alinhado com Header */}\r\n      <motion.div\r\n        className=\"w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\"\r\n        variants={containerVariants}\r\n        initial=\"hidden\"\r\n        whileInView=\"visible\"\r\n        viewport={{ once: true, margin: \"-100px\" }}\r\n      >\r\n        <Accordion type=\"single\" collapsible className=\"w-full space-y-4\">\r\n          {paginatedItems.length === 0 ? (\r\n            <div className=\"text-center py-12 text-[var(--color-muted)]\">\r\n              {t('backlog.noItems')}\r\n            </div>\r\n          ) : (\r\n            paginatedItems.map((item, index) => (\r\n              <AccordionItem value={item.id} key={item.id} className=\"border-none\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  className=\"bg-white dark:bg-[var(--color-surface)] rounded-2xl border border-[var(--color-border)] shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden\"\r\n                >\r\n                  <AccordionTrigger className=\"px-6 py-6 hover:no-underline group [&[data-state=open]]:pb-4\">\r\n                    <div className=\"flex items-center gap-4 w-full min-h-[60px]\">\r\n                      {/* Icon Container - Middle Aligned */}\r\n                      <div className=\"flex-shrink-0 w-12 h-12 bg-[var(--color-primary)]/10 rounded-xl flex items-center justify-center self-center\">\r\n                        <CheckCircle2 className=\"w-6 h-6 text-[var(--color-primary)]\" />\r\n                      </div>\r\n\r\n                      {/* Content Container - Middle Aligned */}\r\n                      <div className=\"flex-1 text-left flex items-center\">\r\n                        <p className=\"backlog-challenge-text group-hover:text-[var(--color-primary)] transition-colors duration-200 pr-4\">\r\n                          {item.challenge}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* Chevron Icon - Middle Aligned */}\r\n                      <div className=\"flex-shrink-0 flex items-center justify-center self-center\">\r\n                        {/* O ícone do chevron é automaticamente adicionado pelo AccordionTrigger */}\r\n                      </div>\r\n                    </div>\r\n                  </AccordionTrigger>\r\n                  <AccordionContent>\r\n                    <div className=\"px-6 pb-6\">\r\n                      {/* Grid Responsivo para as seções */}\r\n                      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n\r\n                        {/* Solução */}\r\n                        <motion.div\r\n                          initial={{ opacity: 0, y: 10 }}\r\n                          animate={{ opacity: 1, y: 0 }}\r\n                          transition={{ duration: 0.3, delay: 0.1 }}\r\n                          className=\"bg-[var(--color-primary)]/5 rounded-xl p-4 border-l-4 border-[var(--color-primary)]\"\r\n                        >\r\n                          <div className=\"flex items-center gap-3 mb-3\">\r\n                            <div className=\"w-8 h-8 bg-[var(--color-primary)]/10 rounded-lg flex items-center justify-center\">\r\n                              <Lightbulb className=\"w-4 h-4 text-[var(--color-primary)]\" />\r\n                            </div>\r\n                            <h4 className=\"font-semibold text-[var(--color-text)]\">\r\n                              {t('backlog.solution')}\r\n                            </h4>\r\n                          </div>\r\n                          <p className=\"text-sm text-[var(--color-muted)] leading-relaxed\">\r\n                            {item.solution}\r\n                          </p>\r\n                        </motion.div>\r\n\r\n                        {/* Resultado */}\r\n                        <motion.div\r\n                          initial={{ opacity: 0, y: 10 }}\r\n                          animate={{ opacity: 1, y: 0 }}\r\n                          transition={{ duration: 0.3, delay: 0.2 }}\r\n                          className=\"bg-[var(--color-secondary)]/5 rounded-xl p-4 border-l-4 border-[var(--color-secondary)]\"\r\n                        >\r\n                          <div className=\"flex items-center gap-3 mb-3\">\r\n                            <div className=\"w-8 h-8 bg-[var(--color-secondary)]/10 rounded-lg flex items-center justify-center\">\r\n                              <TrendingUp className=\"w-4 h-4 text-[var(--color-secondary)]\" />\r\n                            </div>\r\n                            <h4 className=\"font-semibold text-[var(--color-text)]\">\r\n                              {t('backlog.result')}\r\n                            </h4>\r\n                          </div>\r\n                          <p className=\"text-sm text-[var(--color-muted)] leading-relaxed\">\r\n                            {item.result}\r\n                          </p>\r\n                        </motion.div>\r\n\r\n                        {/* Nota */}\r\n                        <motion.div\r\n                          initial={{ opacity: 0, y: 10 }}\r\n                          animate={{ opacity: 1, y: 0 }}\r\n                          transition={{ duration: 0.3, delay: 0.3 }}\r\n                          className=\"bg-purple-50 dark:bg-purple-900/10 rounded-xl p-4 border-l-4 border-purple-500\"\r\n                        >\r\n                          <div className=\"flex items-center gap-3 mb-3\">\r\n                            <div className=\"w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center\">\r\n                              <Target className=\"w-4 h-4 text-purple-600 dark:text-purple-400\" />\r\n                            </div>\r\n                            <h4 className=\"font-semibold text-[var(--color-text)]\">\r\n                              {t('backlog.note')}\r\n                            </h4>\r\n                          </div>\r\n                          <p className=\"text-sm text-[var(--color-muted)] leading-relaxed\">\r\n                            {item.note}\r\n                          </p>\r\n                        </motion.div>\r\n\r\n                      </div>\r\n                    </div>\r\n                  </AccordionContent>\r\n                </motion.div>\r\n              </AccordionItem>\r\n            ))\r\n          )}\r\n        </Accordion>\r\n\r\n        {/* Paginação Melhorada */}\r\n        {totalPages > 1 && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.3 }}\r\n            className=\"flex justify-center items-center gap-4 mt-12\"\r\n          >\r\n            <CTAButton\r\n              onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}\r\n              disabled={currentPage === 1}\r\n              variant=\"ghost\"\r\n              size=\"md\"\r\n              icon={ChevronLeft}\r\n              iconPosition=\"left\"\r\n              className=\"disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {t('backlog.previous')}\r\n            </CTAButton>\r\n\r\n            <div className=\"flex items-center gap-2\">\r\n              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (\r\n                <button\r\n                  key={page}\r\n                  onClick={() => {\r\n                    setCurrentPage(page);\r\n                    playButtonClick();\r\n                  }}\r\n                  onMouseEnter={() => playButtonHover()}\r\n                  className={`w-10 h-10 rounded-lg font-medium transition-all duration-200 ${\r\n                    currentPage === page\r\n                      ? 'bg-[var(--color-primary)] text-white shadow-md'\r\n                      : 'text-[var(--color-muted)] hover:bg-[var(--color-primary)]/10 hover:text-[var(--color-primary)]'\r\n                  }`}\r\n                  aria-label={`${t('backlog.page')} ${page}`}\r\n                >\r\n                  {page}\r\n                </button>\r\n              ))}\r\n            </div>\r\n\r\n            <CTAButton\r\n              onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}\r\n              disabled={currentPage === totalPages}\r\n              variant=\"ghost\"\r\n              size=\"md\"\r\n              icon={ChevronRight}\r\n              iconPosition=\"right\"\r\n              className=\"disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {t('backlog.next')}\r\n            </CTAButton>\r\n          </motion.div>\r\n        )}\r\n      </motion.div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default BacklogCycle;\r\n"], "names": ["COLLAPSIBLE_NAME", "createCollapsibleContext", "createCollapsibleScope", "createContextScope", "CollapsibleProvider", "useCollapsibleContext", "Collapsible", "React.forwardRef", "forwardRef", "props", "forwardedRef", "__scopeCollapsible", "open", "openProp", "defaultOpen", "disabled", "onOpenChange", "collapsibleProps", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "onChange", "caller", "jsxRuntimeExports", "jsx", "scope", "contentId", "useId", "onOpenToggle", "React.useCallback", "prevOpen", "children", "Primitive", "div", "getState", "ref", "displayName", "TRIGGER_NAME", "CollapsibleTrigger", "triggerProps", "context", "button", "type", "onClick", "composeEventHandlers", "CONTENT_NAME", "Collapsible<PERSON><PERSON>nt", "forceMount", "contentProps", "Presence", "present", "CollapsibleContentImpl", "isPresent", "setIsPresent", "React.useState", "React.useRef", "useRef", "composedRefs", "useComposedRefs", "heightRef", "height", "current", "widthRef", "width", "isOpen", "isMountAnimationPreventedRef", "originalStylesRef", "React.useEffect", "rAF", "requestAnimationFrame", "cancelAnimationFrame", "useLayoutEffect", "node", "transitionDuration", "style", "animationName", "rect", "getBoundingClientRect", "id", "hidden", "Root", "<PERSON><PERSON>", "Content", "DirectionContext", "React.createContext", "createContext", "ACCORDION_NAME", "ACCORDION_KEYS", "Collection", "useCollection", "createCollectionScope", "createCollection", "createAccordionContext", "createAccordionScope", "useCollapsibleScope", "Accordion", "React", "accordionProps", "singleProps", "multipleProps", "Provider", "__scopeAccordion", "AccordionImplMultiple", "AccordionImplSingle", "Accordion<PERSON><PERSON><PERSON>", "useAccordionValueContext", "AccordionCollapsibleProvider", "useAccordionCollapsibleContext", "collapsible", "value", "valueProp", "defaultValue", "onValueChange", "accordionSingleProps", "setValue", "useMemo", "onItemOpen", "onItemClose", "useCallback", "AccordionImpl", "accordionMultipleProps", "handleItemOpen", "itemValue", "prevValue", "handleItemClose", "filter", "value2", "AccordionImplProvider", "useAccordionContext", "dir", "orientation", "accordionRef", "getItems", "isDirectionLTR", "localDir", "globalDir", "React.useContext", "useContext", "useDirection", "handleKeyDown", "onKeyDown", "event", "includes", "key", "target", "triggerCollection", "item", "_a", "triggerIndex", "findIndex", "triggerCount", "length", "preventDefault", "nextIndex", "endIndex", "moveNext", "movePrev", "focus", "direction", "Slot", "ITEM_NAME", "AccordionItemProvider", "useAccordionItemContext", "AccordionItem", "accordionItemProps", "accordionContext", "valueContext", "collapsibleScope", "triggerId", "CollapsiblePrimitive.Root", "open2", "HEADER_NAME", "Accordi<PERSON><PERSON><PERSON><PERSON>", "headerProps", "itemContext", "h3", "AccordionTrigger", "collapsibleContext", "ItemSlot", "CollapsiblePrimitive.Trigger", "Accordi<PERSON><PERSON><PERSON><PERSON>", "CollapsiblePrimitive.Content", "role", "<PERSON><PERSON>", "Header", "Trigger2", "Content2", "className", "AccordionPrimitive", "cn", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "ChevronDown", "BacklogCycle", "currentPage", "setCurrentPage", "useState", "t", "useTranslation", "useProjectSounds", "playButtonClick", "playButtonHover", "useNavigationSounds", "backlogItemsWithIds", "useTranslationArray", "map", "index", "totalPages", "Math", "ceil", "startIdx", "endIdx", "paginatedItems", "slice", "section", "motion", "initial", "opacity", "y", "animate", "transition", "duration", "ease", "h1", "p", "delay", "_jsxs", "jsxs", "variants", "visible", "stagger<PERSON><PERSON><PERSON><PERSON>", "whileInView", "viewport", "once", "margin", "CheckCircle2", "challenge", "Lightbulb", "h4", "solution", "TrendingUp", "result", "Target", "note", "CTAButton", "max", "variant", "size", "icon", "ChevronLeft", "iconPosition", "Array", "from", "_", "i", "page", "onMouseEnter", "aria-label", "min", "ChevronRight"], "mappings": "ifAaA,IAAIA,EAAmB,eAClBC,EAA0BC,GAA0BC,EAAmBH,IACvEI,EAAqBC,GAAyBJ,EAAyBD,GACxEM,EAAcC,EAAgBC,YAChC,CAACC,EAAOC,KACA,MAAAC,mBACJA,EACAC,KAAMC,EAAAC,YACNA,EAAAC,SACAA,EAAAC,aACAA,KACGC,GACDR,GACGG,EAAMM,GAAWC,EAAqB,CAC3CC,KAAMP,EACNQ,YAAaP,IAAe,EAC5BQ,SAAUN,EACVO,OAAQvB,IAEV,OAA0BwB,EAAAC,IACxBrB,EACA,CACEsB,MAAOf,EACPI,WACAY,UAAWC,IACXhB,OACAiB,aAAcC,EAAAA,aAAkB,IAAMZ,GAASa,IAAcA,KAAW,CAACb,IACzEc,SAA6BR,EAAAC,IAC3BQ,EAAUC,IACV,CACE,aAAcC,EAASvB,GACvB,gBAAiBG,EAAW,QAAK,KAC9BE,EACHmB,IAAK1B,KAIZ,IAGLJ,EAAY+B,YAAcrC,EAC1B,IAAIsC,EAAe,qBACfC,EAAqBhC,EAAgBC,YACvC,CAACC,EAAOC,KACN,MAAMC,mBAAEA,KAAuB6B,GAAiB/B,EAC1CgC,EAAUpC,EAAsBiC,EAAc3B,GACpD,OAA0Ba,EAAAC,IACxBQ,EAAUS,OACV,CACEC,KAAM,SACN,gBAAiBF,EAAQd,UACzB,gBAAiBc,EAAQ7B,OAAQ,EACjC,aAAcuB,EAASM,EAAQ7B,MAC/B,gBAAiB6B,EAAQ1B,SAAW,QAAK,EACzCA,SAAU0B,EAAQ1B,YACfyB,EACHJ,IAAK1B,EACLkC,QAASC,EAAqBpC,EAAMmC,QAASH,EAAQZ,eAExD,IAGLU,EAAmBF,YAAcC,EACjC,IAAIQ,EAAe,qBACfC,EAAqBxC,EAAgBC,YACvC,CAACC,EAAOC,KACN,MAAMsC,WAAEA,KAAeC,GAAiBxC,EAClCgC,EAAUpC,EAAsByC,EAAcrC,EAAME,oBACnCc,OAAAA,EAAGA,IAACyB,EAAU,CAAEC,QAASH,GAAcP,EAAQ7B,KAAMoB,SAAU,EAAGmB,aAAiC3B,EAAAC,IAAC2B,EAAwB,IAAKH,EAAcb,IAAK1B,EAAcyC,aAAY,IAGzMJ,EAAmBV,YAAcS,EACjC,IAAIM,EAAyB7C,EAAgBC,YAAC,CAACC,EAAOC,KACpD,MAAMC,mBAAEA,EAAoBwC,QAAAA,EAAAnB,SAASA,KAAaiB,GAAiBxC,EAC7DgC,EAAUpC,EAAsByC,EAAcnC,IAC7C0C,EAAWC,GAAgBC,EAAAA,SAAeJ,GAC3Cf,EAAMoB,EAAYC,OAAC,MACnBC,EAAeC,EAAgBjD,EAAc0B,GAC7CwB,EAAYJ,EAAYC,OAAC,GACzBI,EAASD,EAAUE,QACnBC,EAAWP,EAAYC,OAAC,GACxBO,EAAQD,EAASD,QACjBG,EAASxB,EAAQ7B,MAAQyC,EACzBa,EAA+BV,EAAYC,OAACQ,GAC5CE,EAAoBX,EAAYC,YAAC,GAwBvC,OAvBAW,EAAAA,WAAgB,KACd,MAAMC,EAAMC,uBAAsB,IAAMJ,EAA6BJ,SAAU,IACxE,MAAA,IAAMS,qBAAqBF,EAAG,GACpC,IACHG,GAAgB,KACd,MAAMC,EAAOrC,EAAI0B,QACjB,GAAIW,EAAM,CACUN,EAAAL,QAAUK,EAAkBL,SAAW,CACvDY,mBAAoBD,EAAKE,MAAMD,mBAC/BE,cAAeH,EAAKE,MAAMC,eAE5BH,EAAKE,MAAMD,mBAAqB,KAChCD,EAAKE,MAAMC,cAAgB,OACrB,MAAAC,EAAOJ,EAAKK,wBAClBlB,EAAUE,QAAUe,EAAKhB,OACzBE,EAASD,QAAUe,EAAKb,MACnBE,EAA6BJ,UAC3BW,EAAAE,MAAMD,mBAAqBP,EAAkBL,QAAQY,mBACrDD,EAAAE,MAAMC,cAAgBT,EAAkBL,QAAQc,eAEvDtB,EAAaH,EACnB,IACK,CAACV,EAAQ7B,KAAMuC,IACQ3B,EAAAC,IACxBQ,EAAUC,IACV,CACE,aAAcC,EAASM,EAAQ7B,MAC/B,gBAAiB6B,EAAQ1B,SAAW,QAAK,EACzCgE,GAAItC,EAAQd,UACZqD,QAASf,KACNhB,EACHb,IAAKsB,EACLiB,MAAO,CACL,qCAAwCd,EAAS,GAAGA,WAAa,EACjE,oCAAuCG,EAAQ,GAAGA,WAAY,KAC3DvD,EAAMkE,OAEX3C,SAAUiC,GAAUjC,GAEvB,IAEH,SAASG,EAASvB,GAChB,OAAOA,EAAO,OAAS,QACzB,CACA,IAAIqE,EAAO3E,EACP4E,EAAU3C,EACV4C,EAAUpC,EC7IVqC,EAAmBC,EAAMC,mBAAc,GCY3C,IAAIC,EAAiB,YACjBC,EAAiB,CAAC,OAAQ,MAAO,YAAa,UAAW,YAAa,eACrEC,EAAYC,EAAeC,GAAyBC,EAAiBL,IACrEM,EAAwBC,GAAwB3F,EAAmBoF,EAAgB,CACtFI,EACAzF,IAEE6F,EAAsB7F,IACtB8F,EAAYC,EAAMzF,YACpB,CAACC,EAAOC,KACN,MAAMiC,KAAEA,KAASuD,GAAmBzF,EAC9B0F,EAAcD,EACdE,EAAgBF,EACtB,aAA2BT,EAAWY,SAAU,CAAE3E,MAAOjB,EAAM6F,iBAAkBtE,SAAmB,aAATW,EAAsClB,EAAGA,IAAC8E,GAAuB,IAAKH,EAAehE,IAAK1B,MAAkCe,IAAI+E,GAAqB,IAAKL,EAAa/D,IAAK1B,KAAiB,IAG5RsF,EAAU3D,YAAckD,EACxB,IAAKkB,EAAwBC,GAA4Bb,EAAuBN,IAC3EoB,EAA8BC,IAAkCf,EACnEN,EACA,CAAEsB,aAAa,IAEbL,GAAsBP,EAAMzF,YAC9B,CAACC,EAAOC,KACA,MACJoG,MAAOC,EAAAC,aACPA,EAAAC,cACAA,EAAgB,OACfJ,YACDA,GAAc,KACXK,GACDzG,GACGqG,EAAOK,GAAYhG,EAAqB,CAC7CC,KAAM2F,EACN1F,YAAa2F,GAAgB,GAC7B1F,SAAU2F,EACV1F,OAAQgE,IAEV,OAA0B/D,EAAAC,IACxBgF,EACA,CACE/E,MAAOjB,EAAM6F,iBACbQ,MAAOb,EAAMmB,SAAQ,IAAMN,EAAQ,CAACA,GAAS,IAAI,CAACA,IAClDO,WAAYF,EACZG,YAAarB,EAAMsB,aAAY,IAAMV,GAAeM,EAAS,KAAK,CAACN,EAAaM,IAChFnF,WAA6BP,IAACkF,EAA8B,CAAEjF,MAAOjB,EAAM6F,iBAAkBO,cAAa7E,WAA6BP,IAAC+F,GAAe,IAAKN,EAAsB9E,IAAK1B,OAE1L,IAGD6F,GAAwBN,EAAMzF,YAAW,CAACC,EAAOC,KAC7C,MACJoG,MAAOC,EAAAC,aACPA,EAAAC,cACAA,EAAgB,UAEbQ,GACDhH,GACGqG,EAAOK,GAAYhG,EAAqB,CAC7CC,KAAM2F,EACN1F,YAAa2F,GAAgB,GAC7B1F,SAAU2F,EACV1F,OAAQgE,IAEJmC,EAAiBzB,EAAMsB,aAC1BI,GAAcR,GAAS,CAACS,EAAY,KAAO,IAAIA,EAAWD,MAC3D,CAACR,IAEGU,EAAkB5B,EAAMsB,aAC3BI,GAAcR,GAAS,CAACS,EAAY,KAAOA,EAAUE,QAAQC,GAAWA,IAAWJ,OACpF,CAACR,IAEH,OAA0B3F,EAAAC,IACxBgF,EACA,CACE/E,MAAOjB,EAAM6F,iBACbQ,QACAO,WAAYK,EACZJ,YAAaO,EACb7F,WAA6BP,IAACkF,EAA8B,CAAEjF,MAAOjB,EAAM6F,iBAAkBO,aAAa,EAAM7E,SAA0BP,EAAAA,IAAI+F,GAAe,IAAKC,EAAwBrF,IAAK1B,OAElM,KAEEsH,GAAuBC,IAAuBpC,EAAuBN,GACtEiC,GAAgBvB,EAAMzF,YACxB,CAACC,EAAOC,KACA,MAAA4F,iBAAEA,WAAkBvF,EAAUmH,IAAAA,EAAAC,YAAKA,EAAc,cAAejC,GAAmBzF,EACnF2H,EAAenC,EAAMxC,OAAO,MAC5BC,EAAeC,EAAgByE,EAAc1H,GAC7C2H,EAAW3C,EAAcY,GAEzBgC,EAA+B,QDlGzC,SAAsBC,GACd,MAAAC,EAAYC,EAAMC,WAAWtD,GACnC,OAAOmD,GAAYC,GAAa,KAClC,CC8FsBG,CAAaT,GAEzBU,EAAgB/F,EAAqBpC,EAAMoI,WAAYC,UAC3D,IAAKtD,EAAeuD,SAASD,EAAME,KAAM,OACzC,MAAMC,EAASH,EAAMG,OACfC,EAAoBb,IAAWP,QAAQqB,UAAS,QAAC,OAAAC,EAAAD,EAAK/G,IAAI0B,gBAATsF,EAAkBrI,SAAA,IACnEsI,EAAeH,EAAkBI,WAAWH,GAASA,EAAK/G,IAAI0B,UAAYmF,IAC1EM,EAAeL,EAAkBM,OACvC,IAAyB,IAArBH,EAAqB,OACzBP,EAAMW,iBACN,IAAIC,EAAYL,EAChB,MACMM,EAAWJ,EAAe,EAC1BK,EAAW,KACfF,EAAYL,EAAe,EACvBK,EAAYC,IACFD,EALE,EAMxB,EAEYG,EAAW,KACfH,EAAYL,EAAe,EACvBK,EAVY,IAWFA,EAAAC,EACtB,EAEM,OAAQb,EAAME,KACZ,IAAK,OACSU,EAhBE,EAiBd,MACF,IAAK,MACSA,EAAAC,EACZ,MACF,IAAK,aACiB,eAAhBxB,IACEG,EACQsB,IAEAC,KAGd,MACF,IAAK,YACiB,aAAhB1B,GACQyB,IAEZ,MACF,IAAK,YACiB,eAAhBzB,IACEG,EACQuB,IAEAD,KAGd,MACF,IAAK,UACiB,aAAhBzB,GACQ0B,IAKhB,OAAAT,EAAAF,EADqBQ,EAAYH,GACDnH,IAAI0B,UAASsF,EAAAU,OAAA,IAE/C,OAA0BtI,EAAAC,IACxBuG,GACA,CACEtG,MAAO4E,EACPvF,WACAgJ,UAAW7B,EACXC,cACAnG,WAA6BP,IAACgE,EAAWuE,KAAM,CAAEtI,MAAO4E,EAAkBtE,SAA6BR,EAAAC,IACrGQ,EAAUC,IACV,IACKgE,EACH,mBAAoBiC,EACpB/F,IAAKsB,EACLmF,UAAW9H,OAAW,EAAS6H,OAItC,IAGDqB,GAAY,iBACXC,GAAuBC,IAA2BtE,EAAuBoE,IAC1EG,GAAgBnE,EAAMzF,YACxB,CAACC,EAAOC,KACN,MAAM4F,iBAAEA,EAAAQ,MAAkBA,KAAUuD,GAAuB5J,EACrD6J,EAAmBrC,GAAoBgC,GAAW3D,GAClDiE,EAAe7D,EAAyBuD,GAAW3D,GACnDkE,EAAmBzE,EAAoBO,GACvCmE,EAAY7I,IACZhB,EAAOkG,GAASyD,EAAazD,MAAMiC,SAASjC,KAAU,EACtD/F,EAAWuJ,EAAiBvJ,UAAYN,EAAMM,SACpD,OAA0BS,EAAAC,IACxByI,GACA,CACExI,MAAO4E,EACP1F,OACAG,WACA0J,YACAzI,SAA6BR,EAAAC,IAC3BiJ,EACA,CACE,mBAAoBJ,EAAiBnC,YACrC,aAAchG,GAASvB,MACpB4J,KACAH,EACHjI,IAAK1B,EACLK,WACAH,OACAI,aAAe2J,IACTA,EACFJ,EAAalD,WAAWP,GAExByD,EAAajD,YAAYR,EACzC,KAKK,IAGLsD,GAAc/H,YAAc4H,GAC5B,IAAIW,GAAc,kBACdC,GAAkB5E,EAAMzF,YAC1B,CAACC,EAAOC,KACN,MAAM4F,iBAAEA,KAAqBwE,GAAgBrK,EACvC6J,EAAmBrC,GAAoB1C,EAAgBe,GACvDyE,EAAcZ,GAAwBS,GAAatE,GACzD,OAA0B9E,EAAAC,IACxBQ,EAAU+I,GACV,CACE,mBAAoBV,EAAiBnC,YACrC,aAAchG,GAAS4I,EAAYnK,MACnC,gBAAiBmK,EAAYhK,SAAW,QAAK,KAC1C+J,EACH1I,IAAK1B,GAER,IAGLmK,GAAgBxI,YAAcuI,GAC9B,IAAItI,GAAe,mBACf2I,GAAmBhF,EAAMzF,YAC3B,CAACC,EAAOC,KACN,MAAM4F,iBAAEA,KAAqB9D,GAAiB/B,EACxC6J,EAAmBrC,GAAoB1C,EAAgBe,GACvDyE,EAAcZ,GAAwB7H,GAAcgE,GACpD4E,EAAqBtE,GAA+BtE,GAAcgE,GAClEkE,EAAmBzE,EAAoBO,GACtB7E,OAAAA,EAAAA,IAAIgE,EAAW0F,SAAU,CAAEzJ,MAAO4E,EAAkBtE,SAA6BR,EAAAC,IACtG2J,EACA,CACE,gBAAiBL,EAAYnK,OAASsK,EAAmBrE,kBAAe,EACxE,mBAAoByD,EAAiBnC,YACrCpD,GAAIgG,EAAYN,aACbD,KACAhI,EACHJ,IAAK1B,KAEN,IAGPuK,GAAiB5I,YAAcC,GAC/B,IAAIQ,GAAe,mBACfuI,GAAmBpF,EAAMzF,YAC3B,CAACC,EAAOC,KACN,MAAM4F,iBAAEA,KAAqBrD,GAAiBxC,EACxC6J,EAAmBrC,GAAoB1C,EAAgBe,GACvDyE,EAAcZ,GAAwBrH,GAAcwD,GACpDkE,EAAmBzE,EAAoBO,GAC7C,OAA0B9E,EAAAC,IACxB6J,EACA,CACEC,KAAM,SACN,kBAAmBR,EAAYN,UAC/B,mBAAoBH,EAAiBnC,eAClCqC,KACAvH,EACHb,IAAK1B,EACLiE,MAAO,CACL,mCAAsC,0CACtC,kCAAqC,4CAClClE,EAAMkE,QAGd,IAIL,SAASxC,GAASvB,GAChB,OAAOA,EAAO,OAAS,QACzB,CAHAyK,GAAiBhJ,YAAcS,GAI/B,IACI0I,GAAOpB,GACPqB,GAASZ,GACTa,GAAWT,GACXU,GAAWN,GC3Sf,MAAMrF,GDuSMA,ECrSNoE,GAAgBnE,EAAAA,YAGpB,EAAG2F,eAAcnL,GAAS2B,IACzByJ,EAAAA,IAAAA,GAAuB,CACtBzJ,MACAwJ,UAAWE,EAAG,WAAYF,MACtBnL,MAGR2J,GAAc/H,YAAc,gBAE5B,MAAM4I,GAAmBhF,EAAAA,YAGvB,EAAG2F,YAAW5J,cAAavB,GAAS2B,IACpC2J,EAAAtK,IAACoK,GAAyB,CAACG,SAAO,EAChChK,eAACE,MAAAA,CAAI0J,UAAU,gBACZC,EAAAA,KAAAA,GAA0B,CACzBzJ,MACAwJ,UAAWE,EACT,+HACAF,MAEEnL,YAEHuB,QACAiK,EAAAA,CAAYL,UAAU,gEAK/BX,GAAiB5I,YAAcwJ,GAA2BxJ,YAE1D,MAAMgJ,GAAmBpF,EAAAA,YAGvB,EAAG2F,YAAW5J,cAAavB,GAAS2B,IACpC2J,EAAAtK,IAACoK,GAA0B,CACzBzJ,MACAwJ,UAAU,8HACNnL,EAEJuB,eAACE,MAAAA,CAAI0J,UAAWE,EAAG,YAAaF,GAAa5J,iBAIjDqJ,GAAiBhJ,YAAcwJ,GAA2BxJ,YCjC1D,MAEM6J,GAAyB,KAC7B,MAAOC,EAAaC,GAAkBC,EAAAA,SAAS,IACzCC,EAAEA,GAAMC,IACuBC,IACrC,MAAMC,gBAAEA,EAAAA,gBAAiBC,GAAoBC,IAWvCC,EAReC,EAAoB,gBAAiBP,GAQjBQ,KAAI,CAAC3D,EAAM4D,KAAW,IAC1D5D,EACHpE,GAAI,WAAWgI,EAAQ,QAGnBC,EAAaC,KAAKC,KAAKN,EAAoBpD,OAtB5B,GAuBf2D,EAvBe,GAuBHhB,EAAc,GAC1BiB,EAASD,EAxBM,EAyBfE,EAAiBT,EAAoBU,MAAMH,EAAUC,iBAexDG,UAAAA,CAAQ3B,UAAU,mBAEjBG,EAAAtK,IAAC+L,EAAOtL,IAAG,CACTuL,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKC,KAAM,WACnCnC,UAAU,QAEV5J,gBAACE,MAAAA,CAAI0J,UAAU,mEACZoC,KAAAA,CAAGpC,UAAU,oFACXU,EAAE,yBAEJ2B,IAAAA,CAAErC,UAAU,kDACVU,EAAE,yBAGLP,EAAAtK,IAAC+L,EAAOtL,IAAG,CACTuL,QAAS,CAAEzJ,MAAO,GAClB4J,QAAS,CAAE5J,MAAO,SAClB6J,WAAY,CAAEC,SAAU,GAAKI,MAAO,IACpCtC,UAAU,uEAMhBuC,EAAAC,KAACZ,EAAOtL,IAAG,CACT0J,UAAU,gDACVyC,SAvCoB,CACxBrJ,OAAQ,CAAE0I,QAAS,GACnBY,QAAS,CACPZ,QAAS,EACTG,WAAY,CACVU,gBAAiB,MAmCjBd,QAAQ,SACRe,YAAY,UACZC,SAAU,CAAEC,MAAM,EAAMC,OAAQ,0BAE/B3I,GAAAA,CAAUrD,KAAK,SAASkE,aAAW,EAAC+E,UAAU,4BAClB,IAA1ByB,EAAe7D,aACbtH,MAAAA,CAAI0J,UAAU,uDACZU,EAAE,qBAGLe,EAAeP,KAAI,CAAC3D,EAAM4D,UACvB3C,GAAAA,CAActD,MAAOqC,EAAKpE,GAAkB6G,UAAU,uBACpD4B,EAAAA,KAAAA,EAAOtL,IAAG,CACTuL,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKI,MAAe,GAARnB,GACpCnB,UAAU,gLAETX,GAAAA,CAAiBW,UAAU,+DAC1B5J,gBAACE,MAAAA,CAAI0J,UAAU,8DAEZ1J,MAAAA,CAAI0J,UAAU,+GACb5J,eAAC4M,EAAAA,CAAahD,UAAU,gDAIzB1J,MAAAA,CAAI0J,UAAU,qCACb5J,eAACiM,IAAAA,CAAErC,UAAU,qGACVzC,SAAAA,EAAK0F,oBAKT3M,MAAAA,CAAI0J,UAAU,0EAKlBP,GAAAA,CACCrJ,eAACE,MAAAA,CAAI0J,UAAU,YAEb5J,gBAACE,MAAAA,CAAI0J,UAAU,kDAGbuC,EAAAC,KAACZ,EAAOtL,IAAG,CACTuL,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKI,MAAO,IACpCtC,UAAU,uGAET1J,MAAAA,CAAI0J,UAAU,+CACZ1J,MAAAA,CAAI0J,UAAU,mFACb5J,eAAC8M,EAAAA,CAAUlD,UAAU,gDAEtBmD,KAAAA,CAAGnD,UAAU,kDACXU,EAAE,+BAGN2B,IAAAA,CAAErC,UAAU,oDACVzC,SAAAA,EAAK6F,cAKVb,EAAAC,KAACZ,EAAOtL,IAAG,CACTuL,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKI,MAAO,IACpCtC,UAAU,2GAET1J,MAAAA,CAAI0J,UAAU,+CACZ1J,MAAAA,CAAI0J,UAAU,qFACb5J,eAACiN,EAAAA,CAAWrD,UAAU,kDAEvBmD,KAAAA,CAAGnD,UAAU,kDACXU,EAAE,6BAGN2B,IAAAA,CAAErC,UAAU,oDACVzC,SAAAA,EAAK+F,YAKVf,EAAAC,KAACZ,EAAOtL,IAAG,CACTuL,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKI,MAAO,IACpCtC,UAAU,kGAET1J,MAAAA,CAAI0J,UAAU,+CACZ1J,MAAAA,CAAI0J,UAAU,0FACb5J,eAACmN,EAAAA,CAAOvD,UAAU,yDAEnBmD,KAAAA,CAAGnD,UAAU,kDACXU,EAAE,2BAGN2B,IAAAA,CAAErC,UAAU,oDACVzC,SAAAA,EAAKiG,qBAxFgBjG,EAAKpE,QAsG9CiI,EAAa,UACXQ,EAAOtL,IAAG,CACTuL,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKI,MAAO,IACpCtC,UAAU,+DAETyD,EAAAA,CACCzM,QAAS,IAAMwJ,GAAgB6B,GAAMhB,KAAKqC,IAAI,EAAGrB,EAAI,KACrDlN,SAA0B,IAAhBoL,EACVoD,QAAQ,QACRC,KAAK,KACLC,KAAMC,EACNC,aAAa,OACb/D,UAAU,2DAETU,EAAE,4BAGJpK,MAAAA,CAAI0J,UAAU,0BACZgE,SAAAA,MAAMC,KAAK,CAAErG,OAAQwD,IAAc,CAAC8C,EAAGC,IAAMA,EAAI,IAAGjD,KAAKkD,GACxDxO,EAAAC,IAACiB,SAAAA,CAECE,QAAS,KACPwJ,EAAe4D,GACfvD,GAAAA,EAEFwD,aAAc,IAAMvD,IACpBd,UAAW,iEACTO,IAAgB6D,EACZ,iDACA,kGAENE,aAAY,GAAG5D,EAAE,mBAAmB0D,IAEnCA,SAAAA,GAbIA,aAkBVX,EAAAA,CACCzM,QAAS,IAAMwJ,GAAgB6B,GAAMhB,KAAKkD,IAAInD,EAAYiB,EAAI,KAC9DlN,SAAUoL,IAAgBa,EAC1BuC,QAAQ,QACRC,KAAK,KACLC,KAAMW,EACNT,aAAa,QACb/D,UAAU,2DAETU,EAAE", "x_google_ignoreList": [0, 1, 2]}