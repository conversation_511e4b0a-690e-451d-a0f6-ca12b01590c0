/**
 * 🎯 COMPONENTES MODULARES
 *
 * CSS modular para todos os componentes
 * Organizado por categoria e funcionalidade
 */

/* ===== IMPORTAÇÕES DE COMPONENTES ===== */
@import './cards.css';
@import './forms.css';
@import './navigation.css';
@import './layout.css';
@import './feedback.css';
@import './profile.css';
@import './projects.css';
@import './backlog.css';
@import './contact.css';
@import './accessibility.css';
@import '../fluid-gradients.css';

/* ===== CLASSES BASE DE COMPONENTES ===== */

/* Container principal */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  contain: layout style;
}

/* Section wrapper */
.section {
  padding: var(--space-3xl) 0;
  position: relative;
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-xl);
  text-align: left;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--border-radius-full);
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-2xl);
  max-width: 48rem;
}

/* Grid layouts */
.grid {
  display: grid;
  gap: var(--space-lg);
}

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Flex layouts */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* Spacing utilities */
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }
.gap-2xl { gap: var(--space-2xl); }

/* Text utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Color utilities */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-light { color: var(--color-text-light); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-surface { background-color: var(--color-surface); }

/* Border utilities */
.border { border: var(--border-width) solid var(--color-border); }
.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-secondary); }

.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-full { border-radius: var(--border-radius-full); }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Transition utilities */
.transition { transition: var(--transition); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.3);
}

/* Focus states */
.focus-ring:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.focus-ring-inset:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
  cursor: not-allowed;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Disabled states */
.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }

  .section {
    padding: var(--space-2xl) 0;
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .grid-auto-fit,
  .grid-auto-fill {
    grid-template-columns: 1fr;
  }
}
