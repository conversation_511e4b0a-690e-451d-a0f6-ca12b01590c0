import{j as e,m as a}from"./chunk-L3uak9dD.js";import{r,b as t}from"./chunk-D3Ns84uO.js";import{c as o,b as s,l as i,P as n,e as l,d as c,u as d,h as p}from"./chunk-Bt4ub4Kz.js";import{b as m,c as u}from"./index-Dn5J_98a.js";import{k as x,l as h,m as f,T as b,n as v,o as g,p as j}from"./chunk-Bc9oLI49.js";import{u as y,a as N,C as w}from"./chunk-CMTALxe4.js";import{u as k}from"./chunk-BQYJeaTK.js";import{u as C}from"./chunk-CS50z45r.js";import"./chunk-DJqUFuPP.js";import"./chunk-DylKGAq6.js";var _="Collapsible",[A,R]=o(_),[I,D]=A(_),O=r.forwardRef(((a,t)=>{const{__scopeCollapsible:o,open:l,defaultOpen:c,disabled:d,onOpenChange:p,...m}=a,[u,x]=s({prop:l,defaultProp:c??!1,onChange:p,caller:_});return e.jsx(I,{scope:o,disabled:d,contentId:i(),open:u,onOpenToggle:r.useCallback((()=>x((e=>!e))),[x]),children:e.jsx(n.div,{"data-state":z(u),"data-disabled":d?"":void 0,...m,ref:t})})}));O.displayName=_;var P="CollapsibleTrigger",M=r.forwardRef(((a,r)=>{const{__scopeCollapsible:t,...o}=a,s=D(P,t);return e.jsx(n.button,{type:"button","aria-controls":s.contentId,"aria-expanded":s.open||!1,"data-state":z(s.open),"data-disabled":s.disabled?"":void 0,disabled:s.disabled,...o,ref:r,onClick:l(a.onClick,s.onOpenToggle)})}));M.displayName=P;var T="CollapsibleContent",V=r.forwardRef(((a,r)=>{const{forceMount:t,...o}=a,s=D(T,a.__scopeCollapsible);return e.jsx(c,{present:t||s.open,children:({present:a})=>e.jsx($,{...o,ref:r,present:a})})}));V.displayName=T;var $=r.forwardRef(((a,t)=>{const{__scopeCollapsible:o,present:s,children:i,...l}=a,c=D(T,o),[m,u]=r.useState(s),x=r.useRef(null),h=d(t,x),f=r.useRef(0),b=f.current,v=r.useRef(0),g=v.current,j=c.open||m,y=r.useRef(j),N=r.useRef(void 0);return r.useEffect((()=>{const e=requestAnimationFrame((()=>y.current=!1));return()=>cancelAnimationFrame(e)}),[]),p((()=>{const e=x.current;if(e){N.current=N.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";const a=e.getBoundingClientRect();f.current=a.height,v.current=a.width,y.current||(e.style.transitionDuration=N.current.transitionDuration,e.style.animationName=N.current.animationName),u(s)}}),[c.open,s]),e.jsx(n.div,{"data-state":z(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!j,...l,ref:h,style:{"--radix-collapsible-content-height":b?`${b}px`:void 0,"--radix-collapsible-content-width":g?`${g}px`:void 0,...a.style},children:j&&i})}));function z(e){return e?"open":"closed"}var E=O,H=M,S=V,B=r.createContext(void 0);var q="Accordion",F=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[K,L,U]=m(q),[G,J]=o(q,[U,R]),Q=R(),W=t.forwardRef(((a,r)=>{const{type:t,...o}=a,s=o,i=o;return e.jsx(K.Provider,{scope:a.__scopeAccordion,children:"multiple"===t?e.jsx(re,{...i,ref:r}):e.jsx(ae,{...s,ref:r})})}));W.displayName=q;var[X,Y]=G(q),[Z,ee]=G(q,{collapsible:!1}),ae=t.forwardRef(((a,r)=>{const{value:o,defaultValue:i,onValueChange:n=()=>{},collapsible:l=!1,...c}=a,[d,p]=s({prop:o,defaultProp:i??"",onChange:n,caller:q});return e.jsx(X,{scope:a.__scopeAccordion,value:t.useMemo((()=>d?[d]:[]),[d]),onItemOpen:p,onItemClose:t.useCallback((()=>l&&p("")),[l,p]),children:e.jsx(Z,{scope:a.__scopeAccordion,collapsible:l,children:e.jsx(se,{...c,ref:r})})})})),re=t.forwardRef(((a,r)=>{const{value:o,defaultValue:i,onValueChange:n=()=>{},...l}=a,[c,d]=s({prop:o,defaultProp:i??[],onChange:n,caller:q}),p=t.useCallback((e=>d(((a=[])=>[...a,e]))),[d]),m=t.useCallback((e=>d(((a=[])=>a.filter((a=>a!==e))))),[d]);return e.jsx(X,{scope:a.__scopeAccordion,value:c,onItemOpen:p,onItemClose:m,children:e.jsx(Z,{scope:a.__scopeAccordion,collapsible:!0,children:e.jsx(se,{...l,ref:r})})})})),[te,oe]=G(q),se=t.forwardRef(((a,o)=>{const{__scopeAccordion:s,disabled:i,dir:c,orientation:p="vertical",...m}=a,u=t.useRef(null),x=d(u,o),h=L(s),f="ltr"===function(e){const a=r.useContext(B);return e||a||"ltr"}(c),b=l(a.onKeyDown,(e=>{var a;if(!F.includes(e.key))return;const r=e.target,t=h().filter((e=>{var a;return!(null==(a=e.ref.current)?void 0:a.disabled)})),o=t.findIndex((e=>e.ref.current===r)),s=t.length;if(-1===o)return;e.preventDefault();let i=o;const n=s-1,l=()=>{i=o+1,i>n&&(i=0)},c=()=>{i=o-1,i<0&&(i=n)};switch(e.key){case"Home":i=0;break;case"End":i=n;break;case"ArrowRight":"horizontal"===p&&(f?l():c());break;case"ArrowDown":"vertical"===p&&l();break;case"ArrowLeft":"horizontal"===p&&(f?c():l());break;case"ArrowUp":"vertical"===p&&c()}null==(a=t[i%s].ref.current)||a.focus()}));return e.jsx(te,{scope:s,disabled:i,direction:c,orientation:p,children:e.jsx(K.Slot,{scope:s,children:e.jsx(n.div,{...m,"data-orientation":p,ref:x,onKeyDown:i?void 0:b})})})})),ie="AccordionItem",[ne,le]=G(ie),ce=t.forwardRef(((a,r)=>{const{__scopeAccordion:t,value:o,...s}=a,n=oe(ie,t),l=Y(ie,t),c=Q(t),d=i(),p=o&&l.value.includes(o)||!1,m=n.disabled||a.disabled;return e.jsx(ne,{scope:t,open:p,disabled:m,triggerId:d,children:e.jsx(E,{"data-orientation":n.orientation,"data-state":fe(p),...c,...s,ref:r,disabled:m,open:p,onOpenChange:e=>{e?l.onItemOpen(o):l.onItemClose(o)}})})}));ce.displayName=ie;var de="AccordionHeader",pe=t.forwardRef(((a,r)=>{const{__scopeAccordion:t,...o}=a,s=oe(q,t),i=le(de,t);return e.jsx(n.h3,{"data-orientation":s.orientation,"data-state":fe(i.open),"data-disabled":i.disabled?"":void 0,...o,ref:r})}));pe.displayName=de;var me="AccordionTrigger",ue=t.forwardRef(((a,r)=>{const{__scopeAccordion:t,...o}=a,s=oe(q,t),i=le(me,t),n=ee(me,t),l=Q(t);return e.jsx(K.ItemSlot,{scope:t,children:e.jsx(H,{"aria-disabled":i.open&&!n.collapsible||void 0,"data-orientation":s.orientation,id:i.triggerId,...l,...o,ref:r})})}));ue.displayName=me;var xe="AccordionContent",he=t.forwardRef(((a,r)=>{const{__scopeAccordion:t,...o}=a,s=oe(q,t),i=le(xe,t),n=Q(t);return e.jsx(S,{role:"region","aria-labelledby":i.triggerId,"data-orientation":s.orientation,...n,...o,ref:r,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...a.style}})}));function fe(e){return e?"open":"closed"}he.displayName=xe;var be=ce,ve=pe,ge=ue,je=he;const ye=W,Ne=r.forwardRef((({className:a,...r},t)=>e.jsx(be,{ref:t,className:u("border-b",a),...r})));Ne.displayName="AccordionItem";const we=r.forwardRef((({className:a,children:r,...t},o)=>e.jsx(ve,{asChild:!0,children:e.jsx("div",{className:"flex",children:e.jsxs(ge,{ref:o,className:u("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",a),...t,children:[r,e.jsx(x,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})})));we.displayName=ge.displayName;const ke=r.forwardRef((({className:a,children:r,...t},o)=>e.jsx(je,{ref:o,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...t,children:e.jsx("div",{className:u("pb-4 pt-0",a),children:r})})));ke.displayName=je.displayName;const Ce=()=>{const[t,o]=r.useState(1),{t:s}=C();y();const{playButtonClick:i,playButtonHover:n}=N(),l=k("backlog.items",s).map(((e,a)=>({...e,id:`backlog-${a+1}`}))),c=Math.ceil(l.length/4),d=4*(t-1),p=d+4,m=l.slice(d,p);return e.jsxs("section",{className:"w-full",children:[e.jsx(a.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},className:"mb-16",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-left px-4 sm:px-6 lg:px-8",children:[e.jsx("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-text)] mb-4",children:s("backlog.title")}),e.jsx("p",{className:"text-[var(--color-muted)] text-lg mb-4",children:s("backlog.description")}),e.jsx(a.div,{initial:{width:0},animate:{width:"120px"},transition:{duration:.8,delay:.6},className:"h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"})]})}),e.jsxs(a.div,{className:"w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},children:[e.jsx(ye,{type:"single",collapsible:!0,className:"w-full space-y-4",children:0===m.length?e.jsx("div",{className:"text-center py-12 text-[var(--color-muted)]",children:s("backlog.noItems")}):m.map(((r,t)=>e.jsx(Ne,{value:r.id,className:"border-none",children:e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},className:"bg-white dark:bg-[var(--color-surface)] rounded-2xl border border-[var(--color-border)] shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden",children:[e.jsx(we,{className:"px-6 py-6 hover:no-underline group [&[data-state=open]]:pb-4",children:e.jsxs("div",{className:"flex items-center gap-4 w-full min-h-[60px]",children:[e.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-[var(--color-primary)]/10 rounded-xl flex items-center justify-center self-center",children:e.jsx(h,{className:"w-6 h-6 text-[var(--color-primary)]"})}),e.jsx("div",{className:"flex-1 text-left flex items-center",children:e.jsx("p",{className:"backlog-challenge-text group-hover:text-[var(--color-primary)] transition-colors duration-200 pr-4",children:r.challenge})}),e.jsx("div",{className:"flex-shrink-0 flex items-center justify-center self-center"})]})}),e.jsx(ke,{children:e.jsx("div",{className:"px-6 pb-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},className:"bg-[var(--color-primary)]/5 rounded-xl p-4 border-l-4 border-[var(--color-primary)]",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-8 h-8 bg-[var(--color-primary)]/10 rounded-lg flex items-center justify-center",children:e.jsx(f,{className:"w-4 h-4 text-[var(--color-primary)]"})}),e.jsx("h4",{className:"font-semibold text-[var(--color-text)]",children:s("backlog.solution")})]}),e.jsx("p",{className:"text-sm text-[var(--color-muted)] leading-relaxed",children:r.solution})]}),e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.2},className:"bg-[var(--color-secondary)]/5 rounded-xl p-4 border-l-4 border-[var(--color-secondary)]",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-8 h-8 bg-[var(--color-secondary)]/10 rounded-lg flex items-center justify-center",children:e.jsx(b,{className:"w-4 h-4 text-[var(--color-secondary)]"})}),e.jsx("h4",{className:"font-semibold text-[var(--color-text)]",children:s("backlog.result")})]}),e.jsx("p",{className:"text-sm text-[var(--color-muted)] leading-relaxed",children:r.result})]}),e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.3},className:"bg-purple-50 dark:bg-purple-900/10 rounded-xl p-4 border-l-4 border-purple-500",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center",children:e.jsx(v,{className:"w-4 h-4 text-purple-600 dark:text-purple-400"})}),e.jsx("h4",{className:"font-semibold text-[var(--color-text)]",children:s("backlog.note")})]}),e.jsx("p",{className:"text-sm text-[var(--color-muted)] leading-relaxed",children:r.note})]})]})})})]})},r.id)))}),c>1&&e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},className:"flex justify-center items-center gap-4 mt-12",children:[e.jsx(w,{onClick:()=>o((e=>Math.max(1,e-1))),disabled:1===t,variant:"ghost",size:"md",icon:g,iconPosition:"left",className:"disabled:opacity-50 disabled:cursor-not-allowed",children:s("backlog.previous")}),e.jsx("div",{className:"flex items-center gap-2",children:Array.from({length:c},((e,a)=>a+1)).map((a=>e.jsx("button",{onClick:()=>{o(a),i()},onMouseEnter:()=>n(),className:"w-10 h-10 rounded-lg font-medium transition-all duration-200 "+(t===a?"bg-[var(--color-primary)] text-white shadow-md":"text-[var(--color-muted)] hover:bg-[var(--color-primary)]/10 hover:text-[var(--color-primary)]"),"aria-label":`${s("backlog.page")} ${a}`,children:a},a)))}),e.jsx(w,{onClick:()=>o((e=>Math.min(c,e+1))),disabled:t===c,variant:"ghost",size:"md",icon:j,iconPosition:"right",className:"disabled:opacity-50 disabled:cursor-not-allowed",children:s("backlog.next")})]})]})]})};export{Ce as default};
//# sourceMappingURL=BacklogCycle-BKliH9bh.js.map
