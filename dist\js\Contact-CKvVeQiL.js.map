{"version": 3, "file": "Contact-CKvVeQiL.js", "sources": ["../../node_modules/emailjs-com/es/store/store.js", "../../node_modules/emailjs-com/es/utils/validateParams.js", "../../node_modules/emailjs-com/es/models/EmailJSResponseStatus.js", "../../node_modules/emailjs-com/es/api/sendPost.js", "../../node_modules/emailjs-com/es/index.js", "../../node_modules/emailjs-com/es/methods/send/send.js", "../../src/components/Contact.tsx"], "sourcesContent": ["export const store = {\n    _origin: 'https://api.emailjs.com',\n};\n", "export const validateParams = (userID, serviceID, templateID) => {\n    if (!userID) {\n        throw 'The user ID is required. Visit https://dashboard.emailjs.com/admin/integration';\n    }\n    if (!serviceID) {\n        throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n    }\n    if (!templateID) {\n        throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n    }\n    return true;\n};\n", "export class EmailJSResponseStatus {\n    constructor(httpResponse) {\n        this.status = httpResponse.status;\n        this.text = httpResponse.responseText;\n    }\n}\n", "import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = (url, data, headers = {}) => {\n    return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest();\n        xhr.addEventListener('load', ({ target }) => {\n            const responseStatus = new EmailJSResponseStatus(target);\n            if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n                resolve(responseStatus);\n            }\n            else {\n                reject(responseStatus);\n            }\n        });\n        xhr.addEventListener('error', ({ target }) => {\n            reject(new EmailJSResponseStatus(target));\n        });\n        xhr.open('POST', store._origin + url, true);\n        Object.keys(headers).forEach((key) => {\n            xhr.setRequestHeader(key, headers[key]);\n        });\n        xhr.send(data);\n    });\n};\n", "import { init } from './methods/init/init';\nimport { send } from './methods/send/send';\nimport { sendForm } from './methods/sendForm/sendForm';\nexport { init, send, sendForm };\nexport default {\n    init,\n    send,\n    sendForm,\n};\n", "import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templatePrams - the template params, what will be set to the EmailJS template\n * @param {string} userID - the EmailJS user ID\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const send = (serviceID, templateID, templatePrams, userID) => {\n    const uID = userID || store._userID;\n    validateParams(uID, serviceID, templateID);\n    const params = {\n        lib_version: '3.2.0',\n        user_id: uID,\n        service_id: serviceID,\n        template_id: templateID,\n        template_params: templatePrams,\n    };\n    return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n        'Content-type': 'application/json',\n    });\n};\n", "import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Mail, User, MessageSquare, Send, CheckCircle, AlertCircle, Loader2, Check, X } from 'lucide-react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport emailjs from 'emailjs-com';\r\nimport { CTAButton } from '@/components/ui/buttons';\r\nimport { SecureValidation } from '@/utils/secureValidation';\r\nimport { useFormSounds } from '@/hooks/useSound';\r\n\r\n// Tipos para o formulário\r\ninterface FormData {\r\n  name: string;\r\n  email: string;\r\n  message: string;\r\n}\r\n\r\ninterface FormErrors {\r\n  name?: string;\r\n  email?: string;\r\n  message?: string;\r\n}\r\n\r\ninterface FormTouched {\r\n  name: boolean;\r\n  email: boolean;\r\n  message: boolean;\r\n}\r\n\r\nconst Contact: React.FC = () => {\r\n  const { t, i18n } = useTranslation();\r\n  const { playSubmitSuccess, playSubmitError, playFieldFocus, playFieldValid } = useFormSounds();\r\n\r\n  // Estados do formulário\r\n  const [formData, setFormData] = useState<FormData>({\r\n    name: '',\r\n    email: '',\r\n    message: ''\r\n  });\r\n\r\n  const [errors, setErrors] = useState<FormErrors>({});\r\n  const [touched, setTouched] = useState<FormTouched>({\r\n    name: false,\r\n    email: false,\r\n    message: false\r\n  });\r\n\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\r\n\r\n  // Refs para acessibilidade\r\n  const nameRef = useRef<HTMLInputElement>(null);\r\n  const emailRef = useRef<HTMLInputElement>(null);\r\n  const messageRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  // Auto-hide da mensagem de sucesso após 4 segundos\r\n  useEffect(() => {\r\n    if (submitStatus === 'success') {\r\n      const timer = setTimeout(() => {\r\n        setSubmitStatus('idle');\r\n      }, 4000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [submitStatus]);\r\n\r\n  // Validação de email segura usando utilitário seguro\r\n  const isValidEmail = (email: string): boolean => {\r\n    return SecureValidation.validateEmail(email);\r\n  };\r\n\r\n  // Validação dos campos - usando useCallback para evitar re-criação\r\n  const validateField = useCallback((name: keyof FormData, value: string): string | undefined => {\r\n    try {\r\n      const lang = i18n?.language || 'pt-BR';\r\n      const safeValue = value || '';\r\n\r\n      switch (name) {\r\n        case 'name':\r\n          if (!safeValue.trim()) {\r\n            if (lang === 'en-US') return 'Name is required';\r\n            if (lang === 'es-ES') return 'El nombre es obligatorio';\r\n            return 'Nome é obrigatório';\r\n          }\r\n          if (safeValue.trim().length < 2) {\r\n            if (lang === 'en-US') return 'Name must be at least 2 characters';\r\n            if (lang === 'es-ES') return 'El nombre debe tener al menos 2 caracteres';\r\n            return 'Nome deve ter pelo menos 2 caracteres';\r\n          }\r\n          return undefined;\r\n\r\n        case 'email':\r\n          if (!safeValue.trim()) {\r\n            if (lang === 'en-US') return 'Email is required';\r\n            if (lang === 'es-ES') return 'El email es obligatorio';\r\n            return 'E-mail é obrigatório';\r\n          }\r\n          if (!isValidEmail(safeValue)) {\r\n            if (lang === 'en-US') return 'Invalid email';\r\n            if (lang === 'es-ES') return 'Email inválido';\r\n            return 'E-mail inválido';\r\n          }\r\n          return undefined;\r\n\r\n        case 'message':\r\n          if (!safeValue.trim()) {\r\n            if (lang === 'en-US') return 'Message is required';\r\n            if (lang === 'es-ES') return 'El mensaje es obligatorio';\r\n            return 'Mensagem é obrigatória';\r\n          }\r\n          if (safeValue.trim().length < 10) {\r\n            if (lang === 'en-US') return 'Message must be at least 10 characters';\r\n            if (lang === 'es-ES') return 'El mensaje debe tener al menos 10 caracteres';\r\n            return 'Mensagem deve ter pelo menos 10 caracteres';\r\n          }\r\n          return undefined;\r\n\r\n        default:\r\n          return undefined;\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro na validação do campo:', error);\r\n      return 'Erro na validação';\r\n    }\r\n  }, [i18n?.language]);\r\n\r\n  // Validar todos os campos\r\n  const validateForm = (): boolean => {\r\n    const newErrors: FormErrors = {};\r\n\r\n    Object.keys(formData).forEach((key) => {\r\n      const fieldName = key as keyof FormData;\r\n      const error = validateField(fieldName, formData[fieldName]);\r\n      if (error) {\r\n        newErrors[fieldName] = error;\r\n      }\r\n    });\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  // Manipular mudanças nos campos - usando useCallback\r\n  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n    try {\r\n      const { name, value } = e.target;\r\n      const fieldName = name as keyof FormData;\r\n\r\n      // Verificar se o campo é válido antes de processar\r\n      if (!name || !Object.keys(formData).includes(name)) {\r\n        return;\r\n      }\r\n\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        [name]: value\r\n      }));\r\n\r\n      // Validação em tempo real apenas se o campo já foi tocado\r\n      if (touched[fieldName]) {\r\n        const error = validateField(fieldName, value || '');\r\n        setErrors(prev => ({\r\n          ...prev,\r\n          [name]: error\r\n        }));\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro no handleChange:', error);\r\n    }\r\n  }, [formData, touched, validateField]);\r\n\r\n  // Manipular blur (quando sai do campo) - usando useCallback\r\n  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n    try {\r\n      const { name, value } = e.target;\r\n      const fieldName = name as keyof FormData;\r\n\r\n      // Verificar se o campo é válido antes de processar\r\n      if (!name || !Object.keys(formData).includes(name)) {\r\n        return;\r\n      }\r\n\r\n      setTouched(prev => ({\r\n        ...prev,\r\n        [name]: true\r\n      }));\r\n\r\n      const error = validateField(fieldName, value || '');\r\n      setErrors(prev => ({\r\n        ...prev,\r\n        [name]: error\r\n      }));\r\n    } catch (error) {\r\n      console.error('Erro no handleBlur:', error);\r\n    }\r\n  }, [formData, validateField]);\r\n\r\n  // Enviar formulário\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Marcar todos os campos como tocados\r\n    setTouched({\r\n      name: true,\r\n      email: true,\r\n      message: true\r\n    });\r\n\r\n    if (!validateForm()) {\r\n      // Focar no primeiro campo com erro - validação mais segura\r\n      const newErrors: FormErrors = {};\r\n      Object.keys(formData).forEach((key) => {\r\n        const fieldName = key as keyof FormData;\r\n        const error = validateField(fieldName, formData[fieldName]);\r\n        if (error) {\r\n          newErrors[fieldName] = error;\r\n        }\r\n      });\r\n\r\n      const firstErrorField = Object.keys(newErrors)[0] as keyof FormData;\r\n      if (firstErrorField === 'name') nameRef.current?.focus();\r\n      else if (firstErrorField === 'email') emailRef.current?.focus();\r\n      else if (firstErrorField === 'message') messageRef.current?.focus();\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    setSubmitStatus('idle');\r\n\r\n    try {\r\n      // Enviar via EmailJS usando as credenciais existentes\r\n      await emailjs.send(\r\n        'service_4z3a60b',    // Service ID do EmailJS\r\n        'template_nc73bg4',   // Template ID do EmailJS\r\n        {\r\n          from_name: formData.name,\r\n          from_email: formData.email,\r\n          message: formData.message,\r\n          to_email: '<EMAIL>',\r\n          subject: `Nova mensagem de contato de ${formData.name}`,\r\n          reply_to: formData.email\r\n        },\r\n        'eRzZy4gTZ2NXGjFKz'  // User ID do EmailJS\r\n      );\r\n\r\n      // Sucesso\r\n      setSubmitStatus('success');\r\n      setFormData({ name: '', email: '', message: '' });\r\n      setTouched({ name: false, email: false, message: false });\r\n      setErrors({});\r\n\r\n      // Play success sound\r\n      playSubmitSuccess();\r\n\r\n    } catch (error) {\r\n      console.error('Erro ao enviar e-mail:', error);\r\n      setSubmitStatus('error');\r\n\r\n      // Play error sound\r\n      playSubmitError();\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // SUPER SIMPLES: só verifica se tem alguma coisa digitada\r\n  const isFormValid = () => {\r\n    return formData.name.length > 0 &&\r\n           formData.email.length > 0 &&\r\n           formData.message.length > 0;\r\n  };\r\n\r\n  // Verificar se campo individual está válido (para mostrar linha verde + ✓) - usando useMemo\r\n  const fieldValidationStates = useMemo(() => {\r\n    const states: Record<keyof FormData, { isValid: boolean; hasError: boolean }> = {\r\n      name: { isValid: false, hasError: false },\r\n      email: { isValid: false, hasError: false },\r\n      message: { isValid: false, hasError: false }\r\n    };\r\n\r\n    Object.keys(formData).forEach((key) => {\r\n      const fieldName = key as keyof FormData;\r\n      try {\r\n        const hasError = touched[fieldName] && !!errors[fieldName];\r\n        const isValid = touched[fieldName] && formData[fieldName] && !validateField(fieldName, formData[fieldName]);\r\n\r\n        states[fieldName] = { isValid, hasError };\r\n      } catch (error) {\r\n        console.error(`Erro ao verificar estado do campo ${fieldName}:`, error);\r\n        states[fieldName] = { isValid: false, hasError: false };\r\n      }\r\n    });\r\n\r\n    return states;\r\n  }, [formData, touched, errors, validateField]);\r\n\r\n  const isFieldValid = useCallback((fieldName: keyof FormData): boolean => {\r\n    return fieldValidationStates[fieldName]?.isValid || false;\r\n  }, [fieldValidationStates]);\r\n\r\n  const hasFieldError = useCallback((fieldName: keyof FormData): boolean => {\r\n    return fieldValidationStates[fieldName]?.hasError || false;\r\n  }, [fieldValidationStates]);\r\n\r\n  return (\r\n    <section id=\"contact\" className=\"py-8\">\r\n      {/* Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 30 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n        className=\"mb-12\"\r\n      >\r\n        <div className=\"max-w-2xl mx-auto text-left px-4 sm:px-6 lg:px-8\">\r\n          <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-text)] mb-4\">\r\n            {t('contact.title')}\r\n          </h1>\r\n          <p className=\"text-[var(--color-muted)] text-lg mb-4\">\r\n            {t('contact.description')}\r\n          </p>\r\n          {/* Linha Azul Animada - Similar ao Hero */}\r\n          <motion.div\r\n            initial={{ width: 0 }}\r\n            animate={{ width: \"120px\" }}\r\n            transition={{ duration: 0.8, delay: 0.6 }}\r\n            className=\"h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full\"\r\n          ></motion.div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Formulário */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 30 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.6, delay: 0.2, ease: \"easeOut\" }}\r\n        className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8\"\r\n      >\r\n        <form onSubmit={handleSubmit} className=\"space-y-6\" noValidate>\r\n\r\n          {/* Campo Nome */}\r\n          <div className=\"space-y-2\">\r\n            <label\r\n              htmlFor=\"name\"\r\n              className=\"block text-sm font-semibold text-[var(--color-text)]\"\r\n            >\r\n              <User className=\"inline w-4 h-4 mr-2\" aria-hidden=\"true\" />\r\n              {t('contact.form.name')}\r\n              <span className=\"text-[var(--color-error)] ml-1\" aria-label={t('contact.form.required')}>*</span>\r\n            </label>\r\n            <div className=\"relative\">\r\n              <input\r\n                ref={nameRef}\r\n                type=\"text\"\r\n                id=\"name\"\r\n                name=\"name\"\r\n                value={formData.name}\r\n                onChange={handleChange}\r\n                onBlur={handleBlur}\r\n                onFocus={() => playFieldFocus()}\r\n                className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-300 bg-[var(--color-surface)] text-[var(--color-text)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent ${\r\n                  hasFieldError('name')\r\n                    ? 'border-red-500 focus:ring-red-500'\r\n                    : isFieldValid('name')\r\n                    ? 'border-green-500 focus:ring-green-500'\r\n                    : 'border-[var(--color-border)] hover:border-[var(--color-primary)]/50'\r\n                }`}\r\n                placeholder={t('contact.form.namePlaceholder')}\r\n                aria-invalid={hasFieldError('name') ? 'true' : 'false'}\r\n                aria-describedby={hasFieldError('name') ? 'name-error' : undefined}\r\n                autoComplete=\"name\"\r\n              />\r\n              {/* Ícone de validação */}\r\n              {hasFieldError('name') && (\r\n                <X className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500\" aria-hidden=\"true\" />\r\n              )}\r\n              {isFieldValid('name') && (\r\n                <Check className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500\" aria-hidden=\"true\" />\r\n              )}\r\n            </div>\r\n            {errors.name && touched.name && (\r\n              <p id=\"name-error\" className=\"text-sm text-[var(--color-error)] flex items-center gap-1\" role=\"alert\">\r\n                <AlertCircle className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n                {errors.name}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Campo Email */}\r\n          <div className=\"space-y-2\">\r\n            <label\r\n              htmlFor=\"email\"\r\n              className=\"block text-sm font-semibold text-[var(--color-text)]\"\r\n            >\r\n              <Mail className=\"inline w-4 h-4 mr-2\" aria-hidden=\"true\" />\r\n              {t('contact.form.email')}\r\n              <span className=\"text-[var(--color-error)] ml-1\" aria-label={t('contact.form.required')}>*</span>\r\n            </label>\r\n            <div className=\"relative\">\r\n              <input\r\n                ref={emailRef}\r\n                type=\"email\"\r\n                id=\"email\"\r\n                name=\"email\"\r\n                value={formData.email}\r\n                onChange={handleChange}\r\n                onBlur={handleBlur}\r\n                onFocus={() => playFieldFocus()}\r\n                className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-300 bg-[var(--color-surface)] text-[var(--color-text)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent ${\r\n                  hasFieldError('email')\r\n                    ? 'border-red-500 focus:ring-red-500'\r\n                    : isFieldValid('email')\r\n                    ? 'border-green-500 focus:ring-green-500'\r\n                    : 'border-[var(--color-border)] hover:border-[var(--color-primary)]/50'\r\n                }`}\r\n                placeholder={t('contact.form.emailPlaceholder')}\r\n                aria-invalid={hasFieldError('email') ? 'true' : 'false'}\r\n                aria-describedby={hasFieldError('email') ? 'email-error' : undefined}\r\n                autoComplete=\"email\"\r\n                inputMode=\"email\"\r\n              />\r\n              {/* Ícone de validação */}\r\n              {hasFieldError('email') && (\r\n                <X className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500\" aria-hidden=\"true\" />\r\n              )}\r\n              {isFieldValid('email') && (\r\n                <Check className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500\" aria-hidden=\"true\" />\r\n              )}\r\n            </div>\r\n            {errors.email && touched.email && (\r\n              <p id=\"email-error\" className=\"text-sm text-[var(--color-error)] flex items-center gap-1\" role=\"alert\">\r\n                <AlertCircle className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n                {errors.email}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Campo Mensagem */}\r\n          <div className=\"space-y-2\">\r\n            <label\r\n              htmlFor=\"message\"\r\n              className=\"block text-sm font-semibold text-[var(--color-text)]\"\r\n            >\r\n              <MessageSquare className=\"inline w-4 h-4 mr-2\" aria-hidden=\"true\" />\r\n              {t('contact.form.message')}\r\n              <span className=\"text-[var(--color-error)] ml-1\" aria-label={t('contact.form.required')}>*</span>\r\n            </label>\r\n            <div className=\"relative\">\r\n              <textarea\r\n                ref={messageRef}\r\n                id=\"message\"\r\n                name=\"message\"\r\n                value={formData.message}\r\n                onChange={handleChange}\r\n                onBlur={handleBlur}\r\n                onFocus={() => playFieldFocus()}\r\n                rows={5}\r\n                className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-300 bg-[var(--color-surface)] text-[var(--color-text)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent resize-vertical ${\r\n                  hasFieldError('message')\r\n                    ? 'border-red-500 focus:ring-red-500'\r\n                    : isFieldValid('message')\r\n                    ? 'border-green-500 focus:ring-green-500'\r\n                    : 'border-[var(--color-border)] hover:border-[var(--color-primary)]/50'\r\n                }`}\r\n                placeholder={t('contact.form.messagePlaceholder')}\r\n                aria-invalid={hasFieldError('message') ? 'true' : 'false'}\r\n                aria-describedby={hasFieldError('message') ? 'message-error' : 'message-hint'}\r\n                minLength={10}\r\n              />\r\n              {/* Ícone de validação */}\r\n              {hasFieldError('message') && (\r\n                <X className=\"absolute right-3 top-3 w-5 h-5 text-red-500\" aria-hidden=\"true\" />\r\n              )}\r\n              {isFieldValid('message') && (\r\n                <Check className=\"absolute right-3 top-3 w-5 h-5 text-green-500\" aria-hidden=\"true\" />\r\n              )}\r\n            </div>\r\n            {errors.message && touched.message ? (\r\n              <p id=\"message-error\" className=\"text-sm text-[var(--color-error)] flex items-center gap-1\" role=\"alert\">\r\n                <AlertCircle className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n                {errors.message}\r\n              </p>\r\n            ) : (\r\n              <p id=\"message-hint\" className=\"text-xs text-[var(--color-muted)]\">\r\n                {t('contact.form.messageHint')}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Botão de Envio */}\r\n          <div>\r\n            <CTAButton\r\n              onClick={handleSubmit}\r\n              variant=\"primary\"\r\n              size=\"lg\"\r\n              icon={Send}\r\n              iconPosition=\"left\"\r\n              disabled={isSubmitting}\r\n              loading={isSubmitting}\r\n              className=\"w-full\"\r\n              ariaLabel={isSubmitting ? t('contact.form.sending') : t('contact.form.send')}\r\n            >\r\n              {isSubmitting ? t('contact.form.sending') : t('contact.form.send')}\r\n            </CTAButton>\r\n\r\n            {/* Mensagem de Privacidade - Integrada ao formulário */}\r\n            <div className=\"mt-2 text-center\">\r\n              <p className=\"text-sm text-[var(--color-muted)] max-w-prose mx-auto\">\r\n                {t('contact.form.privacy')}\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mensagens de Status - Embaixo do Botão */}\r\n          {submitStatus === 'success' && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -10 }}\r\n              className=\"mt-4 flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-400\"\r\n              role=\"status\"\r\n              aria-live=\"polite\"\r\n            >\r\n              <CheckCircle className=\"w-5 h-5 flex-shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"font-medium text-left\">\r\n                {i18n.language === 'en-US' ? 'Message sent successfully!' :\r\n                 i18n.language === 'es-ES' ? '¡Mensaje enviado con éxito!' :\r\n                 'Mensagem enviada com sucesso!'}\r\n              </span>\r\n            </motion.div>\r\n          )}\r\n\r\n          {submitStatus === 'error' && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -10 }}\r\n              className=\"mt-4 flex items-center gap-3 p-4 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-400\"\r\n              role=\"alert\"\r\n              aria-live=\"assertive\"\r\n            >\r\n              <AlertCircle className=\"w-5 h-5 flex-shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"font-medium text-left\">\r\n                {i18n.language === 'en-US' ? 'Error sending message. Please try again.' :\r\n                 i18n.language === 'es-ES' ? 'Error al enviar mensaje. Inténtalo de nuevo.' :\r\n                 'Erro ao enviar mensagem. Tente novamente.'}\r\n              </span>\r\n            </motion.div>\r\n          )}\r\n\r\n        </form>\r\n\r\n      </motion.div>\r\n\r\n\r\n\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Contact;\r\n"], "names": ["store", "_origin", "validateParams", "userID", "serviceID", "templateID", "EmailJSResponseStatus", "constructor", "httpResponse", "this", "status", "text", "responseText", "sendPost", "url", "data", "headers", "Promise", "resolve", "reject", "xhr", "XMLHttpRequest", "addEventListener", "target", "responseStatus", "open", "Object", "keys", "for<PERSON>ach", "key", "setRequestHeader", "send", "emailjs", "templatePrams", "uID", "_userID", "params", "lib_version", "user_id", "service_id", "template_id", "template_params", "JSON", "stringify", "Contact", "t", "i18n", "useTranslation", "playSubmitSuccess", "playSubmitError", "playFieldFocus", "useFormSounds", "formData", "setFormData", "useState", "name", "email", "message", "errors", "setErrors", "touched", "setTouched", "isSubmitting", "setIsSubmitting", "submitStatus", "setSubmitStatus", "nameRef", "useRef", "emailRef", "messageRef", "useEffect", "timer", "setTimeout", "clearTimeout", "isValidEmail", "validateField", "useCallback", "value", "lang", "language", "safeValue", "trim", "length", "undefined", "SecureValidation", "validateEmail", "error", "handleChange", "e", "fieldName", "includes", "prev", "handleBlur", "handleSubmit", "async", "preventDefault", "newErrors", "validateForm", "from_name", "from_email", "to_email", "subject", "reply_to", "firstErrorField", "current", "focus", "fieldValidationStates", "useMemo", "states", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "console", "isField<PERSON><PERSON>d", "hasFieldError", "section", "id", "className", "_jsx", "jsx", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "ease", "children", "h1", "p", "width", "delay", "form", "onSubmit", "noValidate", "label", "htmlFor", "User", "aria-hidden", "span", "aria-label", "input", "ref", "type", "onChange", "onBlur", "onFocus", "placeholder", "aria-invalid", "aria-<PERSON><PERSON>", "autoComplete", "X", "Check", "role", "AlertCircle", "Mail", "inputMode", "MessageSquare", "textarea", "rows", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CTAButton", "onClick", "variant", "size", "icon", "Send", "iconPosition", "disabled", "loading", "aria<PERSON><PERSON><PERSON>", "exit", "aria-live", "CheckCircle"], "mappings": "0ZAAO,MAAMA,EAAQ,CACjBC,QAAS,2BCDAC,EAAiB,CAACC,EAAQC,EAAWC,KAC9C,IAAKF,EACK,KAAA,iFAEV,IAAKC,EACK,KAAA,wEAEV,IAAKC,EACK,KAAA,mFAEH,OAAA,CAAA,ECVJ,MAAMC,EACT,WAAAC,CAAYC,GACRC,KAAKC,OAASF,EAAaE,OAC3BD,KAAKE,KAAOH,EAAaI,YACjC,ECFO,MAAMC,EAAW,CAACC,EAAKC,EAAMC,EAAU,CAAA,IACnC,IAAIC,SAAQ,CAACC,EAASC,KACnB,MAAAC,EAAM,IAAIC,eAChBD,EAAIE,iBAAiB,QAAQ,EAAGC,aACtB,MAAAC,EAAiB,IAAIlB,EAAsBiB,GACnB,MAA1BC,EAAed,QAA0C,OAAxBc,EAAeb,KAChDO,EAAQM,GAGRL,EAAOK,EACvB,IAEQJ,EAAIE,iBAAiB,SAAS,EAAGC,aACtBJ,EAAA,IAAIb,EAAsBiB,GAAO,IAE5CH,EAAIK,KAAK,OAAQzB,EAAMC,QAAUa,GAAK,GACtCY,OAAOC,KAAKX,GAASY,SAASC,IAC1BT,EAAIU,iBAAiBD,EAAKb,EAAQa,GAAI,IAE1CT,EAAIW,KAAKhB,EAAI,ICjBNiB,ECOK,CAAC5B,EAAWC,EAAY4B,EAAe9B,KACjD,MAAA+B,EAAM/B,GAAUH,EAAMmC,QACbjC,EAAAgC,EAAK9B,EAAWC,GAC/B,MAAM+B,EAAS,CACXC,YAAa,QACbC,QAASJ,EACTK,WAAYnC,EACZoC,YAAanC,EACboC,gBAAiBR,GAErB,OAAOpB,EAAS,uBAAwB6B,KAAKC,UAAUP,GAAS,CAC5D,eAAgB,oBACnB,ECKCQ,EAAoB,KACxB,MAAMC,EAAEA,EAAAA,KAAGC,GAASC,KACdC,kBAAEA,EAAAA,gBAAmBC,EAAiBC,eAAAA,GAAmCC,KAGxEC,EAAUC,GAAeC,WAAmB,CACjDC,KAAM,GACNC,MAAO,GACPC,QAAS,MAGJC,EAAQC,GAAaL,EAAAA,SAAqB,CAAA,IAC1CM,EAASC,GAAcP,WAAsB,CAClDC,MAAM,EACNC,OAAO,EACPC,SAAS,KAGJK,EAAcC,GAAmBT,EAAAA,UAAS,IAC1CU,EAAcC,GAAmBX,EAAAA,SAAuC,QAGzEY,EAAUC,SAAyB,MACnCC,EAAWD,SAAyB,MACpCE,EAAaF,SAA4B,MAG/CG,EAAAA,WAAU,KACR,GAAqB,YAAjBN,EAA4B,CACxBO,MAAAA,EAAQC,YAAW,KACvBP,EAAgB,OAAA,GACf,KACI,MAAA,IAAMQ,aAAaF,EAAAA,IAE3B,CAACP,IAGEU,MAKAC,EAAgBC,EAAAA,aAAY,CAACrB,EAAsBsB,KACnD,IACIC,MAAAA,SAAOhC,WAAMiC,WAAY,QACzBC,EAAYH,GAAS,GAE3B,OAAQtB,GACN,IAAK,OACC,OAACyB,EAAUC,OAKXD,EAAUC,OAAOC,OAAS,EACf,UAATJ,EAAyB,qCAChB,UAATA,EAAyB,6CACtB,6CAEFK,EATQ,UAATL,EAAyB,mBAChB,UAATA,EAAyB,2BACtB,qBASX,IAAK,QACC,OAACE,EAAUC,QAzBDzB,EA8BIwB,EA7BjBI,EAAiBC,cAAc7B,QAkCzB2B,EAJQ,UAATL,EAAyB,gBAChB,UAATA,EAAyB,iBACtB,mBAPM,UAATA,EAAyB,oBAChB,UAATA,EAAyB,0BACtB,uBASX,IAAK,UACC,OAACE,EAAUC,OAKXD,EAAUC,OAAOC,OAAS,GACf,UAATJ,EAAyB,yCAChB,UAATA,EAAyB,+CACtB,kDAEFK,EATQ,UAATL,EAAyB,sBAChB,UAATA,EAAyB,4BACtB,yBASX,QACSK,cAEJG,GAEA,OADCA,QAAAA,MAAM,8BAA+BA,GACtC,mBAAA,CAvDU,IAAC9B,CAuDX,GAER,CAAOuB,MAANjC,OAAMiC,EAAAA,EAAAA,WAmBJQ,EAAeX,eAAaY,IAC5B,IACF,MAAMjC,KAAEA,EAAAA,MAAMsB,GAAUW,EAAEjE,OACpBkE,EAAYlC,EAGd,IAACA,IAAS7B,OAAOC,KAAKyB,GAAUsC,SAASnC,GAC3C,OASEK,GANJP,GAAYsC,IAAS,IAChBA,EACHpC,CAACA,GAAOsB,MAINjB,EAAQ6B,GAAY,CACtB,MAAMH,EAAQX,EAAcc,EAAWZ,GAAS,IAChDlB,GAAUgC,IAAS,IACdA,EACHpC,CAACA,GAAO+B,KACV,QAEKA,GACCA,QAAAA,MAAM,wBAAyBA,EAAAA,IAExC,CAAClC,EAAUQ,EAASe,IAGjBiB,EAAahB,eAAaY,IAC1B,IACF,MAAMjC,KAAEA,EAAAA,MAAMsB,GAAUW,EAAEjE,OACpBkE,EAAYlC,EAGd,IAACA,IAAS7B,OAAOC,KAAKyB,GAAUsC,SAASnC,GAC3C,OAGFM,GAAW8B,IAAS,IACfA,EACHpC,CAACA,IAAO,MAGV,MAAM+B,EAAQX,EAAcc,EAAWZ,GAAS,IAChDlB,GAAUgC,IAAS,IACdA,EACHpC,CAACA,GAAO+B,YAEHA,GACCA,QAAAA,MAAM,sBAAuBA,EAAAA,IAEtC,CAAClC,EAAUuB,IAGRkB,EAAeC,MAAON,cAUtB,GATJA,EAAEO,iBAGSlC,EAAA,CACTN,MAAM,EACNC,OAAO,EACPC,SAAS,IA9EQ,MACnB,MAAMuC,EAAwB,CAAC,EAW/B,OATAtE,OAAOC,KAAKyB,GAAUxB,SAASC,IAC7B,MAAM4D,EAAY5D,EACZyD,EAAQX,EAAcc,EAAWrC,EAASqC,IAC5CH,IACFU,EAAUP,GAAaH,EAAAA,IAI3B3B,EAAUqC,GAC+B,IAAlCtE,OAAOC,KAAKqE,GAAWd,MAAW,EAqEpCe,GAAD,CAkBJlC,GAAgB,GAChBE,EAAgB,QAEZ,UAEIjC,EACJ,kBACA,mBACA,CACEkE,UAAW9C,EAASG,KACpB4C,WAAY/C,EAASI,MACrBC,QAASL,EAASK,QAClB2C,SAAU,qBACVC,QAAS,+BAA+BjD,EAASG,OACjD+C,SAAUlD,EAASI,OAErB,qBAIFS,EAAgB,WACJZ,EAAA,CAAEE,KAAM,GAAIC,MAAO,GAAIC,QAAS,KACjCI,EAAA,CAAEN,MAAM,EAAOC,OAAO,EAAOC,SAAS,IACjDE,EAAU,CAAA,GAGVX,UAEOsC,GACCA,QAAAA,MAAM,yBAA0BA,GACxCrB,EAAgB,SAGhBhB,GAAAA,CACQ,QACRc,GAAgB,EAAA,CAtChB,KAfE,CAEF,MAAMiC,EAAwB,CAAC,EAC/BtE,OAAOC,KAAKyB,GAAUxB,SAASC,IAC7B,MAAM4D,EAAY5D,EACZyD,EAAQX,EAAcc,EAAWrC,EAASqC,IAC5CH,IACFU,EAAUP,GAAaH,EAAAA,IAI3B,MAAMiB,EAAkB7E,OAAOC,KAAKqE,GAAW,GACvB,SAApBO,EAAoCC,OAAAA,EAAAA,EAAAA,UAASC,EAAAA,QACpB,UAApBF,EAAsCC,OAAAA,EAAAA,EAAAA,UAASC,EAAAA,QAC3B,YAApBF,IAA0CC,OAAAA,EAAAA,EAAAA,UAASC,EAAAA,QAC5D,CAsCgB,EAYdC,EAAwBC,EAAAA,SAAQ,KACpC,MAAMC,EAA0E,CAC9ErD,KAAM,CAAEsD,SAAS,EAAOC,UAAU,GAClCtD,MAAO,CAAEqD,SAAS,EAAOC,UAAU,GACnCrD,QAAS,CAAEoD,SAAS,EAAOC,UAAU,IAgBhCF,OAbPlF,OAAOC,KAAKyB,GAAUxB,SAASC,IAC7B,MAAM4D,EAAY5D,EACd,IACF,MAAMiF,EAAWlD,EAAQ6B,MAAgB/B,EAAO+B,GAC1CoB,EAAUjD,EAAQ6B,IAAcrC,EAASqC,KAAed,EAAcc,EAAWrC,EAASqC,IAEhGmB,EAAOnB,GAAa,CAAEoB,UAASC,kBACxBxB,GACPyB,QAAQzB,MAAM,qCAAqCG,KAAcH,GACjEsB,EAAOnB,GAAa,CAAEoB,SAAS,EAAOC,UAAU,EAAM,KAInDF,CAAAA,GACN,CAACxD,EAAUQ,EAASF,EAAQiB,IAEzBqC,EAAepC,eAAaa,UACzBiB,OAAAA,OAAAA,EAAsBjB,EAAAA,SAAtBiB,EAAAA,EAAkCG,WAAW,CAAA,GACnD,CAACH,IAEEO,EAAgBrC,eAAaa,UAC1BiB,OAAAA,OAAAA,EAAsBjB,EAAAA,SAAtBiB,EAAAA,EAAkCI,YAAY,CAAA,GACpD,CAACJ,kBAGDQ,UAAAA,CAAQC,GAAG,UAAUC,UAAU,iBAE9BC,EAAAC,IAACC,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKC,KAAM,WACnCX,UAAU,QAEVY,gBAACR,MAAAA,CAAIJ,UAAU,mEACZa,KAAAA,CAAGb,UAAU,oFACXvE,EAAE,yBAEJqF,IAAAA,CAAEd,UAAU,kDACVvE,EAAE,yBAGLwE,EAAAC,IAACC,EAAOC,IAAG,CACTC,QAAS,CAAEU,MAAO,GAClBP,QAAS,CAAEO,MAAO,SAClBN,WAAY,CAAEC,SAAU,GAAKM,MAAO,IACpChB,UAAU,uEAMhBC,EAAAC,IAACC,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKM,MAAO,GAAKL,KAAM,WAC/CX,UAAU,yCAEVY,gBAACK,OAAAA,CAAKC,SAAUzC,EAAcuB,UAAU,YAAYmB,YAAU,mBAG3Df,MAAAA,CAAIJ,UAAU,6BACZoB,QAAAA,CACCC,QAAQ,OACRrB,UAAU,uEAETsB,EAAAA,CAAKtB,UAAU,sBAAsBuB,cAAY,SACjD9F,EAAE,2BACF+F,OAAAA,CAAKxB,UAAU,iCAAiCyB,aAAYhG,EAAE,yBAA0BmF,SAAA,gBAE1FR,MAAAA,CAAIJ,UAAU,2BACZ0B,QAAAA,CACCC,IAAK7E,EACL8E,KAAK,OACL7B,GAAG,OACH5D,KAAK,OACLsB,MAAOzB,EAASG,KAChB0F,SAAU1D,EACV2D,OAAQtD,EACRuD,QAAS,IAAMjG,IACfkE,UAAW,mNACTH,EAAc,QACV,oCACAD,EAAa,QACb,wCACA,uEAENoC,YAAavG,EAAE,gCACfwG,eAAcpC,EAAc,QAAU,OAAS,QAC/CqC,mBAAkBrC,EAAc,QAAU,kBAAe9B,EACzDoE,aAAa,SAGdtC,EAAc,SACbI,EAAAC,IAACkC,EAAAA,CAAEpC,UAAU,2EAA2EuB,cAAY,SAErG3B,EAAa,SACZK,EAAAC,IAACmC,EAAAA,CAAMrC,UAAU,6EAA6EuB,cAAY,YAG7GjF,EAAOH,MAAQK,EAAQL,aACrB2E,IAAAA,CAAEf,GAAG,aAAaC,UAAU,4DAA4DsC,KAAK,wBAC3FC,EAAAA,CAAYvC,UAAU,UAAUuB,cAAY,SAC5CjF,EAAOH,kBAMbiE,MAAAA,CAAIJ,UAAU,6BACZoB,QAAAA,CACCC,QAAQ,QACRrB,UAAU,uEAETwC,EAAAA,CAAKxC,UAAU,sBAAsBuB,cAAY,SACjD9F,EAAE,4BACF+F,OAAAA,CAAKxB,UAAU,iCAAiCyB,aAAYhG,EAAE,yBAA0BmF,SAAA,gBAE1FR,MAAAA,CAAIJ,UAAU,2BACZ0B,QAAAA,CACCC,IAAK3E,EACL4E,KAAK,QACL7B,GAAG,QACH5D,KAAK,QACLsB,MAAOzB,EAASI,MAChByF,SAAU1D,EACV2D,OAAQtD,EACRuD,QAAS,IAAMjG,IACfkE,UAAW,mNACTH,EAAc,SACV,oCACAD,EAAa,SACb,wCACA,uEAENoC,YAAavG,EAAE,iCACfwG,eAAcpC,EAAc,SAAW,OAAS,QAChDqC,mBAAkBrC,EAAc,SAAW,mBAAgB9B,EAC3DoE,aAAa,QACbM,UAAU,UAGX5C,EAAc,UACbI,EAAAC,IAACkC,EAAAA,CAAEpC,UAAU,2EAA2EuB,cAAY,SAErG3B,EAAa,UACZK,EAAAC,IAACmC,EAAAA,CAAMrC,UAAU,6EAA6EuB,cAAY,YAG7GjF,EAAOF,OAASI,EAAQJ,cACtB0E,IAAAA,CAAEf,GAAG,cAAcC,UAAU,4DAA4DsC,KAAK,wBAC5FC,EAAAA,CAAYvC,UAAU,UAAUuB,cAAY,SAC5CjF,EAAOF,mBAMbgE,MAAAA,CAAIJ,UAAU,6BACZoB,QAAAA,CACCC,QAAQ,UACRrB,UAAU,uEAET0C,EAAAA,CAAc1C,UAAU,sBAAsBuB,cAAY,SAC1D9F,EAAE,8BACF+F,OAAAA,CAAKxB,UAAU,iCAAiCyB,aAAYhG,EAAE,yBAA0BmF,SAAA,gBAE1FR,MAAAA,CAAIJ,UAAU,2BACZ2C,WAAAA,CACChB,IAAK1E,EACL8C,GAAG,UACH5D,KAAK,UACLsB,MAAOzB,EAASK,QAChBwF,SAAU1D,EACV2D,OAAQtD,EACRuD,QAAS,IAAMjG,IACf8G,KAAM,EACN5C,UAAW,mOACTH,EAAc,WACV,oCACAD,EAAa,WACb,wCACA,uEAENoC,YAAavG,EAAE,mCACfwG,eAAcpC,EAAc,WAAa,OAAS,QAClDqC,mBAAkBrC,EAAc,WAAa,gBAAkB,eAC/DgD,UAAW,KAGZhD,EAAc,YACbI,EAAAC,IAACkC,EAAAA,CAAEpC,UAAU,8CAA8CuB,cAAY,SAExE3B,EAAa,YACZK,EAAAC,IAACmC,EAAAA,CAAMrC,UAAU,gDAAgDuB,cAAY,YAGhFjF,EAAOD,SAAWG,EAAQH,eACxByE,IAAAA,CAAEf,GAAG,gBAAgBC,UAAU,4DAA4DsC,KAAK,wBAC9FC,EAAAA,CAAYvC,UAAU,UAAUuB,cAAY,SAC5CjF,EAAOD,WAGVyG,EAAA5C,IAACY,IAAAA,CAAEf,GAAG,eAAeC,UAAU,6CAC5BvE,EAAE,wCAMR2E,MAAAA,iBACE2C,EAAAA,CACCC,QAASvE,EACTwE,QAAQ,UACRC,KAAK,KACLC,KAAMC,EACNC,aAAa,OACbC,SAAU5G,EACV6G,QAAS7G,EACTsD,UAAU,SACVwD,UAA0B/H,EAAfiB,EAAiB,uBAA4B,8BAExCjB,EAAfiB,EAAiB,uBAA4B,6BAI/C0D,MAAAA,CAAIJ,UAAU,mBACbY,eAACE,IAAAA,CAAEd,UAAU,iEACVvE,EAAE,+BAMS,YAAjBmB,UACEuD,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BkD,KAAM,CAAEnD,QAAS,EAAGC,GAAG,IACvBP,UAAU,gKACVsC,KAAK,SACLoB,YAAU,yBAETC,EAAAA,CAAY3D,UAAU,wBAAwBuB,cAAY,eAC1DC,OAAAA,CAAKxB,UAAU,iCACK,UAAlBtE,EAAKiC,SAAuB,6BACV,UAAlBjC,EAAKiC,SAAuB,8BAC5B,qCAKW,UAAjBf,UACEuD,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BkD,KAAM,CAAEnD,QAAS,EAAGC,GAAG,IACvBP,UAAU,oJACVsC,KAAK,QACLoB,YAAU,4BAETnB,EAAAA,CAAYvC,UAAU,wBAAwBuB,cAAY,eAC1DC,OAAAA,CAAKxB,UAAU,iCACK,UAAlBtE,EAAKiC,SAAuB,2CACV,UAAlBjC,EAAKiC,SAAuB,+CAC5B", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}