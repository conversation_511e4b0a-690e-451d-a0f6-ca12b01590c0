{"version": 3, "mappings": ";oaAQaA,MAWAC,EAAgBC,IACpBF,MAPA,eALoBG,EAYP,UAAUD,KAVPE,WAAW,KAAOD,EAAKE,MAAM,GAAKF,IAF/B,IAACA,CAYa,ECVpCG,EAAoC,EACxCC,OAAO,KACPC,YAAW,EACXC,SAAS,aACTC,YAAY,OAEZ,MAAMC,EDqBC,CACLC,KAAMX,EAAa,wBACnBY,MAAOZ,EAAa,0BCtBhBa,EAAc,CAClBC,GAAI,OACJC,GAAI,OACJC,GAAI,QAGAC,EAAkB,CACtBH,GAAI,UACJC,GAAI,UACJC,GAAI,aAGAE,EAA8B,aAAXV,EACrB,mCACA,wCAGDW,OAAIV,UAAW,GAAGS,KAAoBT,qBAEpCU,OAAIV,UAAU,2BAEZW,OACCC,IAAKX,EAAUC,KACfW,IAAI,qCACJb,UAAW,GAAGI,EAAYP,yEAC1BiB,MAAgB,OAATjB,EAAgB,KAAgB,OAATA,EAAgB,KAAO,KACrDkB,OAAiB,OAATlB,EAAgB,KAAgB,OAATA,EAAgB,KAAO,KACtDmB,MAAO,CACLD,OAAiB,OAATlB,EAAgB,OAAkB,OAATA,EAAgB,OAAS,OAC1DiB,MAAgB,OAATjB,EAAgB,OAAkB,OAATA,EAAgB,OAAS,OACzDoB,UAAW,OACXC,SAAU,QAEZC,QAAQ,OACRC,SAAS,gBAIVT,OACCC,IAAKX,EAAUE,MACfU,IAAI,qCACJb,UAAW,GAAGI,EAAYP,+EAC1BiB,MAAgB,OAATjB,EAAgB,KAAgB,OAATA,EAAgB,KAAO,KACrDkB,OAAiB,OAATlB,EAAgB,KAAgB,OAATA,EAAgB,KAAO,KACtDmB,MAAO,CACLD,OAAiB,OAATlB,EAAgB,OAAkB,OAATA,EAAgB,OAAS,OAC1DiB,MAAgB,OAATjB,EAAgB,OAAkB,OAATA,EAAgB,OAAS,OACzDoB,UAAW,OACXC,SAAU,QAEZC,QAAQ,OACRC,SAAS,aAKZtB,UACEY,OAAIV,UAAU,gCACZqB,QACCrB,UAAW,iBAAiBQ,EAAgBX,KAC5CmB,MAAO,CAAEM,MAAO,qBACjBC,SAAA,eAGAF,QACCrB,UAAW,GAAGQ,EAAgBX,gBAC9BmB,MAAO,CAAEM,MAAO,sBACjBC,SAAA,yCCpF8E,SAASC,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,OAAO,IAAID,IAAIA,EAAEE,WAAW,SAASC,EAAEC,GAAG,SAASP,EAAEC,GAAM,IAACO,EAAEJ,EAAEK,KAAKR,GAAG,OAAOA,GAAGM,EAAEN,EAAE,CAAC,CAAC,SAASS,EAAET,GAAM,IAACO,EAAEJ,EAAEO,MAAMV,GAAG,OAAOA,GAAGM,EAAEN,EAAE,CAAC,CAAC,SAASO,EAAEP,GAAOC,MAAED,EAAEW,KAAKN,EAAEL,EAAEY,QAAQX,EAAED,EAAEY,MAAMX,aAAaC,EAAED,EAAE,IAAIC,GAAG,SAASF,GAAGA,EAAEC,EAAE,KAAKY,KAAKd,EAAEU,EAAE,CAACF,GAAGJ,EAAEA,EAAEW,MAAMd,EAAK,KAAKQ,OAAO,GAAG,CAAC,SAASC,EAAET,EAAEC,GAAO,IAAAC,EAAEC,EAAEE,EAAEC,EAAEP,EAAE,CAACgB,MAAM,EAAEC,KAAK,WAAW,GAAG,EAAEX,EAAE,GAAG,MAAMA,EAAE,GAAG,OAAOA,EAAE,EAAE,EAAEY,KAAK,GAAGC,IAAI,IAAW,OAAAZ,EAAE,CAACE,KAAKC,EAAE,GAAGC,MAAMD,EAAE,GAAGU,OAAOV,EAAE,IAAI,mBAAmBW,SAASd,EAAEc,OAAOC,UAAU,WAAkB,OAAAC,IAAI,GAAGhB,EAAE,SAASG,EAAEH,GAAG,OAAO,SAASG,GAAG,OAAO,SAASH,GAAG,GAAGJ,EAAE,MAAM,IAAIqB,UAAU,mCAAmC,KAAKxB,GAAM,IAAC,GAAGG,EAAE,EAAEC,IAAIE,EAAE,EAAEC,EAAE,GAAGH,EAAEgB,OAAOb,EAAE,GAAGH,EAAEO,SAASL,EAAEF,EAAEgB,SAASd,EAAEmB,KAAKrB,GAAG,GAAGA,EAAEK,SAASH,EAAEA,EAAEmB,KAAKrB,EAAEG,EAAE,KAAKK,KAAY,OAAAN,EAAE,OAAOF,EAAE,EAAEE,IAAIC,EAAE,CAAC,EAAEA,EAAE,GAAGD,EAAEO,QAAQN,EAAE,IAAI,KAAK,EAAE,KAAK,EAAIA,IAAE,MAAM,KAAK,EAASP,SAAEgB,QAAQ,CAACH,MAAMN,EAAE,GAAGK,MAAK,GAAI,KAAK,EAAEZ,EAAEgB,QAAQZ,EAAEG,EAAE,GAAGA,EAAE,CAAC,GAAG,SAAS,KAAK,EAAEA,EAAEP,EAAEmB,IAAIO,MAAM1B,EAAEkB,KAAKQ,MAAM,SAAS,QAAW,MAAYpB,GAAVA,EAAEN,EAAEkB,MAAUS,OAAO,GAAGrB,EAAEA,EAAEqB,OAAO,KAAK,IAAIpB,EAAE,IAAI,IAAIA,EAAE,IAAI,CAACP,EAAE,EAAE,QAAQ,CAAC,GAAG,IAAIO,EAAE,MAAMD,GAAGC,EAAE,GAAGD,EAAE,IAAIC,EAAE,GAAGD,EAAE,IAAI,CAACN,EAAEgB,MAAMT,EAAE,GAAG,KAAK,CAAI,OAAIA,EAAE,IAAIP,EAAEgB,MAAMV,EAAE,GAAG,CAACN,EAAEgB,MAAMV,EAAE,GAAGA,EAAEC,EAAE,KAAK,CAAC,GAAGD,GAAGN,EAAEgB,MAAMV,EAAE,GAAG,CAACN,EAAEgB,MAAMV,EAAE,GAAGN,EAAEmB,IAAIS,KAAKrB,GAAG,KAAK,CAAGD,EAAA,IAAIN,EAAEmB,IAAIO,MAAM1B,EAAEkB,KAAKQ,MAAM,SAASnB,EAAEL,EAAEuB,KAAKxB,EAAED,EAAE,OAAOC,GAAGM,EAAE,CAAC,EAAEN,GAAGG,EAAE,CAAC,CAAC,QAAQD,EAAEG,EAAE,CAAC,CAAC,GAAG,EAAEC,EAAE,GAAG,MAAMA,EAAE,GAAS,OAACM,MAAMN,EAAE,GAAGA,EAAE,QAAG,EAAOK,MAAK,EAAG,CAApxB,CAAsxB,CAACL,EAAEG,GAAG,CAAC,CAAC,CAAC,SAASF,EAAEP,GAAO,IAAAC,EAAE,mBAAmBmB,QAAQA,OAAOC,SAASnB,EAAED,GAAGD,EAAEC,GAAGE,EAAE,EAAE,GAAGD,EAAE,OAAOA,EAAEsB,KAAKxB,GAAM,GAAAA,GAAG,iBAAiBA,EAAE0B,OAAa,OAAClB,KAAK,WAAW,OAAOR,GAAGG,GAAGH,EAAE0B,SAAS1B,OAAE,GAAQ,CAACY,MAAMZ,GAAGA,EAAEG,KAAKQ,MAAMX,EAAE,GAAG,MAAM,IAAIuB,UAAUtB,EAAE,0BAA0B,kCAAkC,CAAC,SAAS2B,EAAE5B,EAAEC,GAAG,IAAIC,EAAE,mBAAmBkB,QAAQpB,EAAEoB,OAAOC,UAAa,IAACnB,EAAS,OAAAF,EAAM,IAAAG,EAAEE,EAAEC,EAAEJ,EAAEsB,KAAKxB,GAAGD,EAAE,GAAM,IAAC,WAAM,IAASE,GAAGA,KAAK,MAAME,EAAEG,EAAEE,QAAQG,MAAMZ,EAAE4B,KAAKxB,EAAES,MAAM,OAAOZ,GAAKK,EAAA,CAACwB,MAAM7B,EAAE,CAAC,QAAW,IAAIG,IAACA,EAAEQ,OAAOT,EAAEI,EAAEa,SAASjB,EAAEsB,KAAKlB,EAAE,CAAC,QAAW,GAAAD,QAAQA,EAAEwB,KAAK,CAAC,CAAQ9B,QAAC,CAAC,SAAS+B,EAAE9B,EAAEC,EAAEC,GAAG,GAAM,IAAI6B,UAAUL,OAAO,QAAQvB,EAAEE,EAAE,EAAEC,EAAEL,EAAEyB,OAAOrB,EAAEC,EAAED,KAAKF,GAAGE,KAAKJ,IAAIE,IAAIA,EAAE6B,MAAMC,UAAU/D,MAAMsD,KAAKvB,EAAE,EAAEI,IAAIF,EAAEE,GAAGJ,EAAEI,IAAW,OAAAL,EAAEkC,OAAO/B,GAAG6B,MAAMC,UAAU/D,MAAMsD,KAAKvB,GAAG,CAAC,SAASkC,EAAEnC,EAAEC,EAAEC,EAAEC,EAAEE,GAAG,QAAQC,EAAE,GAAG6B,EAAE,EAAEA,EAAEJ,UAAUL,OAAOS,IAAMA,IAAE,GAAGJ,UAAUI,GAAG,OAAOpC,EAAEuB,KAAK,OAAO,GAAQ,WAAW,IAAIvB,EAAEoC,EAAEC,EAAEC,EAAEC,EAAEC,EAAS,OAAA9B,EAAEa,MAAM,SAASb,GAAG,OAAOA,EAAEM,OAAO,KAAK,EAAEN,EAAEQ,KAAKU,KAAK,CAAC,EAAE,GAAG,GAAG,KAAK5B,EAAEQ,EAAED,GAAG6B,EAAEpC,EAAES,OAAOC,EAAEM,MAAM,EAAE,KAAK,EAAE,GAAGoB,EAAExB,KAAW,OAAC,EAAE,IAAI,cAAOyB,EAAED,EAAEvB,QAAgB,IAAI,SAAe,OAAC,EAAE,GAAG,IAAI,SAAe,OAAC,EAAE,GAAG,IAAI,WAAiB,OAAC,EAAE,GAAS,OAAC,EAAE,GAAG,KAAK,EAAQ,OAAC,EAAE4B,EAAExC,EAAEC,EAAEmC,EAAElC,EAAEC,EAAEE,IAAI,KAAK,EAAE,OAAOI,EAAEO,OAAO,CAAC,EAAE,IAAI,KAAK,EAAE,MAAM,CAAC,EAAEyB,EAAEL,IAAI,KAAK,EAAE,OAAO3B,EAAEO,OAAO,CAAC,EAAE,IAAI,KAAK,EAAE,MAAM,CAAC,EAAEoB,EAAEtB,WAAM,EAAOgB,EAAE,CAAC9B,EAAEC,EAAEC,EAAEC,EAAEE,GAAGuB,EAAEtB,IAAG,KAAM,KAAK,EAAE,OAAOG,EAAEO,OAAO,CAAC,EAAE,IAAI,KAAK,EAAQ,OAAC,EAAEoB,GAAG,KAAK,EAAE3B,EAAEO,OAAOP,EAAEM,MAAM,GAAG,KAAK,GAAG,OAAOoB,EAAEpC,EAAES,OAAO,CAAC,EAAE,GAAG,KAAK,GAAS,OAAC,EAAE,IAAI,KAAK,GAAU6B,SAAE5B,EAAEO,OAAOsB,EAAE,CAACT,MAAMQ,GAAG,CAAC,EAAE,IAAI,KAAK,GAAM,IAACF,IAAIA,EAAExB,OAAO4B,EAAExC,EAAEoB,SAASoB,EAAEf,KAAKzB,EAAE,CAAC,QAAWuC,WAAQA,EAAET,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,SAASW,EAAExC,EAAEC,EAAEC,EAAEC,EAAEE,EAAEC,GAAG,OAAOP,EAAEuB,KAAK,OAAO,GAAQ,WAAW,IAAIvB,EAAEQ,EAAS,OAAAE,EAAEa,MAAM,SAASb,GAAG,OAAOA,EAAEM,OAAO,KAAK,EAAE,OAAOhB,EAAEC,EAAE0C,aAAa,GAAc1C,EAAuGD,EAA9FG,EAAE0B,EAA8F1B,GAAzFhC,MAAM,GAAjCqC,EAA2CuB,EAAEA,EAAE,GAAGF,EAAE5B,IAAG,GAAI,CAAC2C,MAAK,GAAIC,WAAW,SAAS5C,EAAEC,GAAUC,SAAED,KAAKD,CAAC,IAAU,CAAC,EAAEoC,EAAEpC,EAAE8B,EAAEA,EAAE,GAAGF,EAAEU,EAAEvC,EAAEE,EAAEM,KAAI,GAAIqB,EAAES,EAAEnC,EAAED,EAAEM,KAAI,GAAIJ,EAAEE,EAAEC,IAAI,KAAK,EAAE,OAAOG,EAAEO,OAAO,CAAC,GAAnM,IAAShB,EAASE,CAAoL,GAAG,GAAG,CAAC,SAASuC,EAAEzC,GAAG,OAAOD,EAAEuB,KAAK,OAAO,GAAQ,WAAkB,OAAAb,EAAEa,MAAM,SAASrB,GAAG,OAAOA,EAAEc,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,IAAIX,SAAS,SAASH,GAAU,OAAA4C,WAAW5C,EAAED,EAAE,KAAK,KAAK,EAAE,OAAOC,EAAEe,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,SAASoB,EAAEpC,EAAEC,EAAEC,EAAEC,EAAEE,GAAG,OAAON,EAAEuB,KAAK,OAAO,GAAQ,WAAe,IAAAhB,EAAEP,EAAE+B,EAAEK,EAAEK,EAAEJ,EAAEC,EAAEC,EAAEC,EAAEO,EAAEC,EAAEC,EAAEC,EAAS,OAAAxC,EAAEa,MAAM,SAAS4B,GAAG,OAAOA,EAAEnC,OAAO,KAAK,EAAK,GAAAT,EAAEL,EAAEI,EAAE,CAAC,IAAIN,EAAE,EAAE+B,EAAE,EAAEA,EAAE7B,EAAEyB,OAAOI,IAAI,GAAGK,EAAEP,EAAE,CAAC3B,EAAE6B,EAAE,GAAG7B,EAAE6B,IAAI,GAAGU,EAAEL,EAAE,IAAIC,EAAED,EAAE,IAAIT,OAAOc,EAAEd,QAAQ,KAAKU,EAAE,CAACrC,EAAE+B,EAAE,KAAK,CAACxB,EAAEL,EAAE/B,MAAM6B,EAAEE,EAAEyB,OAAO,CAACwB,EAAEnC,MAAM,EAAE,KAAK,EAAEmC,EAAEjC,KAAKU,KAAK,CAAC,EAAE,EAAE,EAAE,IAAIU,EAAE9B,EAAE,SAASP,GAAG,IAAIC,EAAEC,EAAEC,EAAEE,EAAEC,EAAEP,EAAE6B,EAAS,OAAAnB,EAAEa,MAAM,SAASQ,GAAG,OAAOA,EAAEf,OAAO,KAAK,EAAEd,EAAE,SAASD,GAAU,OAAAS,EAAEa,MAAM,SAASrB,GAAG,OAAOA,EAAEc,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,CAACoC,GAAG,SAASlD,GAAG,OAAOmD,uBAAuB,WAAW,OAAOnD,EAAEyC,YAAY1C,CAAC,GAAG,EAAEqD,OAAO,SAASpD,GAAOC,MAAED,EAAEyC,aAAa,GAAG,MAAM,KAAK1C,GAAGE,EAAEwB,OAAO1B,EAAE0B,OAAO,SAAS,SAAS,IAAI,KAAK,EAAE,OAAOzB,EAAEe,OAAO,CAAC,GAAG,GAAG,EAAEc,EAAEf,MAAM,EAAE,KAAK,EAAEe,EAAEb,KAAKU,KAAK,CAAC,EAAE,EAAE,EAAE,IAAIzB,EAAEK,EAAEP,GAAGG,EAAED,EAAEM,OAAOsB,EAAEf,MAAM,EAAE,KAAK,EAAE,OAAOZ,EAAEQ,KAAK,CAAC,EAAE,IAAIN,EAAEF,EAAES,MAAM,CAAC,EAAEX,EAAEI,KAAK,KAAK,EAAEyB,EAAEd,OAAOc,EAAEf,MAAM,EAAE,KAAK,EAAE,OAAOZ,EAAED,EAAEM,OAAO,CAAC,EAAE,GAAG,KAAK,EAAQ,OAAC,EAAE,GAAG,KAAK,EAASF,SAAEwB,EAAEd,OAAOjB,EAAE,CAAC8B,MAAMvB,GAAG,CAAC,EAAE,GAAG,KAAK,EAAK,IAACH,IAAIA,EAAEQ,OAAOiB,EAAE1B,EAAEiB,SAASS,EAAEJ,KAAKtB,EAAE,CAAC,QAAWH,WAAQA,EAAE8B,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,EAAE,MAAM,CAAC,GAAG,GAAG,CAAjsB,CAAmsBvB,IAAIgC,EAAED,EAAE7B,OAAO0C,EAAEnC,MAAM,EAAE,KAAK,EAAE,OAAOuB,EAAE3B,KAAK,CAAC,EAAE,IAAI4B,EAAED,EAAE1B,MAAMkC,EAAE,YAAYP,EAAEc,OAAOrD,GAAGE,EAAEA,GAAGoD,KAAKC,SAAS,IAAIpD,EAAEA,GAAGmD,KAAKC,SAAS,IAAIhB,EAAEY,GAAGnD,GAAG,CAAC,EAAEyC,EAAEK,KAAK,KAAK,EAAII,EAAAlC,OAAOkC,EAAEnC,MAAM,EAAE,KAAK,EAAE,OAAOuB,EAAED,EAAE7B,OAAO,CAAC,EAAE,GAAG,KAAK,EAAQ,OAAC,EAAE,GAAG,KAAK,EAAS,OAAAuC,EAAEG,EAAElC,OAAOgC,EAAE,CAACnB,MAAMkB,GAAG,CAAC,EAAE,GAAG,KAAK,EAAK,IAACT,IAAIA,EAAE3B,OAAOsC,EAAEZ,EAAElB,SAAS8B,EAAEzB,KAAKa,EAAE,CAAC,QAAW,GAAAW,QAAQA,EAAEnB,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,SAASQ,EAAErC,EAAEC,EAAEC,GAAG,IAAIC,EAAEE,EAAE,YAAO,IAASH,IAAIA,EAAE,GAAGO,EAAEa,MAAM,SAAShB,GAAG,OAAOA,EAAES,OAAO,KAAK,EAAEZ,EAAEF,EAAED,GAAGK,EAAEF,EAAEuB,OAAOpB,EAAES,MAAM,EAAE,KAAK,EAAE,OAAOb,EAAEG,EAAE,CAAC,EAAEF,EAAEjC,MAAM,IAAIgC,GAAGsD,KAAK,KAAK,CAAC,EAAE,GAAG,KAAK,EAAE,OAAOlD,EAAEU,OAAO,CAAC,EAAE,GAAG,KAAK,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,SAASsB,EAAEtC,EAAEC,EAAEC,GAAG,IAAIC,EAAEE,EAAE,YAAO,IAASH,IAAIA,EAAE,GAAGO,EAAEa,MAAM,SAAShB,GAAG,OAAOA,EAAES,OAAO,KAAK,EAAEZ,EAAEF,EAAED,GAAGK,EAAEF,EAAEuB,OAAOpB,EAAES,MAAM,EAAE,KAAK,EAAE,OAAOV,EAAEH,EAAE,CAAC,EAAEC,EAAEjC,MAAM,IAAImC,GAAGmD,KAAK,KAAK,CAAC,EAAE,GAAG,KAAK,EAAE,OAAOlD,EAAEU,OAAO,CAAC,EAAE,GAAG,KAAK,EAAE,MAAM,CAAC,GAAG,GAAG,EAAmC,SAAShB,EAAEC,QAAY,IAAAA,IAAIA,EAAE,IAAI,IAAIC,EAAED,EAAEwD,SAAe,uBAAoBC,SAAS,CAAC,IAAIvD,EAAEuD,SAASC,MAAMD,SAASE,qBAAqB,QAAQ,GAAGvD,EAAEqD,SAASG,cAAc,SAAWxD,EAAAyD,KAAK,WAAW,QAAQ5D,GAAGC,EAAE4D,WAAW5D,EAAE6D,aAAa3D,EAAEF,EAAE4D,YAAY5D,EAAE8D,YAAY5D,GAAGA,EAAE6D,WAAW7D,EAAE6D,WAAWC,QAAQnE,EAAEK,EAAE4D,YAAYP,SAASU,eAAepE,GAAG,CAAC,CAA9V,CAAgW,4MAA4M,IAAI8C,EAAExC,EAAC+D,KAAChE,EAACiE,YAAE,SAASjE,EAAEC,GAAOP,MAAEM,EAAEkE,SAAS9D,EAAEJ,EAAEmE,OAAOjE,EAAEF,EAAE9B,UAAUiE,EAAEnC,EAAEoE,MAAMhC,OAAE,IAASD,EAAE,GAAGA,EAAEJ,EAAE/B,EAAEqE,cAAcrC,EAAEhC,EAAEsE,sBAAsBrC,OAAE,IAASD,GAAGA,EAAES,EAAEzC,EAAEuE,qBAAqB7B,OAAE,IAASD,GAAGA,EAAEE,EAAE3C,EAAEwE,QAAQ5B,OAAE,IAASD,EAAE,OAAOA,EAAEE,EAAE7C,EAAEyE,SAASC,OAAE,IAAS7B,EAAE,SAASlD,GAAG,OAAO8B,EAAE,GAAGF,EAAE5B,IAAG,EAAG,EAAEkD,EAAE8B,EAAE3E,EAAE4E,OAAOC,OAAE,IAASF,GAAGA,EAAEG,EAAE9E,EAAEd,MAAM6F,EAAE,SAASpF,EAAEC,GAAG,IAAIC,EAAE,CAAE,EAAC,QAAQC,KAAKH,EAAEqF,OAAOpD,UAAUqD,eAAe9D,KAAKxB,EAAEG,IAAIF,EAAEsF,QAAQpF,GAAG,IAAID,EAAEC,GAAGH,EAAEG,IAAI,GAAG,MAAMH,GAAG,mBAAmBqF,OAAOG,sBAAsB,CAAC,IAAInF,EAAE,EAAE,IAAIF,EAAEkF,OAAOG,sBAAsBxF,GAAGK,EAAEF,EAAEuB,OAAOrB,IAAMJ,EAAAsF,QAAQpF,EAAEE,IAAI,GAAGgF,OAAOpD,UAAUwD,qBAAqBjE,KAAKxB,EAAEG,EAAEE,MAAMH,EAAEC,EAAEE,IAAIL,EAAEG,EAAEE,IAAI,CAAQ,OAAAH,CAAC,CAAxU,CAA0UG,EAAE,CAAC,WAAW,SAAS,YAAY,QAAQ,gBAAgB,wBAAwB,uBAAuB,UAAU,WAAW,SAAS,UAAUqF,EAAEN,EAAE,cAAcO,EAAEP,EAAE,eAAeQ,EAAER,EAAES,KAAKzD,IAAIA,EAAEK,GAAG,IAAIqD,EAAE,IAAI9D,MAAM,GAAG+D,KAAK,IAAI,CAACtD,EAAEL,GAAG4D,SAAS,SAAShG,EAAEC,GAAG,cAAcD,GAAG,IAAI,SAAS8F,EAAE7F,GAAGqD,KAAK2C,IAAIjG,EAAE,KAAK,MAAM,IAAI,SAAS,IAAIE,EAAEF,EAAE8D,KAAK3D,EAAEH,EAAEY,MAAS,oBAAiBT,EAAE,MAAS,uBAAuBD,IAAI4F,EAAA7F,GAAGE,GAAE,IAAI,IAAI+F,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEV,EAAE,GAAGW,EAAEX,EAAE,GAAGY,EAAE,SAAS1G,EAAEE,QAAG,IAASA,IAAIA,EAAE,MAAUG,MAAEJ,EAAC0G,OAACzG,GAAUC,oBAAG,WAAeH,IAAA,mBAAmBA,EAAEA,EAAEK,EAAEuG,SAAS5G,EAAE4G,QAAQvG,EAAEuG,QAAQ,GAAG,CAAC5G,IAAIK,CAAC,CAAtI,CAAwIC,GAAGuG,EAA1yD,2BAA8yDX,EAAE3F,EAAE,GAAG2B,OAAOgD,EAAE2B,EAAE,IAAI,IAAI3E,OAAO3B,GAAG2E,EAAE2B,EAAE,GAAGV,EAAElG,EAAC0G,QAAE,WAAW,IAAI3G,EAAEC,EAAEF,EAAEU,IAAI,IAAIT,EAAEmC,EAAE,iBAAiB1B,IAAIR,EAAE+B,MAAM,EAAEvB,GAAGsF,KAAKhG,GAAG+G,QAAY,IAAA5G,EAAEF,EAAE8B,EAAEA,EAAE,GAAGF,EAAE3B,IAAG,GAAI,CAACD,IAAG,GAAI8B,EAAE,GAAGF,EAAE3B,IAAG,GAAI,OAAOkC,EAAErB,WAAM,EAAOgB,EAAE,CAAC4E,EAAEE,QAAQ7B,EAAEyB,EAAEC,EAAEnE,GAAGV,EAAE1B,IAAG,IAAK,WAAoB,CAAC,IAAIkG,EAAEnG,WAAIoG,EAAEpG,UAAE,GAAIqG,EAAErG,UAAE,GAAIsG,EAAE3E,EAAE1B,WAAE,GAAG,GAAG,GAAGmG,EAAEO,UAAUN,EAAEM,SAAQ,GAAIzG,aAAG,WAAW,OAAOkG,EAAEO,UAAUR,EAAEQ,QAAQT,EAAES,UAAUP,EAAEO,SAAQ,GAAIL,GAAG,SAASvG,GAAG,OAAOA,EAAE,CAAC,IAAI,WAAWsG,EAAEM,SAASR,EAAEQ,SAASR,EAAEQ,SAAS,CAAC,GAAG,IAAI,IAAIG,EAAE9D,EAAE+D,EAAEjE,EAAEhD,EAAEkH,MAAM,SAASjH,GAAG,MAAM,iBAAiBA,CAAC,KAAK,GAAG,KAAK,OAAOA,EAAE6D,cAAckD,EAAE,CAAC,cAAcpB,EAAE,aAAaD,EAAEG,KAAKD,EAAErG,MAAM4F,EAAE5G,UAAU2H,EAAEpG,SAAS4F,EAAE1F,EAAE6D,cAAc,OAAO,CAAC,cAAc,OAAOqD,IAAIR,EAAE5G,SAASkH,IAAIA,EAAEE,IAAIxB,OAAE,EAAOgB,GAAG,KAAK,SAAS1G,EAAEC,GAAW,YCah3P,MAAMkH,EAAgD,EAC3D5C,WACAM,UAAU,OACVJ,QAAQ,GACRlG,YAAY,GACZiG,SAAS4C,IACTnC,UAAS,EACTL,wBAAuB,WAGpByC,GACC9C,WACAM,UACAJ,QACAlG,YACAiG,SACAS,SACAL,uBACArF,MAAO,CAAE+H,QAAS,gBAClBC,YAAU,WCpBVC,EAA0C,EAAGjJ,YAAY,OAC7D,MAAM0B,EAAEA,OAAGwH,GAASC,KACbC,EAAaC,GAAkBC,EAAMC,SAAS,GAGrDD,EAAME,WAAU,KACCC,MAAQA,EAAO,MAC7B,CAACP,EAAKQ,wBAwBNhJ,OAAIV,UAAW,QAAQA,IACtBuB,eAAC2C,KAAElE,UAAU,oBACV2J,SAvBY,MAEXC,QAAclI,EAAE,eAGlBkI,MAA+B,gBAAhBA,EACVA,SAIT,OAAQV,EAAKQ,UACX,IAAK,QACI,sSACT,IAAK,QACI,uSACT,QACS,6RAONC,QCxBHE,EAAsD,EAC1DjJ,MACAC,MACAC,QACAC,SACAf,YAAY,GACZmB,UAAU,OACV2I,YAAW,EACXC,UACAC,cACAC,SACAC,cAEA,MAAOC,EAAaC,GAAkBb,YAAS,IACxCc,EAAYC,GAAiBf,YAAS,IACtCgB,EAAUC,GAAejB,EAASO,YAAwB,UAAZ3I,GAC/CsJ,EAASrC,SAAyB,MAClCsC,EAAetC,SAAuB,MAG5CoB,aAAU,KACR,GAAgB,SAAZrI,IAAuB2I,GAAYY,EAAarC,QAAS,CAC3D,MAAMsC,EAAW,IAAIC,sBAClBC,IACO,MAACC,GAASD,EACZC,EAAMC,iBACRP,GAAY,GACZG,EAASK,aAAU,GAGvB,CACEC,WAAY,WACZC,UAAW,MAMR,OAFEC,UAAQT,EAAarC,SAEvB,IAAMsC,EAASK,YAAU,IAEjC,CAAC7J,EAAS2I,IAEb,MAcMsB,EAAsC,CAC1CtK,MAAO,OACPI,SAAU,GAAGJ,MACbuK,SAAU,WACVC,SAAU,SAEVC,cAAe,GATGxK,EAAUD,EAAS,OAUrCC,OAAQ,GAIJyK,EAAkC,CACtCH,SAAU,WACVI,IAAK,EACLC,KAAM,EACN5K,MAAO,OACPC,OAAQ,OACR4K,UAAW,QACXC,WAAY,oBACZC,QAAS1B,EAAc,EAAI,GAIvB2B,EAAwC,CAC5CT,SAAU,WACVI,IAAK,EACLC,KAAM,EACN5K,MAAO,OACPC,OAAQ,OACRgL,gBAAiB,UACjBhD,QAAS,OACTiD,WAAY,SACZC,eAAgB,SAChBJ,QAAS1B,EAAc,EAAI,EAC3ByB,WAAY,mCAIXlL,OACCiI,IAAK+B,EACL1K,UAAW,uBAAuBA,IAClCgB,MAAOoK,kBAGN1K,OAAIM,MAAO8K,EACT9B,WACCkC,EAAAC,IAACxL,OACCC,IAAKoJ,EACLnJ,IAAI,GACJG,MAAO,CACLF,MAAO,OACPC,OAAQ,OACR4K,UAAW,QACXS,OAAQ,YACRC,UAAW,gBAIfH,EAAAC,IAACzL,OACCM,MAAO,CACLF,MAAO,OACPC,OAAQ,OACRgL,gBAAiB,UACjBO,aAAc,MACdC,UAAW,sDAOlBhC,UACEiC,qBACEzC,SACE0C,UAAOC,OAAQ3C,EAASxE,KAAK,qBAE/B5E,OACCgI,IAAK8B,EACL7J,MACAC,MACAC,QACAC,SACAI,QAAS2I,EAAW,QAAU3I,EAC9BC,SAAU0I,EAAW,OAAS,WACzBA,GAAY,CAAE6C,cAAe,QAClC3L,MAAOwK,EACPvB,OApGc,KACtBG,GAAe,GACfH,cAmGQC,QAhGe,KACvBI,GAAc,GACdJ,cAgGQ0C,MAAO,eAAe9L,eAAmBA,WAM9CuJ,SACE3J,OACCM,MAAO,CACLqK,SAAU,WACVI,IAAK,EACLC,KAAM,EACN5K,MAAO,OACPC,OAAQ,OACRgL,gBAAiB,UACjBhD,QAAS,OACTiD,WAAY,SACZC,eAAgB,SAChB3K,MAAO,UACPuL,SAAU,OACVC,UAAW,SACXC,QAAS,OAEZxL,SAAA,6BAST,GAAwB,oBAAb4D,SAA0B,CAC7BnE,QAAQmE,SAASG,cAAc,SACrCtE,EAAMmD,YAAc,0VAqBfgB,SAASC,KAAK4H,cAAc,iCACzBC,eAAa,4BAA6B,QACvC7H,cAAKM,YAAY1E,GAE9B,CCrNO,MA+EMkM,EAA+B,KAC1C,MAAOC,EAAgBC,GAAqB7D,YAAS,IAC/C8D,cAAEA,GAjFwB,EAACC,EAAkC,MACnE,MAAMC,MAAEA,EAAQ,IAAKC,oBAAmB,GAASF,GAC1CD,EAAeI,GAAoBlE,YAAS,IAC5CmE,EAAeC,GAAoBpE,YAAS,GAkE5C,OAhEPC,aAAU,KAER,GAAI,wBAAyBoE,OAAQ,CACnC,MAAMjD,EAAW,IAAIkD,qBAAqBC,IAClCjD,QAAUiD,EAAKC,aACJlD,EAAQA,EAAQ1H,OAAS,KAOxCsK,GAAiB,GACjB9C,EAASK,aAGLwC,GACFlJ,YAAW,KACTqJ,GAAiB,KAChBJ,GACL,IAIA,IACF5C,EAASQ,QAAQ,CAAE6C,WAAY,CAAC,oCACzB1K,GACC2K,aAAK,iCAAkC3K,GAE/CgB,YAAW,KACTmJ,GAAiB,GACbD,GACFG,GAAiB,KAElB,KAIL,MAAO,KACLhD,EAASK,YAAU,CACrB,CAGA1G,YAAW,KACTmJ,GAAiB,GACbD,GACFG,GAAiB,KAElB,OAEJ,CAACJ,EAAOC,IAGXhE,aAAU,KACF0E,QAAgB5J,YAAW,KAC/BmJ,GAAiB,GACbD,GACFG,GAAiB,KAElB,KAEI,UAAMQ,aAAaD,KACzB,CAACV,IAEG,CACLH,gBACAK,gBAAeF,GAAmBE,EACpC,EAS0BU,CAAmB,CAAEb,MAAO,MAa/C,OAXP/D,aAAU,KACR,GAAI6D,EAAe,CAEXgB,QAAQ/J,YAAW,KACvB8I,GAAkB,KACjB,KAEI,UAAMe,aAAaE,MAE3B,CAAChB,IAEG,CACLF,iBACAE,gBACF,EC5EIiB,EAAkC,EAAGC,WACzC,MAAM7M,EAAEA,OAAGwH,GAASC,IACdqF,EPRC,CACLC,KAAMlP,EAAa,uBACnBmP,IAAKnP,EAAa,wBOOd4N,eAAEA,GAAkCD,iBAKvCyB,WAAQ3O,UAAU,2DAA2D4O,kBAAgB,gBAC5FrN,gBAACb,OAAIV,UAAU,6GAGZU,OACCV,UAAU,kDACVgB,MAAO,CAAEsK,SAAU,WAEnB/J,eAACb,OAAIV,UAAU,qBAEbuB,gBAACb,OAAIV,UAAU,2GAGZU,OAAIV,UAAU,2KAGdU,OAAIV,UAAU,sEAGZU,OAAIV,UAAU,2BAEZU,OAAIV,UAAU,yFAGdU,OAAIV,UAAU,yEACbuB,eAACsI,GACCjJ,IAAK4N,EAAcE,IACnB3E,QAASyE,EAAcC,KACvB5N,IAAKa,EAAE,sBACPZ,MAAO,IACPC,OAAQ,IACR+I,UAAU,EACV9J,UAAU,6CAKbU,OAAIV,UAAU,wHAIhBU,OAAIV,UAAU,+CAEZ6O,MAAG7O,UAAU,wGACX0B,EAAE,wBAIJwC,KAAElE,UAAU,uEAAuEuB,SAAA,8BAKnF2C,KAAElE,UAAU,2DAA2DuB,SAAA,8BAKvE2C,KAAElE,UAAU,2DAA2DuB,SAAA,iCAMzEb,OAAIV,UAAU,2MACZU,OAAIV,UAAU,yDACbuB,eAAC3B,GACCC,KAAK,KACLC,UAAU,EACVE,UAAU,yCAIbqB,QAAKrB,UAAU,qCAAqCuB,SAAA,6DAQ9Db,OAAIV,UAAU,8DAEZU,OAAIV,UAAU,wBACZ8O,MACCC,GAAG,gBACH/O,UAAU,oDACVgB,MAAO,CACLgO,kBAAmB,UACnBC,qBAAsB,aACtBC,WAAY,OACZ7C,UAAW,gBACX8C,QAAS,uCAGV9N,QAAKrB,UAAU,0CAAiC0B,EAAE,yBAAyB,OAC5EwK,EAAAC,IAACiD,eACA/N,QAAKrB,UAAU,0FACbmN,WACCjB,EAAAC,IAACkD,YAASC,SAAU5N,EAAE,iCACpBH,eAACqH,GAEC5C,SAAU,CACRtE,EAAE,iCACF,IACAA,EAAE,sCACF,IACAA,EAAE,uCACF,IACAA,EAAE,0CACF,KAEF1B,UAAU,0FACVkG,MAAO,GACPD,OAAQ,EACRI,sBAAsB,GAdjB6C,EAAKQ,YAkBdhI,EAAE,4CAMPhB,OAAIV,UAAU,sFAIhBiJ,SAAiBC,EAAKQ,iBAGtBhJ,OAAIV,UAAU,kDAEZuP,GACCC,KAAK,4BACLC,OAAO,SACPC,IAAI,sBACJC,QAAQ,UACR9P,KAAK,KACL+P,KAAMC,EACNC,aAAa,OACb9P,UAAU,qHAET0B,EAAE,4BAIJ6N,GACCC,KAAK,wFACLC,OAAO,SACPC,IAAI,sBACJC,QAAQ,QACR9P,KAAK,KACL+P,KAAMG,EACND,aAAa,OACb9P,UAAU,uJAET0B,EAAE,8BAIJ6N,GACCC,KAAK,uDACLC,OAAO,SACPC,IAAI,sBACJC,QAAQ,QACR9P,KAAK,KACL+P,KAAMI,EACNF,aAAa,OACb9P,UAAU,uJAET0B,EAAE,kCCnNXuO,EAAqB,UAEtBlO,KACCyN,KAAK,gBACLxP,UAAU,ySACVkQ,SAAU,EACX3O,SAAA,kCCUC4O,EAA0B,EAC9BC,QAAQ,uCACRC,cAAc,6LACdC,WAAW,sHACXC,QAAQ,yDACRC,MAAM,6CACNjL,OAAO,UACPkL,SAAS,2BACTC,gBACAC,eACAhC,UACAiC,qBAEA,MAAMC,EAAU,4CAGVC,EAAUN,EAAI9Q,WAAW,SAAWqR,EAAiBC,YAAYR,GAAOA,EAAM,GAAGK,IAAUL,IAC3FS,EAAeV,EAAM7Q,WAAW,SAAWqR,EAAiBC,YAAYT,GAASA,EAAQ,GAAGM,IAAUN,kBAGzGW,mBAEEd,SAAOA,mBACPe,QAAK5C,KAAK,cAAc6C,QAASf,UACjCc,QAAK5C,KAAK,WAAW6C,QAASd,UAC9Ba,QAAK5C,KAAK,SAAS6C,QAASX,UAC5BU,QAAK5C,KAAK,SAAS6C,QAAQ,wBAC3BD,QAAK5C,KAAK,WAAW6C,QAAQ,gBAC7BD,QAAK5C,KAAK,gBAAgB6C,QAAQ,iBAGlCC,QAAK3B,IAAI,YAAYF,KAAMsB,UAG3BK,QAAKG,SAAS,UAAUF,QAAS7L,UACjC4L,QAAKG,SAAS,WAAWF,QAAShB,UAClCe,QAAKG,SAAS,iBAAiBF,QAASf,UACxCc,QAAKG,SAAS,WAAWF,QAASH,UAClCE,QAAKG,SAAS,iBAAiBF,QAAQ,eACvCD,QAAKG,SAAS,kBAAkBF,QAAQ,cACxCD,QAAKG,SAAS,gBAAgBF,QAAQ,qBACtCD,QAAKG,SAAS,SAASF,QAASN,UAChCK,QAAKG,SAAS,eAAeF,QAAQ,mCACrCD,QAAKG,SAAS,YAAYF,QAAQ,UAGzB,YAAT7L,GACC2G,EAAAqF,KAAAC,WAAA,iBACGL,QAAKG,SAAS,iBAAiBF,QAASX,IACxC9B,SAAYwC,QAAKG,SAAS,kBAAkBF,QAASzC,IACrD+B,SAAkBS,QAAKG,SAAS,yBAAyBF,QAASV,IAClEC,SAAiBQ,QAAKG,SAAS,wBAAwBF,QAAST,aAKpEQ,QAAK5C,KAAK,eAAe6C,QAAQ,8BACjCD,QAAK5C,KAAK,gBAAgB6C,QAAShB,UACnCe,QAAK5C,KAAK,sBAAsB6C,QAASf,UACzCc,QAAK5C,KAAK,gBAAgB6C,QAASH,UACnCE,QAAK5C,KAAK,kBAAkB6C,QAAQ,yBACpCD,QAAK5C,KAAK,eAAe6C,QAAQ,yBAGjCD,QAAK5C,KAAK,cAAc6C,QAAQ,kBAChCD,QAAK5C,KAAK,0BAA0B6C,QAAQ,YAG5CR,SACEa,UAAOlM,KAAK,sBACVmM,cAAKC,UAAUf,SCwCbgB,EAAiB,CAC5B,WAAY,qBACZ,SAAU,CAhIgB,CAC1B,WAAY,qBACZ,QAAS,SACTrD,KAAQ,2BACRsD,SAAY,sBACZxB,YAAe,qJACfG,IAAO,6CACPD,MAAS,8DACTuB,MAAS,qBACTC,WAAc,CACZ,YACA,iBACA,YACA,kBACA,eACA,sBACA,QACA,uBACA,kBAEFC,OAAU,CACR,6CACA,mCACA,qCAEFC,SAAY,CACV,QAAS,eACT1D,KAAQ,cAEV2D,QAAW,CACT,QAAS,gBACTC,gBAAmB,SACnBC,eAAkB,OAIO,CAC3B,WAAY,qBACZ,QAAS,UACT7D,KAAQ,8BACR8B,YAAe,2EACfG,IAAO,6CACPC,OAAU,CACR,QAAS,SACTlC,KAAQ,4BAEV8D,WAAc,QACdC,cAAiB,OACjBC,gBAAmB,CACjB,QAAS,SACThE,KAAQ,4BAEViE,gBAAmB,CACjB,QAAS,eACT/C,OAAU,yEACV,cAAe,qCAIY,CAC7B,WAAY,qBACZ,QAAS,eACTlB,KAAQ,8BACR8B,YAAe,4EACfoC,QAAW,CACT,QAAS,SACTlE,KAAQ,4BAEViC,IAAO,6CACPkC,YAAe,OACfL,WAAc,QACdM,MAAS,YACTrC,SAAY,yDAGkB,CAC9B,WAAY,qBACZ,QAAS,iBACTsC,gBAAmB,CACjB,CACE,QAAS,WACTvH,SAAY,EACZkD,KAAQ,OACRsE,KAAQ,8CAEV,CACE,QAAS,WACTxH,SAAY,EACZkD,KAAQ,WACRsE,KAAQ,uDAEV,CACE,QAAS,WACTxH,SAAY,EACZkD,KAAQ,UACRsE,KAAQ,0DCtFRC,EAAwBC,QAAK,IAAMC,GAAA,IAAAC,OAAO,qFAC1CC,EAAqBH,QAAK,IAAMC,GAAA,IAAAC,OAAO,iFACvCE,EAAgBJ,QAAK,IAAMC,GAAA,IAAAC,OAAO,4EAClCG,EAAeL,QAAK,WAAME,OAAO,2DAEjCI,EAAQ,KACN,MAAAC,cAAEA,GCgJqB,MAEzBC,MACA,IACFA,EAAWC,IACL,MACKD,EAAA,CAAEE,SAAU,IAAI,CAwBtB,OAAEH,cArBaI,eAAY,KACmB,oBAAX9F,SAKjC+F,iBAAY/F,OAAO+F,WAAa,GACvC/F,OAAO+F,UAAUvQ,KAAK,CACpBwQ,MAAOC,EAAiBC,OAAOC,UAC/BC,UAAWT,EAASE,SACpBQ,WAAY9O,SAASiL,MACrB8D,cAAetG,OAAO2F,SAAS/D,KAC/B2E,WAAW,IAAIC,MAAOC,gBACxB,GAMC,CAACd,EAASE,WAEU,ED9KGa,GAG1B9K,aAAU,KACR8J,MACC,CAACA,IAEJ,MAIMiB,EAAW,CACf,CACEC,WAAY,SACZC,SAAU,wGAEZ,CACED,WAAY,YACZC,SAAU,wGAEZ,CACED,WAAY,YACZC,SAAU,wGAEZ,CACED,WAAY,kBACZC,SAAU,yGAKRC,EAAYH,EAASI,KAAIC,GAAWA,EAAQH,iBExCzB,GACzBI,SAAS,GACTtH,QAAQ,IACRuH,WAAU,GACS,MAEnBtL,aAAU,KAEF6E,QAAQ/J,YAAW,KAChBmD,WAAQsN,IACbC,EAAcD,KAChB,GACCxH,GAEI,UAAMY,aAAaE,KACzB,CAACwG,EAAQtH,IAENyH,QAAiBD,IAOf1D,QAAOlM,SAASG,cAAc,QACpC+L,EAAK3B,IAAM,WACX2B,EAAK7B,KAAOuF,EACZ1D,EAAK4D,GAAK,WAGV5D,EAAK6D,QAAU,KACLjH,aAAK,6BAA6B8G,IAAO,EAI9B5P,SAAS6H,cAAc,cAAc+H,QAE/C3P,cAAKM,YAAY2L,GA6B9B,EFxBY8D,CAAA,CACVN,OAAQ,GACRtH,MAAO,ME0BqB,CAACmH,IAC/BlL,aAAU,KACR,MAeM6E,EAAQ/J,YAfS,KACXmD,WAAQ7G,IACVyQ,QAAOlM,SAASG,cAAc,QACpC+L,EAAK3B,IAAM,WACX2B,EAAK7B,KAAO5O,EACZyQ,EAAK4D,GAAK,QAEW9P,SAAS6H,cAAc,cAAcpM,QAE/CwE,cAAKM,YAAY2L,KAE9B,GAIuC,KAClC,UAAMlD,aAAaE,KACzB,CAACqG,GAAU,EF3CdU,CAAiBV,UAKdhU,OAAIV,UAAU,6CACdmQ,GACCC,MAAM,uCACNC,YAAY,6LACZC,SAAS,sHACTM,eAAgBgB,IAElB1F,EAAAC,IAAC8D,aACAoF,QAAKtG,GAAG,eAAe/O,UAAU,wEAE/BU,OAAIqO,GAAG,SAAS/O,UAAU,2BAEzBuB,eAACb,OAAIV,UAAU,uDAEbuB,eAAC+M,GAhDPC,KAAM,uCAuDHI,WAAQI,GAAG,WAAW/O,UAAU,iBAAiB4O,kBAAgB,mBAChErN,eAACb,OAAIV,UAAU,kDACZsV,SAAO5U,IAAG,CACT6U,QAAS,CAAE1J,QAAS,EAAG/H,EAAG,IAC1B0R,YAAa,CAAE3J,QAAS,EAAG/H,EAAG,GAC9B2R,SAAU,CAAEC,MAAM,GAClB9J,WAAY,CAAE+J,SAAU,oBAEvB9G,MAAGE,GAAG,mBAAmB/O,UAAU,UAAUuB,SAAA,gCAC7C8N,YAASC,SAAUsG,MAACC,MACnBtU,eAACuR,GAAgByB,4BAOxB5F,WAAQI,GAAG,UAAU/O,UAAU,gDAAgD4O,kBAAgB,kBAC9FrN,eAACb,OAAIV,UAAU,2DACZsV,SAAO5U,IAAG,CACT6U,QAAS,CAAE1J,QAAS,EAAG/H,EAAG,IAC1B0R,YAAa,CAAE3J,QAAS,EAAG/H,EAAG,GAC9B2R,SAAU,CAAEC,MAAM,GAClB9J,WAAY,CAAE+J,SAAU,oBAEvB9G,MAAGE,GAAG,kBAAkB/O,UAAU,UAAUuB,SAAA,8BAC5C8N,YAASC,SAAUsG,MAACC,MACnBtU,SAAAqU,MAAC1C,qBAORvE,WAAQI,GAAG,UAAU/O,UAAU,iBAAiB4O,kBAAgB,kBAC/DrN,eAACb,OAAIV,UAAU,kDACZsV,SAAO5U,IAAG,CACT6U,QAAS,CAAE1J,QAAS,EAAG/H,EAAG,IAC1B0R,YAAa,CAAE3J,QAAS,EAAG/H,EAAG,GAC9B2R,SAAU,CAAEC,MAAM,GAClB9J,WAAY,CAAE+J,SAAU,oBAEvB9G,MAAGE,GAAG,kBAAkB/O,UAAU,UAAUuB,SAAA,kBAC5C8N,YAASC,SAAUsG,MAACC,MACnBtU,SAAAqU,MAACzC,wBAQV9D,YAASC,SAAUsG,MAACC,MACnBtU,SAAAqU,MAACxC", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "getImagePath", "filename", "path", "startsWith", "slice", "IxDFLogo", "size", "showText", "layout", "className", "logoPaths", "dark", "white", "sizeClasses", "sm", "md", "lg", "textSizeClasses", "containerClasses", "div", "img", "src", "alt", "width", "height", "style", "maxHeight", "max<PERSON><PERSON><PERSON>", "loading", "decoding", "span", "color", "children", "i", "e", "t", "r", "n", "Promise", "o", "a", "c", "next", "u", "throw", "done", "value", "then", "apply", "label", "sent", "trys", "ops", "return", "Symbol", "iterator", "this", "TypeError", "call", "pop", "length", "push", "l", "error", "s", "arguments", "Array", "prototype", "concat", "f", "h", "y", "v", "b", "d", "p", "textContent", "NaN", "findIndex", "setTimeout", "m", "w", "g", "x", "S", "op", "requestAnimationFrame", "opCode", "Math", "random", "join", "insertAt", "document", "head", "getElementsByTagName", "createElement", "type", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "append<PERSON><PERSON><PERSON>", "styleSheet", "cssText", "createTextNode", "memo", "forwardRef", "sequence", "repeat", "speed", "deletionSpeed", "omitDeletionAnimation", "preRenderFirstString", "wrapper", "splitter", "E", "_", "cursor", "k", "O", "T", "Object", "hasOwnProperty", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "A", "C", "N", "role", "P", "fill", "for<PERSON>ach", "abs", "j", "I", "G", "D", "M", "R", "q", "F", "B", "useRef", "current", "Q", "flat", "W", "L", "find", "ref", "TypewriterText", "Infinity", "TypeAnimation", "display", "aria-live", "CriticalBio", "i18n", "useTranslation", "forceUpdate", "setForceUpdate", "React", "useState", "useEffect", "prev", "language", "getBioText", "bioFromI18n", "CLSOptimizedImage", "priority", "webpSrc", "placeholder", "onLoad", "onError", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "isInView", "setIsInView", "imgRef", "containerRef", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "disconnect", "rootMargin", "threshold", "observe", "containerStyle", "position", "overflow", "paddingBottom", "imageStyle", "top", "left", "objectFit", "transition", "opacity", "placeholder<PERSON><PERSON><PERSON>", "backgroundColor", "alignItems", "justifyContent", "jsxRuntimeExports", "jsx", "filter", "transform", "borderRadius", "animation", "picture", "source", "srcSet", "fetchpriority", "sizes", "fontSize", "textAlign", "padding", "querySelector", "setAttribute", "useTypewriterLCPOptimization", "showTypewriter", "setShowTypewriter", "isLCPComplete", "options", "delay", "enableAnimations", "setIsLCPComplete", "shouldAnimate", "setShouldAnimate", "window", "PerformanceObserver", "list", "getEntries", "entryTypes", "warn", "fallbackTimer", "clearTimeout", "useLCPOptimization", "timer", "Profile", "name", "profileImages", "webp", "png", "section", "aria-<PERSON>by", "h2", "h1", "id", "contentVisibility", "containIntrinsicSize", "<PERSON><PERSON><PERSON><PERSON>", "contain", "br", "Suspense", "fallback", "CTAButton", "href", "target", "rel", "variant", "icon", "MessageCircle", "iconPosition", "Download", "Linkedin", "SkipLink", "tabIndex", "SEO", "title", "description", "keywords", "image", "url", "author", "publishedTime", "modifiedTime", "structuredData", "siteUrl", "fullUrl", "SecureValidation", "validateUrl", "fullImageUrl", "<PERSON><PERSON><PERSON>", "meta", "content", "link", "property", "jsxs", "_Fragment", "script", "JSON", "stringify", "mainPageSchema", "jobTitle", "email", "knowsAbout", "sameAs", "worksFor", "address", "addressLocality", "addressCountry", "inLanguage", "copyrightYear", "copyrightHolder", "potentialAction", "creator", "dateCreated", "genre", "itemListElement", "item", "ProjectShowcase", "lazy", "__vitePreload", "import", "BacklogCycle", "Contact", "Footer", "Index", "trackPageView", "location", "useLocation", "pathname", "useCallback", "dataLayer", "event", "ANALYTICS_CONFIG", "EVENTS", "PAGE_VIEW", "page_path", "page_title", "page_location", "timestamp", "Date", "toISOString", "usePageTracking", "projects", "projectKey", "imageUrl", "imageSrcs", "map", "project", "routes", "onHover", "route", "prefetchRoute", "as", "onerror", "usePrefetch", "useImagePrefetch", "main", "motion", "initial", "whileInView", "viewport", "once", "duration", "_jsx", "LoadingSpinner"], "ignoreList": [2], "sources": ["../../src/utils/assetPaths.ts", "../../src/components/ui/IxDFLogo.tsx", "../../node_modules/react-type-animation/dist/esm/index.es.js", "../../src/components/ui/TypewriterText.tsx", "../../src/components/CriticalBio.tsx", "../../src/components/CLSOptimizedImage.tsx", "../../src/hooks/useLCPOptimization.ts", "../../src/components/Profile.tsx", "../../src/components/SkipLink.tsx", "../../src/components/SEO.tsx", "../../src/utils/structuredData.ts", "../../src/pages/Index.tsx", "../../src/hooks/useAnalytics.ts", "../../src/hooks/usePrefetch.ts"], "sourcesContent": ["/**\r\n * Utility functions for handling asset paths in development and production\r\n */\r\n\r\n/**\r\n * Get the correct asset path for the current environment\r\n * Must respect Vite's base path configuration\r\n */\r\nexport const getAssetPath = (path: string): string => {\r\n  // Remove leading slash if present\r\n  const cleanPath = path.startsWith('/') ? path.slice(1) : path;\r\n\r\n  // Use import.meta.env.BASE_URL which Vite provides automatically\r\n  return `${import.meta.env.BASE_URL}${cleanPath}`;\r\n};\r\n\r\n/**\r\n * Get image path specifically\r\n */\r\nexport const getImagePath = (filename: string): string => {\r\n  return getAssetPath(`images/${filename}`);\r\n};\r\n\r\n/**\r\n * Get profile image paths with WebP support\r\n */\r\nexport const getProfileImagePaths = () => {\r\n  return {\r\n    webp: getImagePath('tarcisio_bispo.webp'),\r\n    png: getImagePath('tarcisio_bispo.png')\r\n  };\r\n};\r\n\r\n/**\r\n * Get IxDF logo paths\r\n */\r\nexport const getIxDFLogoPaths = () => {\r\n  return {\r\n    dark: getImagePath('ixdf-symbol-dark.png'),\r\n    white: getImagePath('ixdf-symbol-white.png')\r\n  };\r\n};\r\n", "import React from 'react';\r\nimport { getIxDFLogoPaths } from '@/utils/assetPaths';\r\n\r\ninterface IxDFLogoProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  showText?: boolean;\r\n  layout?: 'horizontal' | 'vertical';\r\n  className?: string;\r\n}\r\n\r\nconst IxDFLogo: React.FC<IxDFLogoProps> = ({\r\n  size = 'md',\r\n  showText = true,\r\n  layout = 'horizontal',\r\n  className = ''\r\n}) => {\r\n  const logoPaths = getIxDFLogoPaths();\r\n  const sizeClasses = {\r\n    sm: 'h-10',    // 40px - forçando maior para testar\r\n    md: 'h-12',    // 48px - médio\r\n    lg: 'h-16'     // 64px - grande\r\n  };\r\n\r\n  const textSizeClasses = {\r\n    sm: 'text-xs',\r\n    md: 'text-sm',\r\n    lg: 'text-base'\r\n  };\r\n\r\n  const containerClasses = layout === 'vertical'\r\n    ? 'flex flex-col items-center gap-3'\r\n    : 'flex items-center gap-6';\r\n\r\n  return (\r\n    <div className={`${containerClasses} ${className}`}>\r\n      {/* Logo IxDF - Usando imagens fornecidas */}\r\n      <div className=\"relative\">\r\n        {/* Logo para Light Mode - usa imagem dark */}\r\n        <img\r\n          src={logoPaths.dark}\r\n          alt=\"Interaction Design Foundation Logo\"\r\n          className={`${sizeClasses[size]} w-auto dark:hidden transition-opacity duration-300 hover:opacity-80`}\r\n          width={size === 'sm' ? '40' : size === 'md' ? '48' : '64'}\r\n          height={size === 'sm' ? '40' : size === 'md' ? '48' : '64'}\r\n          style={{\r\n            height: size === 'sm' ? '40px' : size === 'md' ? '48px' : '64px',\r\n            width: size === 'sm' ? '40px' : size === 'md' ? '48px' : '64px',\r\n            maxHeight: 'none',\r\n            maxWidth: 'none'\r\n          }}\r\n          loading=\"lazy\"\r\n          decoding=\"async\"\r\n        />\r\n\r\n        {/* Logo para Dark Mode - usa imagem white */}\r\n        <img\r\n          src={logoPaths.white}\r\n          alt=\"Interaction Design Foundation Logo\"\r\n          className={`${sizeClasses[size]} w-auto hidden dark:block transition-opacity duration-300 hover:opacity-80`}\r\n          width={size === 'sm' ? '40' : size === 'md' ? '48' : '64'}\r\n          height={size === 'sm' ? '40' : size === 'md' ? '48' : '64'}\r\n          style={{\r\n            height: size === 'sm' ? '40px' : size === 'md' ? '48px' : '64px',\r\n            width: size === 'sm' ? '40px' : size === 'md' ? '48px' : '64px',\r\n            maxHeight: 'none',\r\n            maxWidth: 'none'\r\n          }}\r\n          loading=\"lazy\"\r\n          decoding=\"async\"\r\n        />\r\n      </div>\r\n\r\n      {/* Texto explicativo */}\r\n      {showText && (\r\n        <div className=\"flex flex-col\">\r\n          <span\r\n            className={`font-semibold ${textSizeClasses[size]}`}\r\n            style={{ color: 'var(--color-text)' }}\r\n          >\r\n            IxDF\r\n          </span>\r\n          <span\r\n            className={`${textSizeClasses[size]} opacity-75`}\r\n            style={{ color: 'var(--color-muted)' }}\r\n          >\r\n            Interaction Design Foundation\r\n          </span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default IxDFLogo;\r\n", "import e,{useRef as t,useState as r,useEffect as n,forwardRef as o,memo as a}from\"react\";function i(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function u(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,u)}c((n=n.apply(e,t||[])).next())}))}function u(e,t){var r,n,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:u(0),throw:u(1),return:u(2)},\"function\"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(a){return function(u){return function(a){if(r)throw new TypeError(\"Generator is already executing.\");for(;i;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,n=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}function c(e){var t=\"function\"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&\"number\"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")}function l(e,t){var r=\"function\"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i}function s(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function f(e,t,r,n,o){for(var a=[],f=5;f<arguments.length;f++)a[f-5]=arguments[f];return i(this,void 0,void 0,(function(){var i,f,h,y,v,b;return u(this,(function(u){switch(u.label){case 0:u.trys.push([0,12,13,14]),i=c(a),f=i.next(),u.label=1;case 1:if(f.done)return[3,11];switch(h=f.value,typeof h){case\"string\":return[3,2];case\"number\":return[3,4];case\"function\":return[3,6]}return[3,8];case 2:return[4,d(e,t,h,r,n,o)];case 3:return u.sent(),[3,10];case 4:return[4,p(h)];case 5:return u.sent(),[3,10];case 6:return[4,h.apply(void 0,s([e,t,r,n,o],l(a),!1))];case 7:return u.sent(),[3,10];case 8:return[4,h];case 9:u.sent(),u.label=10;case 10:return f=i.next(),[3,1];case 11:return[3,14];case 12:return y=u.sent(),v={error:y},[3,14];case 13:try{f&&!f.done&&(b=i.return)&&b.call(i)}finally{if(v)throw v.error}return[7];case 14:return[2]}}))}))}function d(e,t,r,n,o,a){return i(this,void 0,void 0,(function(){var i,c;return u(this,(function(u){switch(u.label){case 0:return i=e.textContent||\"\",c=function(e,t){var r=l(t).slice(0);return s(s([],l(e),!1),[NaN],!1).findIndex((function(e,t){return r[t]!==e}))}(i,r),[4,h(e,s(s([],l(v(i,t,c)),!1),l(y(r,t,c)),!1),n,o,a)];case 1:return u.sent(),[2]}}))}))}function p(e){return i(this,void 0,void 0,(function(){return u(this,(function(t){switch(t.label){case 0:return[4,new Promise((function(t){return setTimeout(t,e)}))];case 1:return t.sent(),[2]}}))}))}function h(e,t,r,n,o){return i(this,void 0,void 0,(function(){var a,i,s,f,d,h,y,v,b,m,w,g,x;return u(this,(function(S){switch(S.label){case 0:if(a=t,o){for(i=0,s=1;s<t.length;s++)if(f=l([t[s-1],t[s]],2),d=f[0],(h=f[1]).length>d.length||\"\"===h){i=s;break}a=t.slice(i,t.length)}S.label=1;case 1:S.trys.push([1,6,7,8]),y=c(function(e){var t,r,n,o,a,i,l;return u(this,(function(s){switch(s.label){case 0:t=function(e){return u(this,(function(t){switch(t.label){case 0:return[4,{op:function(t){return requestAnimationFrame((function(){return t.textContent=e}))},opCode:function(t){var r=t.textContent||\"\";return\"\"===e||r.length>e.length?\"DELETE\":\"WRITING\"}}];case 1:return t.sent(),[2]}}))},s.label=1;case 1:s.trys.push([1,6,7,8]),r=c(e),n=r.next(),s.label=2;case 2:return n.done?[3,5]:(o=n.value,[5,t(o)]);case 3:s.sent(),s.label=4;case 4:return n=r.next(),[3,2];case 5:return[3,8];case 6:return a=s.sent(),i={error:a},[3,8];case 7:try{n&&!n.done&&(l=r.return)&&l.call(r)}finally{if(i)throw i.error}return[7];case 8:return[2]}}))}(a)),v=y.next(),S.label=2;case 2:return v.done?[3,5]:(b=v.value,m=\"WRITING\"===b.opCode(e)?r+r*(Math.random()-.5):n+n*(Math.random()-.5),b.op(e),[4,p(m)]);case 3:S.sent(),S.label=4;case 4:return v=y.next(),[3,2];case 5:return[3,8];case 6:return w=S.sent(),g={error:w},[3,8];case 7:try{v&&!v.done&&(x=y.return)&&x.call(y)}finally{if(g)throw g.error}return[7];case 8:return[2]}}))}))}function y(e,t,r){var n,o;return void 0===r&&(r=0),u(this,(function(a){switch(a.label){case 0:n=t(e),o=n.length,a.label=1;case 1:return r<o?[4,n.slice(0,++r).join(\"\")]:[3,3];case 2:return a.sent(),[3,1];case 3:return[2]}}))}function v(e,t,r){var n,o;return void 0===r&&(r=0),u(this,(function(a){switch(a.label){case 0:n=t(e),o=n.length,a.label=1;case 1:return o>r?[4,n.slice(0,--o).join(\"\")]:[3,3];case 2:return a.sent(),[3,1];case 3:return[2]}}))}var b=\"index-module_type__E-SaG\";!function(e,t){void 0===t&&(t={});var r=t.insertAt;if(e&&\"undefined\"!=typeof document){var n=document.head||document.getElementsByTagName(\"head\")[0],o=document.createElement(\"style\");o.type=\"text/css\",\"top\"===r&&n.firstChild?n.insertBefore(o,n.firstChild):n.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}}(\".index-module_type__E-SaG::after {\\n  content: '|';\\n  animation: index-module_cursor__PQg0P 1.1s infinite step-start;\\n}\\n\\n@keyframes index-module_cursor__PQg0P {\\n  50% {\\n    opacity: 0;\\n  }\\n}\\n\");var m=a(o((function(o,a){var i=o.sequence,u=o.repeat,c=o.className,d=o.speed,p=void 0===d?40:d,h=o.deletionSpeed,y=o.omitDeletionAnimation,v=void 0!==y&&y,m=o.preRenderFirstString,w=void 0!==m&&m,g=o.wrapper,x=void 0===g?\"span\":g,S=o.splitter,E=void 0===S?function(e){return s([],l(e),!1)}:S,_=o.cursor,k=void 0===_||_,O=o.style,T=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&\"function\"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}(o,[\"sequence\",\"repeat\",\"className\",\"speed\",\"deletionSpeed\",\"omitDeletionAnimation\",\"preRenderFirstString\",\"wrapper\",\"splitter\",\"cursor\",\"style\"]),A=T[\"aria-label\"],C=T[\"aria-hidden\"],N=T.role;h||(h=p);var P=new Array(2).fill(40);[p,h].forEach((function(e,t){switch(typeof e){case\"number\":P[t]=Math.abs(e-100);break;case\"object\":var r=e.type,n=e.value;if(\"number\"!=typeof n)break;if(\"keyStrokeDelayInMs\"===r)P[t]=n}}));var j,I,G,D,M,R,q=P[0],F=P[1],B=function(e,r){void 0===r&&(r=null);var o=t(r);return n((function(){e&&(\"function\"==typeof e?e(o.current):e.current=o.current)}),[e]),o}(a),Q=b;j=c?\"\".concat(k?Q+\" \":\"\").concat(c):k?Q:\"\",I=t((function(){var e,t=i;u===1/0?e=f:\"number\"==typeof u&&(t=Array(1+u).fill(i).flat());var r=e?s(s([],l(t),!1),[e],!1):s([],l(t),!1);return f.apply(void 0,s([B.current,E,q,F,v],l(r),!1)),function(){B.current}})),G=t(),D=t(!1),M=t(!1),R=l(r(0),2)[1],D.current&&(M.current=!0),n((function(){return D.current||(G.current=I.current(),D.current=!0),R((function(e){return e+1})),function(){M.current&&G.current&&G.current()}}),[]);var W=x,L=w?i.find((function(e){return\"string\"==typeof e}))||\"\":null;return e.createElement(W,{\"aria-hidden\":C,\"aria-label\":A,role:N,style:O,className:j,children:A?e.createElement(\"span\",{\"aria-hidden\":\"true\",ref:B,children:L}):L,ref:A?void 0:B})})),(function(e,t){return!0}));export{m as TypeAnimation};\n", "import React from 'react';\r\nimport { TypeAnimation } from 'react-type-animation';\r\n\r\ninterface TypewriterTextProps {\r\n  sequence: (string | number)[];\r\n  wrapper?: keyof JSX.IntrinsicElements;\r\n  speed?: number;\r\n  className?: string;\r\n  repeat?: number;\r\n  cursor?: boolean;\r\n  preRenderFirstString?: boolean;\r\n}\r\n\r\nexport const TypewriterText: React.FC<TypewriterTextProps> = ({\r\n  sequence,\r\n  wrapper = 'span',\r\n  speed = 50,\r\n  className = '',\r\n  repeat = Infinity,\r\n  cursor = true,\r\n  preRenderFirstString = true\r\n}) => {\r\n  return (\r\n    <TypeAnimation\r\n      sequence={sequence}\r\n      wrapper={wrapper}\r\n      speed={speed}\r\n      className={className}\r\n      repeat={repeat}\r\n      cursor={cursor}\r\n      preRenderFirstString={preRenderFirstString}\r\n      style={{ display: 'inline-block' }}\r\n      aria-live=\"polite\"\r\n    />\r\n  );\r\n};\r\n\r\nexport default TypewriterText;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n/**\r\n * Critical Bio Component - Now with i18n support\r\n * Updated to support multiple languages while maintaining performance\r\n */\r\n\r\ninterface CriticalBioProps {\r\n  className?: string;\r\n}\r\n\r\nconst CriticalBio: React.FC<CriticalBioProps> = ({ className = '' }) => {\r\n  const { t, i18n } = useTranslation();\r\n  const [forceUpdate, setForceUpdate] = React.useState(0);\r\n\r\n  // Forçar re-render quando idioma mudar\r\n  React.useEffect(() => {\r\n    setForceUpdate(prev => prev + 1);\r\n  }, [i18n.language]);\r\n\r\n  // Traduções diretas baseadas no idioma atual (seguindo a lógica existente)\r\n  const getBioText = () => {\r\n    // Primeiro tenta usar a tradução do i18n\r\n    const bioFromI18n = t('profile.bio');\r\n\r\n    // Se a tradução carregou corretamente (não retorna a chave), usa ela\r\n    if (bioFromI18n && bioFromI18n !== 'profile.bio') {\r\n      return bioFromI18n;\r\n    }\r\n\r\n    // Fallback direto por idioma\r\n    switch (i18n.language) {\r\n      case 'en-US':\r\n        return \"I am a UX/Product Designer with strong expertise in designing digital products focused on user experience, conversion and business impact. With a background in Digital Marketing, SEO and AI, I integrate strategy, design and usability in continuous improvement and innovation processes.\";\r\n      case 'es-ES':\r\n        return \"Soy UX/Product Designer con amplia experiencia en el diseño de productos digitales enfocados en experiencia del usuario, conversión e impacto empresarial. Con formación en Marketing Digital, SEO e IA, integro estrategia, diseño y usabilidad en procesos continuos de mejora e innovación.\";\r\n      default:\r\n        return \"Sou UX/Product Designer com forte atuação no design de produtos digitais focados em experiência do usuário, conversão e impacto de negócio. Com background em Marketing Digital, SEO e IA, integro estratégia, design e usabilidade em processos contínuos de melhoria e inovação.\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-8 ${className}`}>\r\n      <p className=\"critical-bio-text\">\r\n        {getBioText()}\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CriticalBio;\r\n", "import React, { useState, useRef, useEffect } from 'react';\n\ninterface CLSOptimizedImageProps {\n  src: string;\n  alt: string;\n  width: number;\n  height: number;\n  className?: string;\n  loading?: 'lazy' | 'eager';\n  priority?: boolean;\n  webpSrc?: string;\n  placeholder?: string;\n  onLoad?: () => void;\n  onError?: () => void;\n}\n\n/**\n * CLS Optimized Image Component\n * Prevents Cumulative Layout Shift by enforcing explicit dimensions\n * and providing proper loading states\n */\nconst CLSOptimizedImage: React.FC<CLSOptimizedImageProps> = ({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  loading = 'lazy',\n  priority = false,\n  webpSrc,\n  placeholder,\n  onLoad,\n  onError\n}) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [isInView, setIsInView] = useState(priority || loading === 'eager');\n  const imgRef = useRef<HTMLImageElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // Intersection Observer for lazy loading\n  useEffect(() => {\n    if (loading === 'lazy' && !priority && containerRef.current) {\n      const observer = new IntersectionObserver(\n        (entries) => {\n          const [entry] = entries;\n          if (entry.isIntersecting) {\n            setIsInView(true);\n            observer.disconnect();\n          }\n        },\n        {\n          rootMargin: '50px 0px', // Start loading 50px before entering viewport\n          threshold: 0.01\n        }\n      );\n\n      observer.observe(containerRef.current);\n\n      return () => observer.disconnect();\n    }\n  }, [loading, priority]);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    onLoad?.();\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    onError?.();\n  };\n\n  // Calculate aspect ratio for responsive behavior\n  const aspectRatio = (height / width) * 100;\n\n  // Container styles to prevent layout shift\n  const containerStyle: React.CSSProperties = {\n    width: '100%',\n    maxWidth: `${width}px`,\n    position: 'relative',\n    overflow: 'hidden',\n    // Maintain aspect ratio\n    paddingBottom: `${aspectRatio}%`,\n    height: 0\n  };\n\n  // Image styles\n  const imageStyle: React.CSSProperties = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transition: 'opacity 0.3s ease',\n    opacity: imageLoaded ? 1 : 0\n  };\n\n  // Placeholder styles\n  const placeholderStyle: React.CSSProperties = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    backgroundColor: '#f3f4f6',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    opacity: imageLoaded ? 0 : 1,\n    transition: 'opacity 0.3s ease'\n  };\n\n  return (\n    <div\n      ref={containerRef}\n      className={`cls-optimized-image ${className}`}\n      style={containerStyle}\n    >\n      {/* Placeholder */}\n      <div style={placeholderStyle}>\n        {placeholder ? (\n          <img\n            src={placeholder}\n            alt=\"\"\n            style={{\n              width: '100%',\n              height: '100%',\n              objectFit: 'cover',\n              filter: 'blur(5px)',\n              transform: 'scale(1.1)'\n            }}\n          />\n        ) : (\n          <div\n            style={{\n              width: '40px',\n              height: '40px',\n              backgroundColor: '#e5e7eb',\n              borderRadius: '4px',\n              animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n            }}\n          />\n        )}\n      </div>\n\n      {/* Main Image */}\n      {isInView && (\n        <picture>\n          {webpSrc && (\n            <source srcSet={webpSrc} type=\"image/webp\" />\n          )}\n          <img\n            ref={imgRef}\n            src={src}\n            alt={alt}\n            width={width}\n            height={height}\n            loading={priority ? 'eager' : loading}\n            decoding={priority ? 'sync' : 'async'}\n            {...(priority && { fetchpriority: 'high' as any })}\n            style={imageStyle}\n            onLoad={handleImageLoad}\n            onError={handleImageError}\n            // Prevent layout shift with explicit dimensions\n            sizes={`(max-width: ${width}px) 100vw, ${width}px`}\n          />\n        </picture>\n      )}\n\n      {/* Error state */}\n      {imageError && (\n        <div\n          style={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            backgroundColor: '#fef2f2',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: '#dc2626',\n            fontSize: '14px',\n            textAlign: 'center',\n            padding: '8px'\n          }}\n        >\n          Failed to load image\n        </div>\n      )}\n    </div>\n  );\n};\n\n// CSS for animations (injected via JavaScript)\nif (typeof document !== 'undefined') {\n  const style = document.createElement('style');\n  style.textContent = `\n    @keyframes pulse {\n      0%, 100% {\n        opacity: 1;\n      }\n      50% {\n        opacity: 0.5;\n      }\n    }\n\n    .cls-optimized-image {\n      contain: layout style paint;\n      content-visibility: visible;\n    }\n\n    .cls-optimized-image img {\n      contain: layout style paint;\n      will-change: opacity;\n    }\n  `;\n\n  if (!document.head.querySelector('[data-cls-optimized-styles]')) {\n    style.setAttribute('data-cls-optimized-styles', 'true');\n    document.head.appendChild(style);\n  }\n}\n\nexport default CLSOptimizedImage;\n", "import { useEffect, useState } from 'react';\n\n/**\n * Hook to optimize LCP (Largest Contentful Paint)\n * Delays non-critical animations until after LCP is complete\n */\n\ninterface LCPOptimizationOptions {\n  delay?: number;\n  enableAnimations?: boolean;\n}\n\nexport const useLCPOptimization = (options: LCPOptimizationOptions = {}) => {\n  const { delay = 100, enableAnimations = true } = options;\n  const [isLCPComplete, setIsLCPComplete] = useState(false);\n  const [shouldAnimate, setShouldAnimate] = useState(false);\n\n  useEffect(() => {\n    // Check if PerformanceObserver is available\n    if ('PerformanceObserver' in window) {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lcpEntry = entries[entries.length - 1] as PerformanceEntry & {\n          renderTime?: number;\n          loadTime?: number;\n        };\n\n        if (lcpEntry) {\n          // LCP detected, mark as complete\n          setIsLCPComplete(true);\n          observer.disconnect();\n          \n          // Start animations after a small delay\n          if (enableAnimations) {\n            setTimeout(() => {\n              setShouldAnimate(true);\n            }, delay);\n          }\n        }\n      });\n\n      try {\n        observer.observe({ entryTypes: ['largest-contentful-paint'] });\n      } catch (error) {\n        console.warn('LCP observation not supported:', error);\n        // Fallback: assume LCP is complete after a timeout\n        setTimeout(() => {\n          setIsLCPComplete(true);\n          if (enableAnimations) {\n            setShouldAnimate(true);\n          }\n        }, 1000);\n      }\n\n      // Cleanup observer on unmount\n      return () => {\n        observer.disconnect();\n      };\n    } else {\n      // Fallback for browsers without PerformanceObserver\n      setTimeout(() => {\n        setIsLCPComplete(true);\n        if (enableAnimations) {\n          setShouldAnimate(true);\n        }\n      }, 1000);\n    }\n  }, [delay, enableAnimations]);\n\n  // Additional fallback: ensure animations start even if LCP detection fails\n  useEffect(() => {\n    const fallbackTimer = setTimeout(() => {\n      setIsLCPComplete(true);\n      if (enableAnimations) {\n        setShouldAnimate(true);\n      }\n    }, 2000); // 2 second fallback\n\n    return () => clearTimeout(fallbackTimer);\n  }, [enableAnimations]);\n\n  return {\n    isLCPComplete,\n    shouldAnimate: enableAnimations ? shouldAnimate : false,\n  };\n};\n\n/**\n * Hook specifically for TypewriterText optimization\n * Ensures the first string is rendered immediately for LCP\n */\nexport const useTypewriterLCPOptimization = () => {\n  const [showTypewriter, setShowTypewriter] = useState(false);\n  const { isLCPComplete } = useLCPOptimization({ delay: 500 });\n\n  useEffect(() => {\n    if (isLCPComplete) {\n      // Small delay to ensure LCP measurement is complete\n      const timer = setTimeout(() => {\n        setShowTypewriter(true);\n      }, 200);\n\n      return () => clearTimeout(timer);\n    }\n  }, [isLCPComplete]);\n\n  return {\n    showTypewriter,\n    isLCPComplete,\n  };\n};\n\nexport default useLCPOptimization;\n", "import React, { Suspense } from 'react';\r\nimport { OptimizedMotion } from '@/components/LazyMotion';\r\nimport { CTAButton } from '@/components/ui/buttons';\r\nimport IxDFLogo from '@/components/ui/IxDFLogo';\r\nimport TypewriterText from '@/components/ui/TypewriterText';\r\nimport CriticalBio from '@/components/CriticalBio';\r\nimport CLSOptimizedImage from '@/components/CLSOptimizedImage';\r\nimport { Download, Linkedin, ArrowRight, MapPin, Mail, Phone, MessageCircle } from 'lucide-react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { getProfileImagePaths } from '@/utils/assetPaths';\r\nimport { useTypewriterLCPOptimization } from '@/hooks/useLCPOptimization';\r\n// CSS já incluído no sistema modular\r\n\r\n// Ícone do WhatsApp\r\nconst WhatsAppIcon = (props: React.SVGProps<SVGSVGElement>) => (\r\n  <svg {...props} viewBox=\"0 0 24 24\" fill=\"none\">\r\n    <path\r\n      d=\"M16.7 13.6c-.3-.2-1.7-.8-2-1s-.5-.2-.7.1c-.2.3-.8 1-.9 1.1-.2.2-.3.2-.6.1-.3-.2-1.2-.4-2.2-1.3-.8-.7-1.3-1.6-1.5-1.9-.2-.3 0-.4.1-.6.1-.1.2-.3.3-.4.1-.1.1-.2.2-.3.1-.1.1-.2.2-.3.1-.2 0-.4 0-.6s-.7-1.7-.9-2.3c-.2-.6-.4-.5-.6-.5h-.5c-.2 0-.5.1-.7.3-.2.2-.7.7-.7 1.7 0 1 .7 2.1 1.1 2.6.4.5 2.1 2.8 5.1 3.7.7.2 1.2.3 1.6.2.5-.1 1.5-.6 1.7-1.2.2-.6.2-1.1.1-1.2z\"\r\n      fill=\"white\"\r\n    />\r\n    <path\r\n      d=\"M12 2C6.5 2 2 6.5 2 12c0 1.9.5 3.7 1.5 5.3L2 22l4.8-1.3C8.3 21.5 10.1 22 12 22c5.5 0 10-4.5 10-10S17.5 2 12 2zm0 18c-1.7 0-3.3-.4-4.7-1.2l-.3-.2-2.8.7.7-2.7-.2-.3C4.4 15.3 4 13.7 4 12c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8z\"\r\n      fill=\"white\"\r\n    />\r\n  </svg>\r\n);\r\n\r\n\r\n\r\ninterface ProfileProps {\r\n  name: string;\r\n}\r\n\r\nconst Profile: React.FC<ProfileProps> = ({ name }) => {\r\n  const { t, i18n } = useTranslation();\r\n  const profileImages = getProfileImagePaths();\r\n  const { showTypewriter, isLCPComplete } = useTypewriterLCPOptimization();\r\n\r\n  // Traduções funcionam corretamente com I18nProvider\r\n\r\n  return (\r\n    <section className=\"min-h-screen flex flex-col justify-center py-16 relative\" aria-labelledby=\"profile-title\">\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-16 items-center justify-center max-w-7xl mx-auto px-6\">\r\n\r\n        {/* CARD DE PERFIL PREMIUM - Melhor que LinkedIn */}\r\n        <div\r\n          className=\"lg:col-span-4 flex justify-center relative z-10\"\r\n          style={{ overflow: 'visible' }}\r\n        >\r\n          <div className=\"relative group p-8\">\r\n            {/* Card Container - ESPAÇAMENTOS HEURÍSTICOS PERFEITOS */}\r\n            <div className=\"profile-card relative rounded-3xl px-6 py-10 max-w-sm w-full transition-all duration-500\">\r\n\r\n              {/* Background Gradient Sutil */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/50 dark:from-blue-900/20 dark:via-transparent dark:to-purple-900/20 rounded-3xl\"></div>\r\n\r\n              {/* ESTRUTURA COMPLETA COM RESPIRO CORRETO */}\r\n              <div className=\"relative z-10 flex flex-col items-center space-y-6\">\r\n\r\n                {/* SEÇÃO 1: Foto de Perfil */}\r\n                <div className=\"relative\">\r\n                  {/* Anel Animado */}\r\n                  <div className=\"profile-ring absolute -inset-1 rounded-full opacity-75 group-hover:opacity-100\"></div>\r\n\r\n                  {/* Container da Foto */}\r\n                  <div className=\"relative w-32 h-32 rounded-full overflow-hidden bg-white p-1 shadow-xl\">\r\n                    <CLSOptimizedImage\r\n                      src={profileImages.png}\r\n                      webpSrc={profileImages.webp}\r\n                      alt={t('alts.profile.photo')}\r\n                      width={128}\r\n                      height={128}\r\n                      priority={true}\r\n                      className=\"profile-image-hover rounded-full\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Status Online */}\r\n                  <div className=\"status-online absolute bottom-2 right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-lg\"></div>\r\n                </div>\r\n\r\n                {/* SEÇÃO 2: Informações Pessoais */}\r\n                <div className=\"text-center space-y-3 w-full\">\r\n                  {/* Nome - FORÇAR UMA LINHA SÓ COM TAMANHO RESPONSIVO */}\r\n                  <h2 className=\"text-base sm:text-lg font-bold text-gray-900 dark:text-white leading-tight whitespace-nowrap\">\r\n                    {t('profile.name')}\r\n                  </h2>\r\n\r\n                  {/* Título Profissional - WCAG 2.2 AA Compliant */}\r\n                  <p className=\"text-blue-700 dark:text-blue-300 font-medium text-sm leading-relaxed\">\r\n                    UX/Product Designer\r\n                  </p>\r\n\r\n                  {/* Localização - WCAG 2.2 AA Compliant */}\r\n                  <p className=\"text-gray-700 dark:text-gray-300 text-sm leading-relaxed\">\r\n                    Campinas, São Paulo\r\n                  </p>\r\n\r\n                  {/* Telefone - WCAG 2.2 AA Compliant */}\r\n                  <p className=\"text-gray-700 dark:text-gray-300 text-sm leading-relaxed\">\r\n                    +55 19 9 9013-7380\r\n                  </p>\r\n                </div>\r\n\r\n                {/* SEÇÃO 3: IxDF Badge - WCAG 2.2 AA Compliant */}\r\n                <div className=\"flex items-center justify-center gap-3 text-xs text-gray-800 dark:text-gray-200 px-4 py-2 bg-gray-50/50 dark:bg-gray-800/30 rounded-lg border border-gray-200/50 dark:border-gray-700/50\">\r\n                  <div className=\"w-5 h-5 flex-shrink-0 flex items-center justify-center\">\r\n                    <IxDFLogo\r\n                      size=\"sm\"\r\n                      showText={false}\r\n                      className=\"w-full h-full object-contain\"\r\n                    />\r\n                  </div>\r\n                  {/* Espaçamento heuristicamente correto entre ícone e texto */}\r\n                  <span className=\"whitespace-nowrap font-medium ml-1\">IxDF - Interaction Design Foundation</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bio Section Moderna - LCP Optimized */}\r\n        <div className=\"lg:col-span-8 flex flex-col justify-center\">\r\n          {/* Título Principal - Renderização Imediata para LCP */}\r\n          <div className=\"mb-8\">\r\n            <h1\r\n              id=\"profile-title\"\r\n              className=\"text-4xl lg:text-6xl font-bold mb-4 leading-tight\"\r\n              style={{\r\n                contentVisibility: 'visible',\r\n                containIntrinsicSize: 'auto 200px',\r\n                willChange: 'auto',\r\n                transform: 'translateZ(0)',\r\n                contain: 'layout style paint'\r\n              }}\r\n            >\r\n              <span className=\"text-gray-900 dark:text-white\">{t('profile.hero.greeting')} </span>\r\n              <br />\r\n              <span className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent\">\r\n                {showTypewriter ? (\r\n                  <Suspense fallback={t('profile.hero.roles.uxDesigner')}>\r\n                    <TypewriterText\r\n                      key={i18n.language} // Força re-render quando idioma muda\r\n                      sequence={[\r\n                        t('profile.hero.roles.uxDesigner'),\r\n                        2000,\r\n                        t('profile.hero.roles.productDesigner'),\r\n                        2000,\r\n                        t('profile.hero.roles.designStrategist'),\r\n                        2000,\r\n                        t('profile.hero.roles.interactionDesigner'),\r\n                        2000\r\n                      ]}\r\n                      className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent\"\r\n                      speed={50}\r\n                      repeat={0}\r\n                      preRenderFirstString={true}\r\n                    />\r\n                  </Suspense>\r\n                ) : (\r\n                  t('profile.hero.roles.uxDesigner')\r\n                )}\r\n              </span>\r\n            </h1>\r\n\r\n            {/* Linha Decorativa - Renderização Imediata */}\r\n            <div className=\"h-1 w-[120px] bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6\"></div>\r\n          </div>\r\n\r\n          {/* Bio Text - Critical for LCP */}\r\n          <CriticalBio key={i18n.language} />\r\n\r\n          {/* CTAs Principais */}\r\n          <div className=\"flex flex-col sm:flex-row gap-4\">\r\n            {/* CTA Principal - Vamos Conversar */}\r\n            <CTAButton\r\n              href=\"https://wa.me/19990137380\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              variant=\"primary\"\r\n              size=\"lg\"\r\n              icon={MessageCircle}\r\n              iconPosition=\"left\"\r\n              className=\"bg-gradient-to-r from-[#25D366] to-[#1ebe5d] hover:from-[#1ebe5d] hover:to-[#25D366] focus:ring-green-400\"\r\n            >\r\n              {t('profile.letsChat')}\r\n            </CTAButton>\r\n\r\n            {/* Download CV */}\r\n            <CTAButton\r\n              href=\"https://drive.google.com/file/d/1NgQorqxUXbGKUaDruLfflxB4_6GhJyo8/view?usp=drive_link\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              variant=\"ghost\"\r\n              size=\"lg\"\r\n              icon={Download}\r\n              iconPosition=\"left\"\r\n              className=\"border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 hover:text-blue-600 dark:hover:text-blue-400\"\r\n            >\r\n              {t('profile.downloadCV')}\r\n            </CTAButton>\r\n\r\n            {/* LinkedIn */}\r\n            <CTAButton\r\n              href=\"https://www.linkedin.com/in/tarcisiobispouxdesigner/\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              variant=\"ghost\"\r\n              size=\"lg\"\r\n              icon={Linkedin}\r\n              iconPosition=\"left\"\r\n              className=\"border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 hover:text-blue-600 dark:hover:text-blue-400\"\r\n            >\r\n              {t('profile.linkedin')}\r\n            </CTAButton>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n", "import React from 'react';\r\n\r\nconst SkipLink: React.FC = () => {\r\n  return (\r\n    <a\r\n      href=\"#main-content\"\r\n      className=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-[var(--color-primary)] focus:text-white focus:rounded-md focus:shadow-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-offset-2 transition-all duration-200\"\r\n      tabIndex={0}\r\n    >\r\n      Pular para conteúdo principal\r\n    </a>\r\n  );\r\n};\r\n\r\nexport default SkipLink;\r\n", "import React from 'react';\r\nimport { Helmet } from 'react-helmet-async';\r\nimport { SecureValidation } from '@/utils/secureValidation';\r\n\r\ninterface SEOProps {\r\n  title?: string;\r\n  description?: string;\r\n  keywords?: string;\r\n  image?: string;\r\n  url?: string;\r\n  type?: string;\r\n  author?: string;\r\n  publishedTime?: string;\r\n  modifiedTime?: string;\r\n  section?: string;\r\n  structuredData?: object;\r\n}\r\n\r\nconst SEO: React.FC<SEOProps> = ({\r\n  title = \"Tarcisio Bispo | UX/Product Designer\",\r\n  description = \"Portfólio de Tarcisio Bispo de Araujo - UX/Product Designer com foco em estratégia, impacto e experiência do usuário. Especialista em design thinking, prototipagem e pesquisa de usuário.\",\r\n  keywords = \"UX Designer, Product Designer, UI/UX, Design Thinking, Prototipagem, Pesquisa de Usuário, Portfolio, Tarcisio Bispo\",\r\n  image = \"https://tarcisiobispo.github.io/portfolio/og-image.jpg\",\r\n  url = \"https://tarcisiobispo.github.io/portfolio/\",\r\n  type = \"website\",\r\n  author = \"Tarcisio Bispo de Araujo\",\r\n  publishedTime,\r\n  modifiedTime,\r\n  section,\r\n  structuredData\r\n}) => {\r\n  const siteUrl = \"https://tarcisiobispo.github.io/portfolio\";\r\n\r\n  // Usar validação segura de URLs\r\n  const fullUrl = url.startsWith('http') && SecureValidation.validateUrl(url) ? url : `${siteUrl}${url}`;\r\n  const fullImageUrl = image.startsWith('http') && SecureValidation.validateUrl(image) ? image : `${siteUrl}${image}`;\r\n\r\n  return (\r\n    <Helmet>\r\n      {/* Basic Meta Tags */}\r\n      <title>{title}</title>\r\n      <meta name=\"description\" content={description} />\r\n      <meta name=\"keywords\" content={keywords} />\r\n      <meta name=\"author\" content={author} />\r\n      <meta name=\"robots\" content=\"index, follow\" />\r\n      <meta name=\"language\" content=\"pt-BR\" />\r\n      <meta name=\"revisit-after\" content=\"7 days\" />\r\n\r\n      {/* Canonical URL */}\r\n      <link rel=\"canonical\" href={fullUrl} />\r\n\r\n      {/* Open Graph / Facebook */}\r\n      <meta property=\"og:type\" content={type} />\r\n      <meta property=\"og:title\" content={title} />\r\n      <meta property=\"og:description\" content={description} />\r\n      <meta property=\"og:image\" content={fullImageUrl} />\r\n      <meta property=\"og:image:width\" content=\"1200\" />\r\n      <meta property=\"og:image:height\" content=\"630\" />\r\n      <meta property=\"og:image:type\" content=\"image/jpeg\" />\r\n      <meta property=\"og:url\" content={fullUrl} />\r\n      <meta property=\"og:site_name\" content=\"Portfólio Tarcisio Bispo\" />\r\n      <meta property=\"og:locale\" content=\"pt_BR\" />\r\n\r\n      {/* Article specific (for blog posts/projects) */}\r\n      {type === 'article' && (\r\n        <>\r\n          <meta property=\"article:author\" content={author} />\r\n          {section && <meta property=\"article:section\" content={section} />}\r\n          {publishedTime && <meta property=\"article:published_time\" content={publishedTime} />}\r\n          {modifiedTime && <meta property=\"article:modified_time\" content={modifiedTime} />}\r\n        </>\r\n      )}\r\n\r\n      {/* Twitter Card */}\r\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n      <meta name=\"twitter:title\" content={title} />\r\n      <meta name=\"twitter:description\" content={description} />\r\n      <meta name=\"twitter:image\" content={fullImageUrl} />\r\n      <meta name=\"twitter:creator\" content=\"@tarcisiobispo\" />\r\n      <meta name=\"twitter:site\" content=\"@tarcisiobispo\" />\r\n\r\n      {/* Additional Meta Tags */}\r\n      <meta name=\"theme-color\" content=\"#1e3a8a\" />\r\n      <meta name=\"msapplication-TileColor\" content=\"#1e3a8a\" />\r\n\r\n      {/* Structured Data */}\r\n      {structuredData && (\r\n        <script type=\"application/ld+json\">\r\n          {JSON.stringify(structuredData)}\r\n        </script>\r\n      )}\r\n    </Helmet>\r\n  );\r\n};\r\n\r\nexport default SEO;\r\n", "// Schema.org Structured Data for Portfolio\r\n\r\nexport const personSchema = {\r\n  \"@context\": \"https://schema.org\",\r\n  \"@type\": \"Person\",\r\n  \"name\": \"Tarcisio Bispo de Araujo\",\r\n  \"jobTitle\": \"UX/Product Designer\",\r\n  \"description\": \"UX/Product Designer com foco em estratégia, impacto e experiência do usuário. Especialista em design thinking, prototipagem e pesquisa de usuário.\",\r\n  \"url\": \"https://tarcisiobispo.github.io/portfolio/\",\r\n  \"image\": \"https://tarcisiobispo.github.io/portfolio/profile-image.jpg\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"knowsAbout\": [\r\n    \"UX Design\",\r\n    \"Product Design\",\r\n    \"UI Design\",\r\n    \"Design Thinking\",\r\n    \"Prototipagem\",\r\n    \"Pesquisa de Usuário\",\r\n    \"Figma\",\r\n    \"Adobe Creative Suite\",\r\n    \"Design Systems\"\r\n  ],\r\n  \"sameAs\": [\r\n    \"https://www.linkedin.com/in/tarcisio-bispo\",\r\n    \"https://github.com/tarcisiobispo\",\r\n    \"https://behance.net/tarcisiobispo\"\r\n  ],\r\n  \"worksFor\": {\r\n    \"@type\": \"Organization\",\r\n    \"name\": \"Freelancer\"\r\n  },\r\n  \"address\": {\r\n    \"@type\": \"PostalAddress\",\r\n    \"addressLocality\": \"Brasil\",\r\n    \"addressCountry\": \"BR\"\r\n  }\r\n};\r\n\r\nexport const websiteSchema = {\r\n  \"@context\": \"https://schema.org\",\r\n  \"@type\": \"WebSite\",\r\n  \"name\": \"Portfólio de Tarcisio Bispo\",\r\n  \"description\": \"Portfólio profissional de Tarcisio Bispo de Araujo - UX/Product Designer\",\r\n  \"url\": \"https://tarcisiobispo.github.io/portfolio/\",\r\n  \"author\": {\r\n    \"@type\": \"Person\",\r\n    \"name\": \"Tarcisio Bispo de Araujo\"\r\n  },\r\n  \"inLanguage\": \"pt-BR\",\r\n  \"copyrightYear\": \"2024\",\r\n  \"copyrightHolder\": {\r\n    \"@type\": \"Person\",\r\n    \"name\": \"Tarcisio Bispo de Araujo\"\r\n  },\r\n  \"potentialAction\": {\r\n    \"@type\": \"SearchAction\",\r\n    \"target\": \"https://tarcisiobispo.github.io/portfolio/?search={search_term_string}\",\r\n    \"query-input\": \"required name=search_term_string\"\r\n  }\r\n};\r\n\r\nexport const portfolioSchema = {\r\n  \"@context\": \"https://schema.org\",\r\n  \"@type\": \"CreativeWork\",\r\n  \"name\": \"Portfólio UX/Product Design\",\r\n  \"description\": \"Coleção de projetos de UX/Product Design desenvolvidos por Tarcisio Bispo\",\r\n  \"creator\": {\r\n    \"@type\": \"Person\",\r\n    \"name\": \"Tarcisio Bispo de Araujo\"\r\n  },\r\n  \"url\": \"https://tarcisiobispo.github.io/portfolio/\",\r\n  \"dateCreated\": \"2024\",\r\n  \"inLanguage\": \"pt-BR\",\r\n  \"genre\": \"Portfolio\",\r\n  \"keywords\": \"UX Design, Product Design, Portfolio, Design Thinking\"\r\n};\r\n\r\nexport const breadcrumbSchema = {\r\n  \"@context\": \"https://schema.org\",\r\n  \"@type\": \"BreadcrumbList\",\r\n  \"itemListElement\": [\r\n    {\r\n      \"@type\": \"ListItem\",\r\n      \"position\": 1,\r\n      \"name\": \"Home\",\r\n      \"item\": \"https://tarcisiobispo.github.io/portfolio/\"\r\n    },\r\n    {\r\n      \"@type\": \"ListItem\",\r\n      \"position\": 2,\r\n      \"name\": \"Projetos\",\r\n      \"item\": \"https://tarcisiobispo.github.io/portfolio/#projetos\"\r\n    },\r\n    {\r\n      \"@type\": \"ListItem\",\r\n      \"position\": 3,\r\n      \"name\": \"Contato\",\r\n      \"item\": \"https://tarcisiobispo.github.io/portfolio/#contato\"\r\n    }\r\n  ]\r\n};\r\n\r\n// Function to generate project schema\r\nexport const generateProjectSchema = (project: {\r\n  name: string;\r\n  description: string;\r\n  url?: string;\r\n  image?: string;\r\n  dateCreated?: string;\r\n  technologies?: string[];\r\n}) => ({\r\n  \"@context\": \"https://schema.org\",\r\n  \"@type\": \"CreativeWork\",\r\n  \"name\": project.name,\r\n  \"description\": project.description,\r\n  \"creator\": {\r\n    \"@type\": \"Person\",\r\n    \"name\": \"Tarcisio Bispo de Araujo\"\r\n  },\r\n  \"url\": project.url || \"https://tarcisiobispo.github.io/portfolio/#projetos\",\r\n  \"image\": project.image,\r\n  \"dateCreated\": project.dateCreated || \"2024\",\r\n  \"inLanguage\": \"pt-BR\",\r\n  \"genre\": \"UX/Product Design Project\",\r\n  \"keywords\": project.technologies?.join(\", \") || \"UX Design, Product Design\"\r\n});\r\n\r\n// Combined schema for the main page\r\nexport const mainPageSchema = {\r\n  \"@context\": \"https://schema.org\",\r\n  \"@graph\": [\r\n    personSchema,\r\n    websiteSchema,\r\n    portfolioSchema,\r\n    breadcrumbSchema\r\n  ]\r\n};\r\n", "import React, { Suspense, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport Profile from '../components/Profile';\r\nimport SkipLink from '@/components/SkipLink';\r\nimport SEO from '@/components/SEO';\r\nimport LoadingSpinner from '@/components/LoadingSpinner';\r\nimport { mainPageSchema } from '@/utils/structuredData';\r\nimport { usePrefetch, useImagePrefetch } from '@/hooks/usePrefetch';\r\nimport { usePageTracking } from '@/hooks/useAnalytics';\r\n\r\n// Lazy loading dos componentes pesados para melhor performance\r\nconst ProjectShowcase = React.lazy(() => import('../components/ProjectShowcase'));\r\nconst BacklogCycle = React.lazy(() => import('../components/BacklogCycle'));\r\nconst Contact = React.lazy(() => import('../components/Contact'));\r\nconst Footer = React.lazy(() => import('@/components/Footer'));\r\n\r\nconst Index = () => {\r\n  const { trackPageView } = usePageTracking();\r\n\r\n  // Track page view on component mount\r\n  useEffect(() => {\r\n    trackPageView();\r\n  }, [trackPageView]);\r\n\r\n  const profileData = {\r\n    name: \"Tarcisio Bispo de <PERSON>ujo\"\r\n  };\r\n\r\n  const projects = [\r\n    {\r\n      projectKey: \"fgvLaw\",\r\n      imageUrl: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=600&q=75&fm=webp\"\r\n    },\r\n    {\r\n      projectKey: \"direitoGV\",\r\n      imageUrl: \"https://images.unsplash.com/photo-1481627834876-b7833e8f5570?auto=format&fit=crop&w=600&q=75&fm=webp\"\r\n    },\r\n    {\r\n      projectKey: \"taliparts\",\r\n      imageUrl: \"https://images.unsplash.com/photo-1605810230434-7631ac76ec81?auto=format&fit=crop&w=600&q=75&fm=webp\"\r\n    },\r\n    {\r\n      projectKey: \"tvInstitucional\",\r\n      imageUrl: \"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=600&q=75&fm=webp\"\r\n    }\r\n  ];\r\n\r\n  // Prefetch de imagens para performance\r\n  const imageSrcs = projects.map(project => project.imageUrl);\r\n  // SPA routes are handled by React Router and don't need prefetching\r\n  usePrefetch({\r\n    routes: [], // Only static files that exist in dist folder\r\n    delay: 3000\r\n  });\r\n  useImagePrefetch(imageSrcs);\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\">\r\n    <SEO\r\n      title=\"Tarcisio Bispo | UX/Product Designer\"\r\n      description=\"Portfólio de Tarcisio Bispo de Araujo - UX/Product Designer com foco em estratégia, impacto e experiência do usuário. Especialista em design thinking, prototipagem e pesquisa de usuário.\"\r\n      keywords=\"UX Designer, Product Designer, UI/UX, Design Thinking, Prototipagem, Pesquisa de Usuário, Portfolio, Tarcisio Bispo\"\r\n      structuredData={mainPageSchema}\r\n    />\r\n    <SkipLink />\r\n    <main id=\"main-content\" className=\"flex-1 w-full relative transition-colors duration-300\">\r\n      {/* Hero Section */}\r\n      <div id=\"perfil\" className=\"relative overflow-hidden\">\r\n\r\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          {/* Profile Section */}\r\n          <Profile {...profileData} />\r\n\r\n\r\n        </div>\r\n      </div>\r\n\r\n      {/* Projects Section */}\r\n      <section id=\"projetos\" className=\"py-12 relative\" aria-labelledby=\"projects-heading\">\r\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.8 }}\r\n          >\r\n            <h2 id=\"projects-heading\" className=\"sr-only\">Projetos de UX Design</h2>\r\n            <Suspense fallback={<LoadingSpinner />}>\r\n              <ProjectShowcase projects={projects} />\r\n            </Suspense>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Backlog Cycle Section */}\r\n      <section id=\"backlog\" className=\"py-12 relative transition-colors duration-300\" aria-labelledby=\"backlog-heading\">\r\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.8 }}\r\n          >\r\n            <h2 id=\"backlog-heading\" className=\"sr-only\">Backlog Estratégico</h2>\r\n            <Suspense fallback={<LoadingSpinner />}>\r\n              <BacklogCycle />\r\n            </Suspense>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section id=\"contato\" className=\"py-12 relative\" aria-labelledby=\"contact-heading\">\r\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.8 }}\r\n          >\r\n            <h2 id=\"contact-heading\" className=\"sr-only\">Contato</h2>\r\n            <Suspense fallback={<LoadingSpinner />}>\r\n              <Contact />\r\n            </Suspense>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n    </main>\r\n\r\n    <Suspense fallback={<LoadingSpinner />}>\r\n      <Footer />\r\n    </Suspense>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Index;\r\n", "import { useCallback } from 'react';\r\nimport { useLocation } from 'react-router-dom';\r\nimport { ANALYTICS_CONFIG, PORTFOLIO_EVENTS, GTMEvent } from '@/config/analytics';\r\nimport { SecureValidation } from '@/utils/secureValidation';\r\nimport { secureLogRocket } from '@/utils/secureLogRocket';\r\n\r\n// Hook para analytics do portfolio\r\nexport const useAnalytics = () => {\r\n  // Usar try/catch para evitar erro quando fora do Router context\r\n  let location;\r\n  try {\r\n    location = useLocation();\r\n  } catch {\r\n    location = { pathname: '/' };\r\n  }\r\n\r\n  // Função para enviar eventos ao GTM\r\n  const sendEvent = useCallback((event: GTMEvent) => {\r\n    if (!ANALYTICS_CONFIG.ENABLED || typeof window === 'undefined') {\r\n      return;\r\n    }\r\n\r\n    // Enviar para dataLayer\r\n    window.dataLayer = window.dataLayer || [];\r\n    window.dataLayer.push({\r\n      ...event,\r\n      page_location: window.location.href,\r\n      page_path: location.pathname,\r\n      timestamp: new Date().toISOString(),\r\n    });\r\n\r\n    // Log em desenvolvimento\r\n    if (import.meta.env.DEV) {\r\n      console.log('📊 Analytics Event:', event);\r\n    }\r\n  }, [location.pathname]);\r\n\r\n  // Tracking de navegação\r\n  const trackNavigation = useCallback((section: string) => {\r\n    sendEvent(PORTFOLIO_EVENTS.navigateToSection(section));\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de projetos\r\n  const trackProjectView = useCallback((projectName: string) => {\r\n    sendEvent(PORTFOLIO_EVENTS.viewProject(projectName));\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de formulário de contato\r\n  const trackContactForm = useCallback((formType: string = 'contact') => {\r\n    sendEvent(PORTFOLIO_EVENTS.submitContactForm(formType));\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de download de CV\r\n  const trackCVDownload = useCallback(() => {\r\n    sendEvent(PORTFOLIO_EVENTS.downloadCV());\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de redes sociais\r\n  const trackSocialClick = useCallback((platform: string) => {\r\n    sendEvent(PORTFOLIO_EVENTS.clickSocialMedia(platform));\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de acessibilidade\r\n  const trackAccessibilityToggle = useCallback((feature: string, enabled: boolean) => {\r\n    sendEvent(PORTFOLIO_EVENTS.toggleAccessibility(feature, enabled));\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de mudança de tema\r\n  const trackThemeChange = useCallback((theme: string) => {\r\n    sendEvent(PORTFOLIO_EVENTS.changeTheme(theme));\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de mudança de idioma\r\n  const trackLanguageChange = useCallback((language: string) => {\r\n    sendEvent(PORTFOLIO_EVENTS.changeLanguage(language));\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de cliques em botões\r\n  const trackButtonClick = useCallback((buttonName: string, section?: string) => {\r\n    sendEvent({\r\n      event: ANALYTICS_CONFIG.EVENTS.BUTTON_CLICK,\r\n      event_category: ANALYTICS_CONFIG.CATEGORIES.ENGAGEMENT,\r\n      event_label: buttonName,\r\n      custom_parameters: {\r\n        section: section || 'unknown',\r\n        button_type: 'cta',\r\n      },\r\n    });\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de links externos com validação segura\r\n  const trackExternalLink = useCallback((url: string, linkText?: string) => {\r\n    // Usar validação segura\r\n    if (!SecureValidation.validateUrl(url)) {\r\n      console.warn('URL inválida para tracking:', url);\r\n      return;\r\n    }\r\n\r\n    const linkDomain = SecureValidation.extractDomain(url) || 'unknown';\r\n\r\n    sendEvent({\r\n      event: ANALYTICS_CONFIG.EVENTS.EXTERNAL_LINK,\r\n      event_category: ANALYTICS_CONFIG.CATEGORIES.ENGAGEMENT,\r\n      event_label: linkText || url,\r\n      custom_parameters: {\r\n        destination_url: url,\r\n        link_domain: linkDomain,\r\n      },\r\n    });\r\n  }, [sendEvent]);\r\n\r\n  // Tracking de erros\r\n  const trackError = useCallback((errorMessage: string, errorType?: string) => {\r\n    sendEvent({\r\n      event: ANALYTICS_CONFIG.EVENTS.ERROR,\r\n      event_category: ANALYTICS_CONFIG.CATEGORIES.ERROR,\r\n      event_label: errorMessage,\r\n      custom_parameters: {\r\n        error_type: errorType || 'javascript_error',\r\n        page_path: location.pathname,\r\n      },\r\n    });\r\n  }, [sendEvent, location.pathname]);\r\n\r\n  // Tracking de performance\r\n  const trackPerformance = useCallback((metricName: string, value: number, unit?: string) => {\r\n    sendEvent({\r\n      event: ANALYTICS_CONFIG.EVENTS.PERFORMANCE_METRIC,\r\n      event_category: ANALYTICS_CONFIG.CATEGORIES.PERFORMANCE,\r\n      event_label: metricName,\r\n      value: Math.round(value),\r\n      custom_parameters: {\r\n        metric_unit: unit || 'ms',\r\n        page_path: location.pathname,\r\n      },\r\n    });\r\n  }, [sendEvent, location.pathname]);\r\n\r\n  return {\r\n    // Eventos específicos do portfolio\r\n    trackNavigation,\r\n    trackProjectView,\r\n    trackContactForm,\r\n    trackCVDownload,\r\n    trackSocialClick,\r\n    trackAccessibilityToggle,\r\n    trackThemeChange,\r\n    trackLanguageChange,\r\n\r\n    // Eventos gerais\r\n    trackButtonClick,\r\n    trackExternalLink,\r\n    trackError,\r\n    trackPerformance,\r\n\r\n    // Função genérica\r\n    sendEvent,\r\n  };\r\n};\r\n\r\n// Hook para tracking automático de page views\r\nexport const usePageTracking = () => {\r\n  // Usar try/catch para evitar erro quando fora do Router context\r\n  let location;\r\n  try {\r\n    location = useLocation();\r\n  } catch {\r\n    location = { pathname: '/' };\r\n  }\r\n\r\n  const trackPageView = useCallback(() => {\r\n    if (!ANALYTICS_CONFIG.ENABLED || typeof window === 'undefined') {\r\n      return;\r\n    }\r\n\r\n    // Enviar page view para GTM\r\n    window.dataLayer = window.dataLayer || [];\r\n    window.dataLayer.push({\r\n      event: ANALYTICS_CONFIG.EVENTS.PAGE_VIEW,\r\n      page_path: location.pathname,\r\n      page_title: document.title,\r\n      page_location: window.location.href,\r\n      timestamp: new Date().toISOString(),\r\n    });\r\n\r\n    // Log em desenvolvimento\r\n    if (import.meta.env.DEV) {\r\n      console.log('📄 Page View:', location.pathname);\r\n    }\r\n  }, [location.pathname]);\r\n\r\n  return { trackPageView };\r\n};\r\n", "import { useEffect } from 'react';\r\n\r\ninterface PrefetchOptions {\r\n  routes?: string[];\r\n  delay?: number;\r\n  onHover?: boolean;\r\n}\r\n\r\nexport const usePrefetch = ({\r\n  routes = [],\r\n  delay = 2000,\r\n  onHover = true\r\n}: PrefetchOptions = {}) => {\r\n\r\n  useEffect(() => {\r\n    // Prefetch automático após delay\r\n    const timer = setTimeout(() => {\r\n      routes.forEach(route => {\r\n        prefetchRoute(route);\r\n      });\r\n    }, delay);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [routes, delay]);\r\n\r\n  const prefetchRoute = (route: string) => {\r\n    // Skip prefetch in development to avoid 404 errors\r\n    if (!import.meta.env.PROD) {\r\n      return;\r\n    }\r\n\r\n    // Criar link element para prefetch\r\n    const link = document.createElement('link');\r\n    link.rel = 'prefetch';\r\n    link.href = route;\r\n    link.as = 'document';\r\n\r\n    // Add error handling\r\n    link.onerror = () => {\r\n      console.warn(`Failed to prefetch route: ${route}`);\r\n    };\r\n\r\n    // Verificar se já não existe\r\n    const existingLink = document.querySelector(`link[href=\"${route}\"]`);\r\n    if (!existingLink) {\r\n      document.head.appendChild(link);\r\n    }\r\n  };\r\n\r\n  const prefetchOnHover = (route: string) => {\r\n    if (onHover) {\r\n      prefetchRoute(route);\r\n    }\r\n  };\r\n\r\n  // Prefetch de recursos críticos\r\n  const prefetchCriticalResources = () => {\r\n    // Only prefetch static files that actually exist in dist folder\r\n    // SPA routes like /privacy-policy are handled by React Router and don't need prefetching\r\n    const baseUrl = import.meta.env.BASE_URL;\r\n    const criticalRoutes: string[] = [\r\n      // Add only static files that exist in dist folder\r\n      // SPA routes are handled by React Router\r\n    ];\r\n\r\n    criticalRoutes.forEach(route => {\r\n      prefetchRoute(route);\r\n    });\r\n  };\r\n\r\n  return {\r\n    prefetchRoute,\r\n    prefetchOnHover,\r\n    prefetchCriticalResources\r\n  };\r\n};\r\n\r\n// Hook para prefetch de imagens\r\nexport const useImagePrefetch = (imageSrcs: string[]) => {\r\n  useEffect(() => {\r\n    const prefetchImages = () => {\r\n      imageSrcs.forEach(src => {\r\n        const link = document.createElement('link');\r\n        link.rel = 'prefetch';\r\n        link.href = src;\r\n        link.as = 'image';\r\n\r\n        const existingLink = document.querySelector(`link[href=\"${src}\"]`);\r\n        if (!existingLink) {\r\n          document.head.appendChild(link);\r\n        }\r\n      });\r\n    };\r\n\r\n    // Prefetch após um pequeno delay\r\n    const timer = setTimeout(prefetchImages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [imageSrcs]);\r\n};\r\n\r\n// Hook para intersection observer (lazy loading avançado)\r\nexport const useIntersectionObserver = (\r\n  callback: (entries: IntersectionObserverEntry[]) => void,\r\n  options: IntersectionObserverInit = {}\r\n) => {\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(callback, {\r\n      rootMargin: '50px',\r\n      threshold: 0.1,\r\n      ...options\r\n    });\r\n\r\n    return () => observer.disconnect();\r\n  }, [callback, options]);\r\n};\r\n"], "file": "js/Index-Cy6sF_0Y.js"}