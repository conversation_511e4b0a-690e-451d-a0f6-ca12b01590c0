import{j as e,m as a}from"./chunk-L3uak9dD.js";import{r}from"./chunk-D3Ns84uO.js";import{b as s,C as t}from"./chunk-CMTALxe4.js";import{S as o}from"./chunk-8nFA7SZG.js";import{u as i}from"./chunk-CS50z45r.js";import{U as n,X as l,q as c,r as m,M as d,s as u,t as h,u as g}from"./chunk-Bc9oLI49.js";import"./chunk-DJqUFuPP.js";import"./chunk-Bt4ub4Kz.js";import"./index-Dn5J_98a.js";import"./chunk-DylKGAq6.js";const x={_origin:"https://api.emailjs.com"},p=(e,a,r)=>{if(!e)throw"The user ID is required. Visit https://dashboard.emailjs.com/admin/integration";if(!a)throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!r)throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates";return!0};class f{constructor(e){this.status=e.status,this.text=e.responseText}}const b=(e,a,r={})=>new Promise(((s,t)=>{const o=new XMLHttpRequest;o.addEventListener("load",(({target:e})=>{const a=new f(e);200===a.status||"OK"===a.text?s(a):t(a)})),o.addEventListener("error",(({target:e})=>{t(new f(e))})),o.open("POST",x._origin+e,!0),Object.keys(r).forEach((e=>{o.setRequestHeader(e,r[e])})),o.send(a)})),v=(e,a,r,s)=>{const t=s||x._userID;p(t,e,a);const o={lib_version:"3.2.0",user_id:t,service_id:e,template_id:a,template_params:r};return b("/api/v1.0/email/send",JSON.stringify(o),{"Content-type":"application/json"})},j=()=>{const{t:x,i18n:p}=i(),{playSubmitSuccess:f,playSubmitError:b,playFieldFocus:j}=s(),[y,N]=r.useState({name:"",email:"",message:""}),[E,k]=r.useState({}),[w,S]=r.useState({name:!1,email:!1,message:!1}),[C,_]=r.useState(!1),[O,q]=r.useState("idle"),M=r.useRef(null),V=r.useRef(null),F=r.useRef(null);r.useEffect((()=>{if("success"===O){const e=setTimeout((()=>{q("idle")}),4e3);return()=>clearTimeout(e)}}),[O]);const T=r.useCallback(((e,a)=>{try{const s=(null==p?void 0:p.language)||"pt-BR",t=a||"";switch(e){case"name":return t.trim()?t.trim().length<2?"en-US"===s?"Name must be at least 2 characters":"es-ES"===s?"El nombre debe tener al menos 2 caracteres":"Nome deve ter pelo menos 2 caracteres":void 0:"en-US"===s?"Name is required":"es-ES"===s?"El nombre es obligatorio":"Nome é obrigatório";case"email":return t.trim()?(r=t,o.validateEmail(r)?void 0:"en-US"===s?"Invalid email":"es-ES"===s?"Email inválido":"E-mail inválido"):"en-US"===s?"Email is required":"es-ES"===s?"El email es obligatorio":"E-mail é obrigatório";case"message":return t.trim()?t.trim().length<10?"en-US"===s?"Message must be at least 10 characters":"es-ES"===s?"El mensaje debe tener al menos 10 caracteres":"Mensagem deve ter pelo menos 10 caracteres":void 0:"en-US"===s?"Message is required":"es-ES"===s?"El mensaje es obligatorio":"Mensagem é obrigatória";default:return}}catch(s){return console.error("Erro na validação do campo:",s),"Erro na validação"}var r}),[null==p?void 0:p.language]),U=r.useCallback((e=>{try{const{name:a,value:r}=e.target,s=a;if(!a||!Object.keys(y).includes(a))return;if(N((e=>({...e,[a]:r}))),w[s]){const e=T(s,r||"");k((r=>({...r,[a]:e})))}}catch(a){console.error("Erro no handleChange:",a)}}),[y,w,T]),P=r.useCallback((e=>{try{const{name:a,value:r}=e.target,s=a;if(!a||!Object.keys(y).includes(a))return;S((e=>({...e,[a]:!0})));const t=T(s,r||"");k((e=>({...e,[a]:t})))}catch(a){console.error("Erro no handleBlur:",a)}}),[y,T]),R=async e=>{var a,r,s;if(e.preventDefault(),S({name:!0,email:!0,message:!0}),(()=>{const e={};return Object.keys(y).forEach((a=>{const r=a,s=T(r,y[r]);s&&(e[r]=s)})),k(e),0===Object.keys(e).length})()){_(!0),q("idle");try{await v("service_4z3a60b","template_nc73bg4",{from_name:y.name,from_email:y.email,message:y.message,to_email:"<EMAIL>",subject:`Nova mensagem de contato de ${y.name}`,reply_to:y.email},"eRzZy4gTZ2NXGjFKz"),q("success"),N({name:"",email:"",message:""}),S({name:!1,email:!1,message:!1}),k({}),f()}catch(t){console.error("Erro ao enviar e-mail:",t),q("error"),b()}finally{_(!1)}}else{const e={};Object.keys(y).forEach((a=>{const r=a,s=T(r,y[r]);s&&(e[r]=s)}));const t=Object.keys(e)[0];"name"===t?null==(a=M.current)||a.focus():"email"===t?null==(r=V.current)||r.focus():"message"===t&&(null==(s=F.current)||s.focus())}},I=r.useMemo((()=>{const e={name:{isValid:!1,hasError:!1},email:{isValid:!1,hasError:!1},message:{isValid:!1,hasError:!1}};return Object.keys(y).forEach((a=>{const r=a;try{const a=w[r]&&!!E[r],s=w[r]&&y[r]&&!T(r,y[r]);e[r]={isValid:s,hasError:a}}catch(s){console.error(`Erro ao verificar estado do campo ${r}:`,s),e[r]={isValid:!1,hasError:!1}}})),e}),[y,w,E,T]),z=r.useCallback((e=>{var a;return(null==(a=I[e])?void 0:a.isValid)||!1}),[I]),B=r.useCallback((e=>{var a;return(null==(a=I[e])?void 0:a.hasError)||!1}),[I]);return e.jsxs("section",{id:"contact",className:"py-8",children:[e.jsx(a.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},className:"mb-12",children:e.jsxs("div",{className:"max-w-2xl mx-auto text-left px-4 sm:px-6 lg:px-8",children:[e.jsx("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-text)] mb-4",children:x("contact.title")}),e.jsx("p",{className:"text-[var(--color-muted)] text-lg mb-4",children:x("contact.description")}),e.jsx(a.div,{initial:{width:0},animate:{width:"120px"},transition:{duration:.8,delay:.6},className:"h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"})]})}),e.jsx(a.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2,ease:"easeOut"},className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("form",{onSubmit:R,className:"space-y-6",noValidate:!0,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{htmlFor:"name",className:"block text-sm font-semibold text-[var(--color-text)]",children:[e.jsx(n,{className:"inline w-4 h-4 mr-2","aria-hidden":"true"}),x("contact.form.name"),e.jsx("span",{className:"text-[var(--color-error)] ml-1","aria-label":x("contact.form.required"),children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{ref:M,type:"text",id:"name",name:"name",value:y.name,onChange:U,onBlur:P,onFocus:()=>j(),className:"w-full px-4 py-3 rounded-lg border-2 transition-all duration-300 bg-[var(--color-surface)] text-[var(--color-text)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent "+(B("name")?"border-red-500 focus:ring-red-500":z("name")?"border-green-500 focus:ring-green-500":"border-[var(--color-border)] hover:border-[var(--color-primary)]/50"),placeholder:x("contact.form.namePlaceholder"),"aria-invalid":B("name")?"true":"false","aria-describedby":B("name")?"name-error":void 0,autoComplete:"name"}),B("name")&&e.jsx(l,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500","aria-hidden":"true"}),z("name")&&e.jsx(c,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500","aria-hidden":"true"})]}),E.name&&w.name&&e.jsxs("p",{id:"name-error",className:"text-sm text-[var(--color-error)] flex items-center gap-1",role:"alert",children:[e.jsx(m,{className:"w-4 h-4","aria-hidden":"true"}),E.name]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{htmlFor:"email",className:"block text-sm font-semibold text-[var(--color-text)]",children:[e.jsx(d,{className:"inline w-4 h-4 mr-2","aria-hidden":"true"}),x("contact.form.email"),e.jsx("span",{className:"text-[var(--color-error)] ml-1","aria-label":x("contact.form.required"),children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{ref:V,type:"email",id:"email",name:"email",value:y.email,onChange:U,onBlur:P,onFocus:()=>j(),className:"w-full px-4 py-3 rounded-lg border-2 transition-all duration-300 bg-[var(--color-surface)] text-[var(--color-text)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent "+(B("email")?"border-red-500 focus:ring-red-500":z("email")?"border-green-500 focus:ring-green-500":"border-[var(--color-border)] hover:border-[var(--color-primary)]/50"),placeholder:x("contact.form.emailPlaceholder"),"aria-invalid":B("email")?"true":"false","aria-describedby":B("email")?"email-error":void 0,autoComplete:"email",inputMode:"email"}),B("email")&&e.jsx(l,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500","aria-hidden":"true"}),z("email")&&e.jsx(c,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500","aria-hidden":"true"})]}),E.email&&w.email&&e.jsxs("p",{id:"email-error",className:"text-sm text-[var(--color-error)] flex items-center gap-1",role:"alert",children:[e.jsx(m,{className:"w-4 h-4","aria-hidden":"true"}),E.email]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{htmlFor:"message",className:"block text-sm font-semibold text-[var(--color-text)]",children:[e.jsx(u,{className:"inline w-4 h-4 mr-2","aria-hidden":"true"}),x("contact.form.message"),e.jsx("span",{className:"text-[var(--color-error)] ml-1","aria-label":x("contact.form.required"),children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("textarea",{ref:F,id:"message",name:"message",value:y.message,onChange:U,onBlur:P,onFocus:()=>j(),rows:5,className:"w-full px-4 py-3 rounded-lg border-2 transition-all duration-300 bg-[var(--color-surface)] text-[var(--color-text)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent resize-vertical "+(B("message")?"border-red-500 focus:ring-red-500":z("message")?"border-green-500 focus:ring-green-500":"border-[var(--color-border)] hover:border-[var(--color-primary)]/50"),placeholder:x("contact.form.messagePlaceholder"),"aria-invalid":B("message")?"true":"false","aria-describedby":B("message")?"message-error":"message-hint",minLength:10}),B("message")&&e.jsx(l,{className:"absolute right-3 top-3 w-5 h-5 text-red-500","aria-hidden":"true"}),z("message")&&e.jsx(c,{className:"absolute right-3 top-3 w-5 h-5 text-green-500","aria-hidden":"true"})]}),E.message&&w.message?e.jsxs("p",{id:"message-error",className:"text-sm text-[var(--color-error)] flex items-center gap-1",role:"alert",children:[e.jsx(m,{className:"w-4 h-4","aria-hidden":"true"}),E.message]}):e.jsx("p",{id:"message-hint",className:"text-xs text-[var(--color-muted)]",children:x("contact.form.messageHint")})]}),e.jsxs("div",{children:[e.jsx(t,{onClick:R,variant:"primary",size:"lg",icon:h,iconPosition:"left",disabled:C,loading:C,className:"w-full",ariaLabel:x(C?"contact.form.sending":"contact.form.send"),children:x(C?"contact.form.sending":"contact.form.send")}),e.jsx("div",{className:"mt-2 text-center",children:e.jsx("p",{className:"text-sm text-[var(--color-muted)] max-w-prose mx-auto",children:x("contact.form.privacy")})})]}),"success"===O&&e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mt-4 flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-400",role:"status","aria-live":"polite",children:[e.jsx(g,{className:"w-5 h-5 flex-shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"font-medium text-left",children:"en-US"===p.language?"Message sent successfully!":"es-ES"===p.language?"¡Mensaje enviado con éxito!":"Mensagem enviada com sucesso!"})]}),"error"===O&&e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mt-4 flex items-center gap-3 p-4 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-400",role:"alert","aria-live":"assertive",children:[e.jsx(m,{className:"w-5 h-5 flex-shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"font-medium text-left",children:"en-US"===p.language?"Error sending message. Please try again.":"es-ES"===p.language?"Error al enviar mensaje. Inténtalo de nuevo.":"Erro ao enviar mensagem. Tente novamente."})]})]})})]})};export{j as default};
//# sourceMappingURL=Contact-CKvVeQiL.js.map
