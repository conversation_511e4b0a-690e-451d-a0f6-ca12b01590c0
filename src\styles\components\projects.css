/**
 * 🎯 PROJECTS SHOWCASE
 *
 * Estilos para o componente ProjectShowcase
 * Design limpo e moderno com cards expansíveis
 */

/* ===== GRID PRINCIPAL ===== */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* ===== CARD DO PROJETO ===== */
.project-card {
  background: var(--color-surface);
  border-radius: 16px;
  overflow: hidden;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-border);
  cursor: pointer;
  position: relative;
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: var(--color-primary);
}

.project-card:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ===== IMAGEM DO PROJETO ===== */
.project-card-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: var(--color-background-secondary);
}

.project-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .project-card-image {
  transform: scale(1.05);
}

/* ===== CONTEÚDO DO CARD ===== */
.project-card-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* ===== TÍTULO ===== */
.project-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-text);
  margin: 0;
  line-height: 1.3;
}

/* ===== DESCRIÇÃO ===== */
.project-card-description {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

/* ===== TAGS/BADGES ===== */
.project-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.project-card-tag {
  background: var(--color-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.project-card-tag:hover {
  background: var(--color-primary-dark);
  transform: scale(1.05);
}

/* ===== AÇÕES DO CARD ===== */
.project-card-actions {
  margin-top: auto;
  padding-top: 0.5rem;
}

.project-card-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.project-card-button:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

.project-card-button:active {
  transform: translateY(0);
}

/* ===== CONTEÚDO EXPANSÍVEL ===== */
.project-expanded-content {
  padding: 1.5rem;
  padding-top: 0;
  border-top: 1px solid var(--color-border);
  margin-top: 1rem;
}

.project-section {
  margin-bottom: 1.5rem;
}

.project-section:last-child {
  margin-bottom: 0;
}

.project-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: var(--color-primary);
  border-radius: 2px;
}

.project-section-content {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
  font-size: 0.9rem;
}

/* ===== LISTA DE RESULTADOS ===== */
.project-outcomes-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.project-outcome-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

.project-outcome-bullet {
  width: 6px;
  height: 6px;
  background: var(--color-primary);
  border-radius: 50%;
  margin-top: 0.5rem;
  flex-shrink: 0;
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 0.5rem;
  }

  .project-card-content {
    padding: 1rem;
  }

  .project-expanded-content {
    padding: 1rem;
    padding-top: 0;
  }

  .project-card-title {
    font-size: 1.1rem;
  }

  .project-card-tags {
    gap: 0.25rem;
  }

  .project-card-tag {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }
}

@media (max-width: 480px) {
  .project-card-image-container {
    height: 160px;
  }

  .project-card-content {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .project-card-button {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }
}

/* ===== MODO ESCURO ===== */
[data-theme="dark"] .project-card {
  background: var(--color-surface-dark);
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .project-card:hover {
  border-color: var(--color-primary);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .project-expanded-content {
  border-top-color: var(--color-border-dark);
}

/* ===== ACESSIBILIDADE ===== */
@media (prefers-reduced-motion: reduce) {
  .project-card,
  .project-card-image,
  .project-card-button,
  .project-card-tag {
    transition: none;
  }

  .project-card:hover {
    transform: none;
  }

  .project-card:hover .project-card-image {
    transform: none;
  }
}

/* ===== ESTADOS DE FOCO ===== */
.project-card-button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.project-card-tag:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 1px;
}
