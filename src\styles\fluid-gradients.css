/**
 * 🎯 FLUID GRADIENTS SYSTEM
 *
 * Sistema de gradientes fluidos dinâmicos baseado em scroll
 * Muda automaticamente conforme as seções do site
 */

/* ===== CONTAINER PRINCIPAL DO SISTEMA ===== */
.fluid-gradient-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.03) 0%,
    rgba(147, 197, 253, 0.02) 50%,
    rgba(219, 234, 254, 0.01) 100%
  );
}

/* ===== SEÇÃO PERFIL - AZUL PROFISSIONAL ===== */
.fluid-gradient-container.section-profile {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.08) 0%,    /* Azul principal */
    rgba(147, 197, 253, 0.05) 30%,  /* Azul claro */
    rgba(219, 234, 254, 0.03) 60%,  /* Azul muito claro */
    rgba(59, 130, 246, 0.06) 100%   /* Azul médio */
  );
  animation: fluidProfile 20s ease-in-out infinite;
}

/* ===== SEÇÃO PROJETOS - VERDE CRESCIMENTO ===== */
.fluid-gradient-container.section-projects {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.08) 0%,    /* Verde principal */
    rgba(110, 231, 183, 0.05) 30%,  /* Verde claro */
    rgba(209, 250, 229, 0.03) 60%,  /* Verde muito claro */
    rgba(16, 185, 129, 0.06) 100%   /* Verde médio */
  );
  animation: fluidProjects 18s ease-in-out infinite;
}

/* ===== SEÇÃO BACKLOG - ROXO ESTRATÉGICO ===== */
.fluid-gradient-container.section-backlog {
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.08) 0%,    /* Roxo principal */
    rgba(196, 181, 253, 0.05) 30%,  /* Roxo claro */
    rgba(237, 233, 254, 0.03) 60%,  /* Roxo muito claro */
    rgba(139, 92, 246, 0.06) 100%   /* Roxo médio */
  );
  animation: fluidBacklog 22s ease-in-out infinite;
}

/* ===== SEÇÃO CONTATO - ROSA ACOLHEDOR ===== */
.fluid-gradient-container.section-contact {
  background: linear-gradient(
    135deg,
    rgba(236, 72, 153, 0.08) 0%,    /* Rosa principal */
    rgba(251, 182, 206, 0.05) 30%,  /* Rosa claro */
    rgba(253, 242, 248, 0.03) 60%,  /* Rosa muito claro */
    rgba(236, 72, 153, 0.06) 100%   /* Rosa médio */
  );
  animation: fluidContact 16s ease-in-out infinite;
}

/* ===== ANIMAÇÕES DOS GRADIENTES ===== */
@keyframes fluidProfile {
  0%, 100% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
  25% {
    background-position: 100% 50%;
    filter: hue-rotate(5deg);
  }
  50% {
    background-position: 100% 100%;
    filter: hue-rotate(10deg);
  }
  75% {
    background-position: 0% 100%;
    filter: hue-rotate(5deg);
  }
}

@keyframes fluidProjects {
  0%, 100% {
    background-position: 0% 0%;
    filter: hue-rotate(0deg);
  }
  33% {
    background-position: 100% 0%;
    filter: hue-rotate(-5deg);
  }
  66% {
    background-position: 100% 100%;
    filter: hue-rotate(-10deg);
  }
}

@keyframes fluidBacklog {
  0%, 100% {
    background-position: 50% 0%;
    filter: hue-rotate(0deg);
  }
  50% {
    background-position: 50% 100%;
    filter: hue-rotate(15deg);
  }
}

@keyframes fluidContact {
  0%, 100% {
    background-position: 100% 50%;
    filter: hue-rotate(0deg);
  }
  25% {
    background-position: 0% 50%;
    filter: hue-rotate(-5deg);
  }
  50% {
    background-position: 0% 0%;
    filter: hue-rotate(-10deg);
  }
  75% {
    background-position: 100% 0%;
    filter: hue-rotate(-5deg);
  }
}

/* ===== GRADIENTES SUTIS ===== */
.gradient-subtle {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(139, 92, 246, 0.1) 50%,
    rgba(16, 185, 129, 0.1) 100%
  );
}

/* ===== GRADIENTES DE TEXTO ===== */
.gradient-text-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.gradient-text-secondary {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* ===== OVERLAYS ===== */
.gradient-overlay {
  position: relative;
}

.gradient-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(139, 92, 246, 0.8) 100%
  );
  z-index: 1;
}

.gradient-overlay > * {
  position: relative;
  z-index: 2;
}

/* ===== MODO ESCURO - GRADIENTES FLUIDOS ===== */
[data-theme="dark"] .fluid-gradient-container.section-profile {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.12) 0%,    /* Azul mais intenso no escuro */
    rgba(147, 197, 253, 0.08) 30%,
    rgba(219, 234, 254, 0.05) 60%,
    rgba(59, 130, 246, 0.10) 100%
  );
}

[data-theme="dark"] .fluid-gradient-container.section-projects {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.12) 0%,    /* Verde mais intenso no escuro */
    rgba(110, 231, 183, 0.08) 30%,
    rgba(209, 250, 229, 0.05) 60%,
    rgba(16, 185, 129, 0.10) 100%
  );
}

[data-theme="dark"] .fluid-gradient-container.section-backlog {
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.12) 0%,    /* Roxo mais intenso no escuro */
    rgba(196, 181, 253, 0.08) 30%,
    rgba(237, 233, 254, 0.05) 60%,
    rgba(139, 92, 246, 0.10) 100%
  );
}

[data-theme="dark"] .fluid-gradient-container.section-contact {
  background: linear-gradient(
    135deg,
    rgba(236, 72, 153, 0.12) 0%,    /* Rosa mais intenso no escuro */
    rgba(251, 182, 206, 0.08) 30%,
    rgba(253, 242, 248, 0.05) 60%,
    rgba(236, 72, 153, 0.10) 100%
  );
}

/* ===== PERFORMANCE E OTIMIZAÇÕES ===== */
.fluid-gradient-container {
  will-change: background, filter;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.fluid-gradient-container.section-profile,
.fluid-gradient-container.section-projects,
.fluid-gradient-container.section-backlog,
.fluid-gradient-container.section-contact {
  background-size: 400% 400%;
  will-change: background-position, filter;
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
  .fluid-gradient-container.section-profile {
    animation-duration: 25s;
  }

  .fluid-gradient-container.section-projects {
    animation-duration: 23s;
  }

  .fluid-gradient-container.section-backlog {
    animation-duration: 27s;
  }

  .fluid-gradient-container.section-contact {
    animation-duration: 21s;
  }
}

/* ===== ACESSIBILIDADE ===== */
@media (prefers-reduced-motion: reduce) {
  .fluid-gradient-container.section-profile,
  .fluid-gradient-container.section-projects,
  .fluid-gradient-container.section-backlog,
  .fluid-gradient-container.section-contact {
    animation: none;
  }

  .fluid-gradient-container {
    transition: background 0.3s ease;
  }
}

/* ===== COMPATIBILIDADE ===== */
@supports not (backdrop-filter: blur(10px)) {
  .fluid-gradient-container {
    opacity: 0.8;
  }
}
