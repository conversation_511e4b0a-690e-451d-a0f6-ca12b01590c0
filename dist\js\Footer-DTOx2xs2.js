import{j as t,m as e}from"./chunk-L3uak9dD.js";import{u as s}from"./chunk-CS50z45r.js";import"./chunk-D3Ns84uO.js";import"./chunk-DJqUFuPP.js";const i=()=>{const{t:i}=s();return t.jsx("footer",{className:"w-full flex items-center justify-center min-h-[200px]",children:t.jsx(e.div,{initial:{opacity:0},whileInView:{opacity:1},viewport:{once:!0},transition:{duration:.6},className:"text-center",children:t.jsxs("div",{className:"space-y-2 text-sm text-gray-700 dark:text-gray-300",children:[t.jsx("p",{children:i("footer.copyright")}),t.jsx("p",{className:"font-medium",children:i("footer.title")})]})})})};export{i as default};
//# sourceMappingURL=Footer-DTOx2xs2.js.map
