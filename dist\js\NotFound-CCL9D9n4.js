import{j as r,m as e}from"./chunk-L3uak9dD.js";import{u as o,r as a,L as t}from"./chunk-D3Ns84uO.js";import{C as s}from"./chunk-CMTALxe4.js";import{H as i,A as n,c}from"./chunk-Bc9oLI49.js";import"./chunk-DJqUFuPP.js";import"./chunk-Bt4ub4Kz.js";import"./index-Dn5J_98a.js";import"./chunk-DylKGAq6.js";import"./chunk-CS50z45r.js";const l=()=>{const l=o();a.useEffect((()=>{console.error("404 Error: User attempted to access non-existent route:",l.pathname)}),[l.pathname]);const d={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden",children:r.jsxs(e.div,{className:"text-center px-4 relative z-10",variants:{hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",children:[r.jsxs(e.div,{variants:d,className:"mb-8",children:[r.jsx(e.div,{className:"text-9xl font-bold text-[var(--color-primary)] mb-4",animate:{scale:[1,1.05,1],rotate:[0,1,-1,0]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},children:"404"}),r.jsx("div",{className:"w-32 h-1 bg-[var(--color-primary)] mx-auto rounded-full"})]}),r.jsxs(e.div,{variants:d,children:[r.jsx("h1",{className:"text-3xl md:text-4xl font-bold mb-4 text-[var(--color-text)]",children:"Página não encontrada"}),r.jsx("p",{className:"text-[var(--color-muted)] mb-8 max-w-md mx-auto text-lg",children:"Ops! A página que você está procurando não existe ou foi movida para outro local."})]}),r.jsxs(e.div,{variants:d,className:"mb-8",children:[r.jsx("div",{className:"text-6xl mb-4",children:"🔍"}),r.jsxs("p",{className:"text-sm text-[var(--color-muted)]",children:["Caminho tentado: ",r.jsx("code",{className:"bg-[var(--color-surface)] px-2 py-1 rounded text-xs",children:l.pathname})]})]}),r.jsxs(e.div,{variants:d,className:"flex flex-col sm:flex-row gap-4 justify-center",children:[r.jsx(t,{to:"/",children:r.jsx(s,{variant:"primary",icon:i,iconPosition:"left",className:"min-w-[160px]",children:"Voltar ao Início"})}),r.jsx(s,{onClick:()=>window.history.back(),variant:"ghost",icon:n,iconPosition:"left",className:"min-w-[160px]",children:"Página Anterior"}),r.jsx(s,{onClick:()=>window.location.reload(),variant:"ghost",icon:c,iconPosition:"left",className:"min-w-[160px]",children:"Recarregar"})]}),r.jsxs(e.div,{variants:d,className:"mt-12 pt-8 border-t border-[var(--color-border)]",children:[r.jsx("p",{className:"text-sm text-[var(--color-muted)] mb-4",children:"Talvez você esteja procurando por:"}),r.jsxs("div",{className:"flex flex-wrap gap-2 justify-center",children:[r.jsx(t,{to:"/#projetos",className:"text-sm text-[var(--color-primary)] hover:underline px-3 py-1 rounded-full bg-[var(--color-surface)] hover:bg-[var(--color-border)] transition-colors",children:"Projetos"}),r.jsx(t,{to:"/#backlog",className:"text-sm text-[var(--color-primary)] hover:underline px-3 py-1 rounded-full bg-[var(--color-surface)] hover:bg-[var(--color-border)] transition-colors",children:"Backlogs Estratégicos"}),r.jsx(t,{to:"/#contato",className:"text-sm text-[var(--color-primary)] hover:underline px-3 py-1 rounded-full bg-[var(--color-surface)] hover:bg-[var(--color-border)] transition-colors",children:"Contato"}),r.jsx(t,{to:"/privacy-policy",className:"text-sm text-[var(--color-primary)] hover:underline px-3 py-1 rounded-full bg-[var(--color-surface)] hover:bg-[var(--color-border)] transition-colors",children:"Política de Privacidade"})]})]})]})})};export{l as default};
//# sourceMappingURL=NotFound-CCL9D9n4.js.map
